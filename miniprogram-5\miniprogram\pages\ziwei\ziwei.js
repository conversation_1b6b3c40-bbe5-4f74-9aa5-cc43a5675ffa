// pages/ziwei/ziwei.js - 紫微斗数页面
const app = getApp();

import {
  calculateZiweiChart,
  formatZiweiChart,
  TWELVE_PALACES
} from '../../utils/ziwei-calculator.js';

import {
  comprehensiveZiweiAnalysis
} from '../../utils/ziwei-analysis.js';

import {
  generateZiweiAnalysis
} from '../../utils/question-analysis.js';

import {
  analyzeZiweiWithAI
} from '../../utils/ai-service.js';

import {
  getCityList,
  getCityCoordinates,
  calculateTrueSolarTime,
  formatSolarTimeExplanation,
  shouldUseTrueSolarTime,
  getTimeHour
} from '../../utils/solar-time.js';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 输入信息
    question: '',
    birthDate: '',
    birthTime: '',
    isMale: true,

    // 出生地信息
    cityList: [],
    selectedCity: '',
    selectedCityIndex: 0,

    // 真太阳时信息
    useTrueSolarTime: false,
    solarTimeResult: null,
    solarTimeExplanation: '',
    trueSolarTimeString: '',

    // 紫微斗数信息
    ziweiData: null,
    formattedChart: null,

    // 分析结果
    analysis: null,
    customAnalysis: null,

    // 界面状态
    isAnalyzing: false,
    showResult: false,

    // 命盘显示
    palaceList: TWELVE_PALACES
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认值
    const now = new Date();
    const cityList = getCityList();
    this.setData({
      birthDate: now.toISOString().split('T')[0],
      birthTime: '12:00',
      cityList: cityList,
      selectedCity: '北京',
      selectedCityIndex: 0
    });
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 选择出生日期
  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    });
  },

  // 选择出生时间
  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    });
    this.calculateSolarTime();
  },

  // 选择出生地
  onCityChange(e) {
    const index = e.detail.value;
    const city = this.data.cityList[index];
    this.setData({
      selectedCityIndex: index,
      selectedCity: city.name
    });
    this.calculateSolarTime();
  },

  // 计算真太阳时
  calculateSolarTime() {
    const { birthDate, birthTime, selectedCity } = this.data;

    if (!birthDate || !birthTime || !selectedCity) {
      return;
    }

    // 构建出生时间
    const birthDateTime = new Date(`${birthDate}T${birthTime}:00`);

    // 获取城市坐标
    const coordinates = getCityCoordinates(selectedCity);
    if (!coordinates) {
      return;
    }

    // 计算真太阳时
    const solarTimeResult = calculateTrueSolarTime(birthDateTime, coordinates.longitude);
    const shouldUse = shouldUseTrueSolarTime(solarTimeResult);
    const explanation = formatSolarTimeExplanation(solarTimeResult);

    // 格式化真太阳时字符串
    const trueSolarTime = solarTimeResult.trueSolarTime;
    const trueSolarTimeString = `${trueSolarTime.getHours().toString().padStart(2, '0')}:${trueSolarTime.getMinutes().toString().padStart(2, '0')}`;

    this.setData({
      solarTimeResult: solarTimeResult,
      useTrueSolarTime: shouldUse,
      solarTimeExplanation: explanation,
      trueSolarTimeString: trueSolarTimeString
    });
  },

  // 选择性别
  onGenderChange(e) {
    this.setData({
      isMale: e.detail.value === '男'
    });
  },

  // 开始排盘
  onStartAnalysis() {
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的问题',
        icon: 'none'
      });
      return;
    }

    if (!this.data.birthDate || !this.data.birthTime) {
      wx.showToast({
        title: '请选择出生日期和时间',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isAnalyzing: true,
      showResult: false
    });

    // 计算紫微斗数
    this.calculateZiweiChart();
  },

  // 计算紫微斗数排盘
  calculateZiweiChart() {
    try {
      // 确定使用的时间（真太阳时或北京时间）
      let calculationTime;
      if (this.data.useTrueSolarTime && this.data.solarTimeResult) {
        calculationTime = this.data.solarTimeResult.trueSolarTime;
        console.log('使用真太阳时计算紫微斗数:', calculationTime);
      } else {
        calculationTime = new Date(`${this.data.birthDate}T${this.data.birthTime}:00`);
        console.log('使用北京时间计算紫微斗数:', calculationTime);
      }

      // 计算紫微斗数命盘
      const ziweiData = calculateZiweiChart(calculationTime, this.data.isMale);
      const formattedChart = formatZiweiChart(ziweiData);

      // 进行紫微斗数分析
      const analysis = comprehensiveZiweiAnalysis(ziweiData);

      // 进行精准问题分析
      const customAnalysis = generateZiweiAnalysis(
        this.data.question,
        ziweiData,
        analysis,
        this.data.isMale
      );

      this.setData({
        ziweiData: ziweiData,
        formattedChart: formattedChart,
        analysis: analysis,
        customAnalysis: customAnalysis,
        isAnalyzing: false,
        showResult: true
      });

      // 进行AI分析
      this.performZiweiAIAnalysis(ziweiData, analysis, customAnalysis);

      wx.showToast({
        title: '排盘完成',
        icon: 'success'
      });

    } catch (error) {
      console.error('紫微斗数计算错误:', error);
      this.setData({
        isAnalyzing: false
      });

      wx.showToast({
        title: '计算出错，请重试',
        icon: 'none'
      });
    }
  },

  // 格式化紫微专项分析
  formatZiweiAnalysis(customAnalysis) {
    const analysis = customAnalysis.specificAnalysis;
    let result = '';

    switch (customAnalysis.questionType) {
      case '财运':
        result = `财帛宫分析：${analysis.wealthStars || '需要观察财帛宫'}
投资时机：${analysis.timing || '需要综合判断'}
预期收益：${analysis.profit || '收益不明确'}
风险评估：${analysis.risk || '风险可控'}
投资建议：${analysis.advice || '谨慎理财'}`;
        break;

      case '事业':
        result = `官禄宫分析：${analysis.careerStars || '需要观察官禄宫'}
升职前景：${analysis.promotion || '需要努力'}
跳槽建议：${analysis.jobChange || '稳定为主'}
行动时机：${analysis.timing || '顺势而为'}
事业建议：${analysis.advice || '踏实工作'}`;
        break;

      case '婚姻':
      case '桃花':
        result = `夫妻宫分析：${analysis.spouseStars || '需要观察夫妻宫'}
感情运势：${analysis.relationship || '缘分未到'}
结婚时机：${analysis.timing || '顺其自然'}
配偶特征：${analysis.partner || '合适即可'}
婚姻建议：${analysis.advice || '真诚待人'}`;
        break;

      default:
        result = `综合分析：${analysis.advice || '根据紫微命盘综合判断'}`;
    }

    return result;
  },

  // 格式化宫位星曜
  formatPalaceStars(palace) {
    if (!palace || !palace.stars || palace.stars.length === 0) {
      return '空宫';
    }
    return palace.stars.join('、');
  },

  // 获取宫位样式类
  getPalaceClass(palaceName) {
    const palace = this.data.formattedChart[palaceName];
    if (!palace) return 'palace-normal';

    if (palace.isMingGong) return 'palace-ming';
    if (palace.isShenGong) return 'palace-shen';
    if (palace.majorStars.length > 0) return 'palace-major';
    return 'palace-normal';
  },

  // 重新排盘
  onRestart() {
    this.setData({
      question: '',
      ziweiData: null,
      formattedChart: null,
      analysis: null,
      customAnalysis: null,
      isAnalyzing: false,
      showResult: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 执行紫微斗数AI分析
  async performZiweiAIAnalysis(ziweiData, traditionalAnalysis, customAnalysis) {
    try {
      // 显示AI分析状态
      this.setData({
        isAnalyzing: true
      });

      // 构建紫微斗数上下文信息
      const ziweiContext = {
        mingGong: ziweiData.mingGong,
        caibogong: ziweiData.caibogong,
        guanlugong: ziweiData.guanlugong,
        fuqigong: ziweiData.fuqigong,
        pattern: traditionalAnalysis.pattern || '普通格局'
      };

      // 调用AI分析
      const aiAnalysis = await analyzeZiweiWithAI(this.data.question, ziweiContext);

      // 合并传统分析和AI分析
      const combinedAnalysis = {
        ...traditionalAnalysis,
        aiAnalysis: aiAnalysis
      };

      // 更新分析结果
      this.setData({
        analysis: combinedAnalysis,
        isAnalyzing: false
      });

      wx.showToast({
        title: 'AI分析完成',
        icon: 'success'
      });

    } catch (error) {
      console.error('紫微斗数AI分析失败:', error);

      // AI分析失败时，保持原有分析结果
      this.setData({
        isAnalyzing: false
      });

      wx.showToast({
        title: 'AI分析暂时不可用',
        icon: 'none'
      });
    }
  }
})