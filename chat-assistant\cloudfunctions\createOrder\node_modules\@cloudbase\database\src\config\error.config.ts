export const ERR_CODE: { [key: string]: string | number } = {
  // "-1": "",
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL:
    'SDK_DATABASE_REALTIME_LISTENER_INIT_WATCH_FAIL',
  // "realtime listener init watch fail",
  SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL:
    'SDK_DATABASE_REALTIME_LISTENER_RECONNECT_WATCH_FAIL',
  // "realtime listener reconnect watch fail",
  SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL:
    'SDK_DATABASE_REALTIME_LISTENER_REBUILD_WATCH_FAIL',
  // "realtime listener rebuild watch fail",
  SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL:
    'SDK_DATABASE_REALTIME_LISTENER_CLOSE_WATCH_FAIL',
  // "realtime listener rebuild watch fail",
  SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG:
    'SDK_DATABASE_REALTIME_LISTENER_SERVER_ERROR_MSG',
  // "realtime listener receive server error msg",
  SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA:
    'SDK_DATABASE_REALTIME_LISTENER_RECEIVE_INVALID_SERVER_DATA',
  // "realtime listener receive invalid server data",
  SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR:
    'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_ERROR',
  // "realtime listener websocket connection error",
  SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED:
    'SDK_DATABASE_REALTIME_LISTENER_WEBSOCKET_CONNECTION_CLOSED',
  // "realtime listener websocket connection closed",
  SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL:
    'SDK_DATABASE_REALTIME_LISTENER_CHECK_LAST_FAIL',
  // "realtime listener check last fail",
  SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR:
    'SDK_DATABASE_REALTIME_LISTENER_UNEXPECTED_FATAL_ERROR'
  // "realtime listener unexpected fatal error"
}
