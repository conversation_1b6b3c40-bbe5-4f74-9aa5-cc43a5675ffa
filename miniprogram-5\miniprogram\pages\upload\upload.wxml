<!--知识库上传页面-->
<view class="upload-container">
  <view class="header">
    <text class="title">知识库上传管理</text>
    <text class="subtitle">将83M知识库文件上传到云数据库</text>
  </view>

  <view class="stats-section">
    <view class="stat-item">
      <text class="stat-label">本地文件数量</text>
      <text class="stat-value">{{localFileCount}}个</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">云端文件数量</text>
      <text class="stat-value">{{cloudFileCount}}个</text>
    </view>
    <view class="stat-item">
      <text class="stat-label">上传进度</text>
      <text class="stat-value">{{uploadProgress}}%</text>
    </view>
  </view>

  <view class="action-section">
    <button class="action-btn" bindtap="checkCloudData" disabled="{{loading}}">
      检查云端数据
    </button>
    
    <button class="action-btn primary" bindtap="startUpload" disabled="{{loading || uploading}}">
      {{uploading ? '上传中...' : '开始上传知识库'}}
    </button>
    
    <button class="action-btn danger" bindtap="clearCloudData" disabled="{{loading}}">
      清空云端数据
    </button>
  </view>

  <view class="progress-section" wx:if="{{uploading}}">
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{uploadProgress}}%"></view>
    </view>
    <text class="progress-text">{{currentFile}} ({{uploadedCount}}/{{totalCount}})</text>
  </view>

  <view class="log-section">
    <text class="log-title">上传日志</text>
    <scroll-view class="log-content" scroll-y="true">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        <text class="log-time">{{item.time}}</text>
        <text class="log-message {{item.type}}">{{item.message}}</text>
      </view>
    </scroll-view>
  </view>

  <view class="sample-data-section">
    <text class="section-title">示例文件数据</text>
    <view class="sample-files">
      <view class="sample-file" wx:for="{{sampleFiles}}" wx:key="index">
        <text class="file-name">{{item.title}}</text>
        <text class="file-author">{{item.author}}</text>
        <text class="file-category">{{item.category}}</text>
        <text class="file-size">{{item.sizeKB}}KB</text>
      </view>
    </view>
  </view>
</view>
