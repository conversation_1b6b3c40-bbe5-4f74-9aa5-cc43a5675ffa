<!--pages/knowledge/knowledge.wxml - 古籍查询页面模板-->
<view class="knowledge-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">古籍查询</view>
    <view class="header-subtitle">300+典籍 • 智能检索</view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-box">
      <ink-input
        label=""
        placeholder="搜索古籍内容、书名、作者..."
        value="{{searchQuery}}"
        maxlength="50"
        bind:input="onSearchInput">
      </ink-input>
      <view class="search-actions">
        <view class="search-btn ink-ripple" bindtap="onSearch">
          <text class="search-icon">🔍</text>
        </view>
        <view class="clear-btn ink-ripple" wx:if="{{searchQuery}}" bindtap="onClearSearch">
          <text class="clear-icon">✕</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-section">
    <view class="category-title">分类筛选</view>
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="onCategoryTap">
          <text class="category-name">{{item.name}}</text>
          <text class="category-count">({{item.count}})</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 搜索结果 -->
  <view class="results-section" wx:if="{{searchResults.length > 0}}">
    <view class="section-title">搜索结果 ({{searchResults.length}})</view>
    <view class="results-list">
      <view 
        class="result-item ink-fade-in ink-ripple"
        wx:for="{{searchResults}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onResultTap">
        <view class="result-header">
          <view class="result-title">{{item.title}}</view>
          <view class="result-meta">
            <text class="result-author">{{item.author}}</text>
            <text class="result-category">{{item.category}}</text>
          </view>
        </view>
        <view class="result-content">
          <text class="matched-text">{{item.matchedContent}}</text>
        </view>
        <view class="result-footer">
          <text class="relevance">相关度: {{item.relevance}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门关键词 -->
  <view class="hot-keywords-section" wx:if="{{searchResults.length === 0}}">
    <view class="section-title">热门关键词</view>
    <view class="keywords-grid">
      <view 
        class="keyword-item ink-ripple"
        wx:for="{{hotKeywords}}"
        wx:key="*this"
        data-keyword="{{item}}"
        bindtap="onHotKeywordTap">
        <text class="keyword-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 推荐古籍 -->
  <view class="recent-books-section" wx:if="{{searchResults.length === 0}}">
    <view class="section-title">推荐古籍</view>
    <view class="books-list">
      <view 
        class="book-item ink-fade-in ink-ripple"
        wx:for="{{recentBooks}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onBookTap">
        <view class="book-header">
          <view class="book-title">{{item.title}}</view>
          <view class="book-author">{{item.author}}</view>
        </view>
        <view class="book-category">{{item.category}}</view>
        <view class="book-description">{{item.description}}</view>
      </view>
    </view>
  </view>

  <!-- 搜索中状态 -->
  <view class="searching-state" wx:if="{{isSearching}}">
    <ink-loading text="正在搜索古籍..."></ink-loading>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{searchQuery && searchResults.length === 0 && !isSearching}}">
    <view class="empty-icon">📚</view>
    <view class="empty-title">未找到相关古籍</view>
    <view class="empty-desc">请尝试其他关键词或浏览推荐古籍</view>
  </view>
</view>
