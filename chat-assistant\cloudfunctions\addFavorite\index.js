const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { userId, content, type, style } = event
  
  try {
    const result = await db.collection('favorites').add({
      data: {
        userId,
        content,
        type, // 'chat' or 'keyboard'
        style,
        createTime: db.serverDate()
      }
    })
    
    return {
      success: true,
      _id: result._id
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}
