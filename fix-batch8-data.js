/**
 * 修复第8批数据中的内容混合问题
 * 问题：《周易杭氏学》文件的content字段包含了《梅花易数》等其他文件的内容
 *
 * 修复策略：
 * 1. 找到问题文件的位置
 * 2. 截断过长的content字段
 * 3. 重新构建正确的JSON结构
 */

const fs = require('fs');

console.log('🔧 开始修复第8批数据...');

// 读取原始文件
const originalFile = 'knowledge-data-clean.js';
let fileContent = fs.readFileSync(originalFile, 'utf8');

console.log('📊 原文件大小:', Math.round(fileContent.length / 1024), 'KB');

// 备份原文件
const backupFile = `knowledge-data-clean-backup-${Date.now()}.js`;
fs.writeFileSync(backupFile, fileContent);
console.log(`✅ 已备份原文件到: ${backupFile}`);

// 找到问题区域：第8批数据中《周易杭氏学》文件的异常长content
console.log('\n🔍 定位问题区域...');

// 找到第8批数据开始
const batch8StartMarker = '// 第 8 批数据';
const batch8StartIndex = fileContent.indexOf(batch8StartMarker);
if (batch8StartIndex === -1) {
    console.error('❌ 找不到第8批数据标记');
    process.exit(1);
}

// 找到第9批数据开始
const batch9StartMarker = '// 第 9 批数据';
const batch9StartIndex = fileContent.indexOf(batch9StartMarker);
if (batch9StartIndex === -1) {
    console.error('❌ 找不到第9批数据标记');
    process.exit(1);
}

console.log(`📍 第8批数据位置: ${batch8StartIndex} - ${batch9StartIndex}`);

// 在第8批数据中找到《周易杭氏学》文件
const zhouyiMarker = '"周易杭氏学-清-杭辛斋.txt"';
const zhouyiIndex = fileContent.indexOf(zhouyiMarker, batch8StartIndex);
if (zhouyiIndex === -1) {
    console.error('❌ 找不到《周易杭氏学》文件');
    process.exit(1);
}

console.log(`📍 找到《周易杭氏学》文件位置: ${zhouyiIndex}`);

// 找到这个文件的content字段开始位置
const contentStartMarker = '"content": "';
const contentStartIndex = fileContent.indexOf(contentStartMarker, zhouyiIndex);
if (contentStartIndex === -1) {
    console.error('❌ 找不到content字段');
    process.exit(1);
}

const actualContentStart = contentStartIndex + contentStartMarker.length;
console.log(`📍 content字段开始位置: ${actualContentStart}`);

// 找到《梅花易数》内容开始的位置（这是错误混入的内容）
const meihuaMarker = '邵康節先生手著梅花易數';
const meihuaIndex = fileContent.indexOf(meihuaMarker, actualContentStart);
if (meihuaIndex === -1) {
    console.error('❌ 找不到《梅花易数》混入内容');
    process.exit(1);
}

console.log(`📍 找到《梅花易数》混入内容位置: ${meihuaIndex}`);
console.log(`🚨 发现问题: content字段异常长度: ${meihuaIndex - actualContentStart} 字符`);

// 修复策略：截断content字段到《梅花易数》内容之前
console.log('\n🔧 开始修复...');

// 在《梅花易数》内容之前找一个合适的截断点
let cutoffPoint = meihuaIndex;

// 向前查找，找到一个合适的结束点（比如句号、引号等）
for (let i = meihuaIndex - 1; i > actualContentStart; i--) {
    const char = fileContent[i];
    if (char === '。' || char === '」' || char === '』' || char === '"') {
        cutoffPoint = i + 1;
        break;
    }
    // 如果找到了明显的分界点，也可以在这里截断
    if (fileContent.substring(i-10, i).includes('八卦') &&
        fileContent.substring(i, i+10).includes('邵')) {
        cutoffPoint = i;
        break;
    }
}

console.log(`📍 选择截断点: ${cutoffPoint}`);
console.log(`📝 截断前内容预览: ...${fileContent.substring(cutoffPoint-50, cutoffPoint)}`);
console.log(`📝 截断后内容预览: ${fileContent.substring(cutoffPoint, cutoffPoint+50)}...`);

// 构建修复后的内容
const beforeContent = fileContent.substring(0, cutoffPoint);
const afterContentMarker = '",\n    "file_size":';
const afterContentIndex = fileContent.indexOf(afterContentMarker, meihuaIndex);

if (afterContentIndex === -1) {
    console.error('❌ 找不到content字段结束标记');
    process.exit(1);
}

const afterContent = fileContent.substring(afterContentIndex);

// 组合修复后的内容
const fixedContent = beforeContent + afterContent;

console.log('\n📊 修复统计:');
console.log(`- 原文件大小: ${Math.round(fileContent.length / 1024)} KB`);
console.log(`- 修复后大小: ${Math.round(fixedContent.length / 1024)} KB`);
console.log(`- 减少大小: ${Math.round((fileContent.length - fixedContent.length) / 1024)} KB`);

// 保存修复后的文件
const fixedFile = 'knowledge-data-clean-fixed.js';
fs.writeFileSync(fixedFile, fixedContent);
console.log(`✅ 修复后文件已保存: ${fixedFile}`);

// 验证修复结果
console.log('\n🔍 验证修复结果...');
try {
    // 检查基本语法
    if (fixedContent.includes('const KNOWLEDGE_BATCH_8 = [') &&
        fixedContent.includes('const KNOWLEDGE_BATCH_9 = [')) {
        console.log('✅ 基本结构验证通过');
    } else {
        throw new Error('基本结构验证失败');
    }

    // 检查是否还包含《梅花易数》混入内容
    const batch8Section = fixedContent.substring(
        fixedContent.indexOf('const KNOWLEDGE_BATCH_8 = ['),
        fixedContent.indexOf('const KNOWLEDGE_BATCH_9 = [')
    );

    if (!batch8Section.includes('邵康節先生手著梅花易數')) {
        console.log('✅ 《梅花易数》混入内容已清除');
    } else {
        console.log('⚠️  警告: 仍然包含《梅花易数》内容');
    }

    console.log('\n🎉 修复完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 检查修复后的文件内容是否正确');
    console.log('2. 如果确认无误，可以替换原文件:');
    console.log(`   copy "${fixedFile}" "${originalFile}"`);
    console.log(`3. 备份文件: ${backupFile}`);

} catch (error) {
    console.error('❌ 修复验证失败:', error.message);
    console.log('请检查修复后的文件并手动调整');
}

if (zhouyiStart === -1 || meihuaContentStart === -1) {
    console.error('❌ 无法找到关键位置标记');
    process.exit(1);
}

console.log('\n🚨 发现的问题:');
console.log(`- 《周易杭氏学》文件从第 ${zhouyiStart + 1} 行开始`);
console.log(`- 《梅花易数》内容从第 ${meihuaContentStart + 1} 行开始`);
console.log(`- 内容混合长度: ${meihuaContentStart - zhouyiStart} 行`);

// 分析《周易杭氏学》的正确结束位置
// 我们需要找到《梅花易数》内容开始之前的位置
let zhouyiContentEnd = -1;

// 在《梅花易数》内容开始之前寻找合适的结束点
for (let i = meihuaContentStart - 1; i > zhouyiStart; i--) {
    const line = lines[i].trim();
    // 寻找可能的《周易杭氏学》内容结束标志
    if (line.includes('位方卦八王文') || 
        line.includes('序次卦八羲伏') || 
        line.includes('六十四卦') ||
        line.includes('八卦') ||
        (line.includes('"') && line.includes(']'))) {
        zhouyiContentEnd = i;
        console.log(`📍 推测《周易杭氏学》内容结束位置: 行 ${i + 1}`);
        console.log(`📝 结束行内容: ${line.substring(0, 100)}...`);
        break;
    }
}

if (zhouyiContentEnd === -1) {
    console.error('❌ 无法确定《周易杭氏学》的正确结束位置');
    process.exit(1);
}

console.log('\n📋 修复计划:');
console.log(`1. 截断《周易杭氏学》文件的content字段到第 ${zhouyiContentEnd + 1} 行`);
console.log(`2. 将《梅花易数》内容分离为独立文件`);
console.log(`3. 重新构建第8批数据的JSON结构`);
console.log(`4. 验证修复后的数据完整性`);

// 执行修复
console.log('\n🔧 开始执行修复...');

// 提取《周易杭氏学》的正确内容
const zhouyiLines = [];
let inZhouyiContent = false;
let contentStarted = false;

for (let i = zhouyiStart; i <= zhouyiContentEnd; i++) {
    const line = lines[i];
    
    if (line.includes('"content":')) {
        inZhouyiContent = true;
        contentStarted = true;
        zhouyiLines.push(line);
        continue;
    }
    
    if (inZhouyiContent) {
        // 检查是否到达content字段的结束
        if (line.includes('"file_size":') || 
            line.includes('"created_at":') ||
            line.includes('"updated_at":')) {
            inZhouyiContent = false;
            // 添加content字段的正确结束
            const lastLine = zhouyiLines[zhouyiLines.length - 1];
            if (!lastLine.trim().endsWith('",')) {
                zhouyiLines[zhouyiLines.length - 1] = lastLine.replace(/[",]*$/, '",');
            }
            zhouyiLines.push(line);
            continue;
        }
        
        if (contentStarted) {
            zhouyiLines.push(line);
        }
    } else {
        zhouyiLines.push(line);
    }
}

console.log(`✅ 提取《周易杭氏学》内容: ${zhouyiLines.length} 行`);

// 保存修复结果到临时文件进行验证
const tempFixedFile = 'knowledge-data-clean-fixed-temp.js';
const fixedLines = [
    ...lines.slice(0, zhouyiStart),
    ...zhouyiLines,
    ...lines.slice(batch9Start)
];

fs.writeFileSync(tempFixedFile, fixedLines.join('\n'));
console.log(`✅ 已生成临时修复文件: ${tempFixedFile}`);

// 验证修复后的文件
console.log('\n🔍 验证修复结果...');

try {
    // 尝试解析JavaScript文件
    const fixedContent = fs.readFileSync(tempFixedFile, 'utf8');
    
    // 检查基本语法
    if (fixedContent.includes('const KNOWLEDGE_BATCH_8 = [') && 
        fixedContent.includes('const KNOWLEDGE_BATCH_9 = [')) {
        console.log('✅ 基本结构验证通过');
    } else {
        throw new Error('基本结构验证失败');
    }
    
    // 检查第8批数据是否正确结束
    const batch8Section = fixedContent.substring(
        fixedContent.indexOf('const KNOWLEDGE_BATCH_8 = ['),
        fixedContent.indexOf('const KNOWLEDGE_BATCH_9 = [')
    );
    
    if (batch8Section.includes('];')) {
        console.log('✅ 第8批数据结构完整');
    } else {
        throw new Error('第8批数据结构不完整');
    }
    
    console.log('\n🎉 修复验证成功！');
    
    // 询问是否应用修复
    console.log('\n❓ 是否要应用修复？');
    console.log('如果确认，请手动将临时文件重命名为原文件名');
    console.log(`临时文件: ${tempFixedFile}`);
    console.log(`原文件: ${originalFile}`);
    console.log(`备份文件: ${backupFile}`);
    
} catch (error) {
    console.error('❌ 修复验证失败:', error);
    console.log('请检查临时文件并手动修复');
}

console.log('\n📊 修复统计:');
console.log(`- 原文件行数: ${lines.length}`);
console.log(`- 修复后行数: ${fixedLines.length}`);
console.log(`- 减少行数: ${lines.length - fixedLines.length}`);
console.log(`- 备份文件: ${backupFile}`);
console.log(`- 临时修复文件: ${tempFixedFile}`);
