## Change Log

### v2.88.0 (2018/08/10)
- [#2996](https://github.com/request/request/pull/2996) fix(uuid): import versioned uuid (@kwonoj)
- [#2994](https://github.com/request/request/pull/2994) Update to oauth-sign 0.9.0 (@dlecocq)
- [#2993](https://github.com/request/request/pull/2993) Fix header tests (@simov)
- [#2904](https://github.com/request/request/pull/2904) #515, #2894 Strip port suffix from Host header if the protocol is known. (#2904) (@paambaati)
- [#2791](https://github.com/request/request/pull/2791) Improve AWS SigV4 support. (#2791) (@vikhyat)
- [#2977](https://github.com/request/request/pull/2977) Update test certificates (@simov)

### v2.87.0 (2018/05/21)
- [#2943](https://github.com/request/request/pull/2943) Replace hawk dependency with a local implemenation (#2943) (@hueniverse)

### v2.86.0 (2018/05/15)
- [#2885](https://github.com/request/request/pull/2885) Remove redundant code (for Node.js 0.9.4 and below) and dependency (@ChALkeR)
- [#2942](https://github.com/request/request/pull/2942) Make Test GREEN Again! (@simov)
- [#2923](https://github.com/request/request/pull/2923) Alterations for failing CI tests (@gareth-robinson)

### v2.85.0 (2018/03/12)
- [#2880](https://github.com/request/request/pull/2880) Revert "Update hawk to 7.0.7 (#2880)" (@simov)

### v2.84.0 (2018/03/12)
- [#2793](https://github.com/request/request/pull/2793) Fixed calculation of oauth_body_hash, issue #2792 (@dvishniakov)
- [#2880](https://github.com/request/request/pull/2880) Update hawk to 7.0.7 (#2880) (@kornel-kedzierski)

### v2.83.0 (2017/09/27)
- [#2776](https://github.com/request/request/pull/2776) Updating tough-cookie due to security fix. (#2776) (@karlnorling)

### v2.82.0 (2017/09/19)
- [#2703](https://github.com/request/request/pull/2703) Add Node.js v8 to Travis CI (@ryysud)
- [#2751](https://github.com/request/request/pull/2751) Update of hawk and qs to latest version (#2751) (@Olivier-Moreau)
- [#2658](https://github.com/request/request/pull/2658) Fixed some text in README.md (#2658) (@Marketionist)
- [#2635](https://github.com/request/request/pull/2635) chore(package): update aws-sign2 to version 0.7.0 (#2635) (@greenkeeperio-bot)
- [#2641](https://github.com/request/request/pull/2641) Update README to simplify & update convenience methods (#2641) (@FredKSchott)
- [#2541](https://github.com/request/request/pull/2541) Add convenience method for HTTP OPTIONS (#2541) (@jamesseanwright)
- [#2605](https://github.com/request/request/pull/2605) Add promise support section to README (#2605) (@FredKSchott)
- [#2579](https://github.com/request/request/pull/2579) refactor(lint): replace eslint with standard (#2579) (@ahmadnassri)
- [#2598](https://github.com/request/request/pull/2598) Update codecov to version 2.0.2 🚀 (@greenkeeperio-bot)
- [#2590](https://github.com/request/request/pull/2590) Adds test-timing keepAlive test (@nicjansma)
- [#2589](https://github.com/request/request/pull/2589) fix tabulation on request example README.MD (@odykyi)
- [#2594](https://github.com/request/request/pull/2594) chore(dependencies): har-validator to 5.x [removes babel dep] (@ahmadnassri)

### v2.81.0 (2017/03/09)
- [#2584](https://github.com/request/request/pull/2584) Security issue: Upgrade qs to version 6.4.0 (@sergejmueller)
- [#2578](https://github.com/request/request/pull/2578) safe-buffer doesn't zero-fill by default, its just a polyfill. (#2578) (@mikeal)
- [#2566](https://github.com/request/request/pull/2566) Timings: Tracks 'lookup', adds 'wait' time, fixes connection re-use (#2566) (@nicjansma)
- [#2574](https://github.com/request/request/pull/2574) Migrating to safe-buffer for improved security. (@mikeal)
- [#2573](https://github.com/request/request/pull/2573) fixes #2572 (@ahmadnassri)

### v2.80.0 (2017/03/04)
- [#2571](https://github.com/request/request/pull/2571) Correctly format the Host header for IPv6 addresses (@JamesMGreene)
- [#2558](https://github.com/request/request/pull/2558) Update README.md example snippet (@FredKSchott)
- [#2221](https://github.com/request/request/pull/2221) Adding a simple Response object reference in argument specification (@calamarico)
- [#2452](https://github.com/request/request/pull/2452) Adds .timings array with DNC, TCP, request and response times (@nicjansma)
- [#2553](https://github.com/request/request/pull/2553) add ISSUE_TEMPLATE, move PR template (@FredKSchott)
- [#2539](https://github.com/request/request/pull/2539) Create PULL_REQUEST_TEMPLATE.md (@FredKSchott)
- [#2524](https://github.com/request/request/pull/2524) Update caseless to version 0.12.0 🚀 (@greenkeeperio-bot)
- [#2460](https://github.com/request/request/pull/2460) Fix wrong MIME type in example (@OwnageIsMagic)
- [#2514](https://github.com/request/request/pull/2514) Change tags to keywords in package.json (@humphd)
- [#2492](https://github.com/request/request/pull/2492) More lenient gzip decompression (@addaleax)

### v2.79.0 (2016/11/18)
- [#2368](https://github.com/request/request/pull/2368) Fix typeof check in test-pool.js (@forivall)
- [#2394](https://github.com/request/request/pull/2394) Use `files` in package.json (@SimenB)
- [#2463](https://github.com/request/request/pull/2463) AWS support for session tokens for temporary credentials (@simov)
- [#2467](https://github.com/request/request/pull/2467) Migrate to uuid (@simov, @antialias)
- [#2459](https://github.com/request/request/pull/2459) Update taper to version 0.5.0 🚀 (@greenkeeperio-bot)
- [#2448](https://github.com/request/request/pull/2448) Make other connect timeout test more reliable too (@mscdex)

### v2.78.0 (2016/11/03)
- [#2447](https://github.com/request/request/pull/2447) Always set request timeout on keep-alive connections (@mscdex)

### v2.77.0 (2016/11/03)
- [#2439](https://github.com/request/request/pull/2439) Fix socket 'connect' listener handling (@mscdex)
- [#2442](https://github.com/request/request/pull/2442) 👻😱 Node.js 0.10 is unmaintained 😱👻 (@greenkeeperio-bot)
- [#2435](https://github.com/request/request/pull/2435) Add followOriginalHttpMethod to redirect to original HTTP method (@kirrg001)
- [#2414](https://github.com/request/request/pull/2414) Improve test-timeout reliability (@mscdex)

### v2.76.0 (2016/10/25)
- [#2424](https://github.com/request/request/pull/2424) Handle buffers directly instead of using "bl" (@zertosh)
- [#2415](https://github.com/request/request/pull/2415) Re-enable timeout tests on Travis + other fixes (@mscdex)
- [#2431](https://github.com/request/request/pull/2431) Improve timeouts accuracy and node v6.8.0+ compatibility (@mscdex, @greenkeeperio-bot)
- [#2428](https://github.com/request/request/pull/2428) Update qs to version 6.3.0 🚀 (@greenkeeperio-bot)
- [#2420](https://github.com/request/request/pull/2420) change .on to .once, remove possible memory leaks (@duereg)
- [#2426](https://github.com/request/request/pull/2426) Remove "isFunction" helper in favor of "typeof" check (@zertosh)
- [#2425](https://github.com/request/request/pull/2425) Simplify "defer" helper creation (@zertosh)
- [#2402](https://github.com/request/request/pull/2402) form-data@2.1.1 breaks build 🚨 (@greenkeeperio-bot)
- [#2393](https://github.com/request/request/pull/2393) Update form-data to version 2.1.0 🚀 (@greenkeeperio-bot)

### v2.75.0 (2016/09/17)
- [#2381](https://github.com/request/request/pull/2381) Drop support for Node 0.10 (@simov)
- [#2377](https://github.com/request/request/pull/2377) Update form-data to version 2.0.0 🚀 (@greenkeeperio-bot)
- [#2353](https://github.com/request/request/pull/2353) Add greenkeeper ignored packages (@simov)
- [#2351](https://github.com/request/request/pull/2351) Update karma-tap to version 3.0.1 🚀 (@greenkeeperio-bot)
- [#2348](https://github.com/request/request/pull/2348) form-data@1.0.1 breaks build 🚨 (@greenkeeperio-bot)
- [#2349](https://github.com/request/request/pull/2349) Check error type instead of string (@scotttrinh)

### v2.74.0 (2016/07/22)
- [#2295](https://github.com/request/request/pull/2295) Update tough-cookie to 2.3.0 (@stash-sfdc)
- [#2280](https://github.com/request/request/pull/2280) Update karma-tap to version 2.0.1 🚀 (@greenkeeperio-bot)

### v2.73.0 (2016/07/09)
- [#2240](https://github.com/request/request/pull/2240) Remove connectionErrorHandler to fix #1903 (@zarenner)
- [#2251](https://github.com/request/request/pull/2251) tape@4.6.0 breaks build 🚨 (@greenkeeperio-bot)
- [#2225](https://github.com/request/request/pull/2225) Update docs (@ArtskydJ)
- [#2203](https://github.com/request/request/pull/2203) Update browserify to version 13.0.1 🚀 (@greenkeeperio-bot)
- [#2275](https://github.com/request/request/pull/2275) Update karma to version 1.1.1 🚀 (@greenkeeperio-bot)
- [#2204](https://github.com/request/request/pull/2204) Add codecov.yml and disable PR comments (@simov)
- [#2212](https://github.com/request/request/pull/2212) Fix link to http.IncomingMessage documentation (@nazieb)
- [#2208](https://github.com/request/request/pull/2208) Update to form-data RC4 and pass null values to it (@simov)
- [#2207](https://github.com/request/request/pull/2207) Move aws4 require statement to the top (@simov)
- [#2199](https://github.com/request/request/pull/2199) Update karma-coverage to version 1.0.0 🚀 (@greenkeeperio-bot)
- [#2206](https://github.com/request/request/pull/2206) Update qs to version 6.2.0 🚀 (@greenkeeperio-bot)
- [#2205](https://github.com/request/request/pull/2205) Use server-destory to close hanging sockets in tests (@simov)
- [#2200](https://github.com/request/request/pull/2200) Update karma-cli to version 1.0.0 🚀 (@greenkeeperio-bot)

### v2.72.0 (2016/04/17)
- [#2176](https://github.com/request/request/pull/2176) Do not try to pipe Gzip responses with no body (@simov)
- [#2175](https://github.com/request/request/pull/2175) Add 'delete' alias for the 'del' API method (@simov, @MuhanZou)
- [#2172](https://github.com/request/request/pull/2172) Add support for deflate content encoding (@czardoz)
- [#2169](https://github.com/request/request/pull/2169) Add callback option (@simov)
- [#2165](https://github.com/request/request/pull/2165) Check for self.req existence inside the write method (@simov)
- [#2167](https://github.com/request/request/pull/2167) Fix TravisCI badge reference master branch (@a0viedo)

### v2.71.0 (2016/04/12)
- [#2164](https://github.com/request/request/pull/2164) Catch errors from the underlying http module (@simov)

### v2.70.0 (2016/04/05)
- [#2147](https://github.com/request/request/pull/2147) Update eslint to version 2.5.3 🚀 (@simov, @greenkeeperio-bot)
- [#2009](https://github.com/request/request/pull/2009) Support JSON stringify replacer argument. (@elyobo)
- [#2142](https://github.com/request/request/pull/2142) Update eslint to version 2.5.1 🚀 (@greenkeeperio-bot)
- [#2128](https://github.com/request/request/pull/2128) Update browserify-istanbul to version 2.0.0 🚀 (@greenkeeperio-bot)
- [#2115](https://github.com/request/request/pull/2115) Update eslint to version 2.3.0 🚀 (@simov, @greenkeeperio-bot)
- [#2089](https://github.com/request/request/pull/2089) Fix badges (@simov)
- [#2092](https://github.com/request/request/pull/2092) Update browserify-istanbul to version 1.0.0 🚀 (@greenkeeperio-bot)
- [#2079](https://github.com/request/request/pull/2079) Accept read stream as body option (@simov)
- [#2070](https://github.com/request/request/pull/2070) Update bl to version 1.1.2 🚀 (@greenkeeperio-bot)
- [#2063](https://github.com/request/request/pull/2063) Up bluebird and oauth-sign (@simov)
- [#2058](https://github.com/request/request/pull/2058) Karma fixes for latest versions (@eiriksm)
- [#2057](https://github.com/request/request/pull/2057) Update contributing guidelines (@simov)
- [#2054](https://github.com/request/request/pull/2054) Update qs to version 6.1.0 🚀 (@greenkeeperio-bot)

### v2.69.0 (2016/01/27)
- [#2041](https://github.com/request/request/pull/2041) restore aws4 as regular dependency (@rmg)

### v2.68.0 (2016/01/27)
- [#2036](https://github.com/request/request/pull/2036) Add AWS Signature Version 4 (@simov, @mirkods)
- [#2022](https://github.com/request/request/pull/2022) Convert numeric multipart bodies to string (@simov, @feross)
- [#2024](https://github.com/request/request/pull/2024) Update har-validator dependency for nsp advisory #76 (@TylerDixon)
- [#2016](https://github.com/request/request/pull/2016) Update qs to version 6.0.2 🚀 (@greenkeeperio-bot)
- [#2007](https://github.com/request/request/pull/2007) Use the `extend` module instead of util._extend (@simov)
- [#2003](https://github.com/request/request/pull/2003) Update browserify to version 13.0.0 🚀 (@greenkeeperio-bot)
- [#1989](https://github.com/request/request/pull/1989) Update buffer-equal to version 1.0.0 🚀 (@greenkeeperio-bot)
- [#1956](https://github.com/request/request/pull/1956) Check form-data content-length value before setting up the header (@jongyoonlee)
- [#1958](https://github.com/request/request/pull/1958) Use IncomingMessage.destroy method (@simov)
- [#1952](https://github.com/request/request/pull/1952) Adds example for Tor proxy (@prometheansacrifice)
- [#1943](https://github.com/request/request/pull/1943) Update eslint to version 1.10.3 🚀 (@simov, @greenkeeperio-bot)
- [#1924](https://github.com/request/request/pull/1924) Update eslint to version 1.10.1 🚀 (@greenkeeperio-bot)
- [#1915](https://github.com/request/request/pull/1915) Remove content-length and transfer-encoding headers from defaultProxyHeaderWhiteList (@yaxia)

### v2.67.0 (2015/11/19)
- [#1913](https://github.com/request/request/pull/1913) Update http-signature to version 1.1.0 🚀 (@greenkeeperio-bot)

### v2.66.0 (2015/11/18)
- [#1906](https://github.com/request/request/pull/1906) Update README URLs based on HTTP redirects (@ReadmeCritic)
- [#1905](https://github.com/request/request/pull/1905) Convert typed arrays into regular buffers (@simov)
- [#1902](https://github.com/request/request/pull/1902) node-uuid@1.4.7 breaks build 🚨 (@greenkeeperio-bot)
- [#1894](https://github.com/request/request/pull/1894) Fix tunneling after redirection from https (Original: #1881) (@simov, @falms)
- [#1893](https://github.com/request/request/pull/1893) Update eslint to version 1.9.0 🚀 (@greenkeeperio-bot)
- [#1852](https://github.com/request/request/pull/1852) Update eslint to version 1.7.3 🚀 (@simov, @greenkeeperio-bot, @paulomcnally, @michelsalib, @arbaaz, @nsklkn, @LoicMahieu, @JoshWillik, @jzaefferer, @ryanwholey, @djchie, @thisconnect, @mgenereu, @acroca, @Sebmaster, @KoltesDigital)
- [#1876](https://github.com/request/request/pull/1876) Implement loose matching for har mime types (@simov)
- [#1875](https://github.com/request/request/pull/1875) Update bluebird to version 3.0.2 🚀 (@simov, @greenkeeperio-bot)
- [#1871](https://github.com/request/request/pull/1871) Update browserify to version 12.0.1 🚀 (@greenkeeperio-bot)
- [#1866](https://github.com/request/request/pull/1866) Add missing quotes on x-token property in README (@miguelmota)
- [#1874](https://github.com/request/request/pull/1874) Fix typo in README.md (@gswalden)
- [#1860](https://github.com/request/request/pull/1860) Improve referer header tests and docs (@simov)
- [#1861](https://github.com/request/request/pull/1861) Remove redundant call to Stream constructor (@watson)
- [#1857](https://github.com/request/request/pull/1857) Fix Referer header to point to the original host name (@simov)
- [#1850](https://github.com/request/request/pull/1850) Update karma-coverage to version 0.5.3 🚀 (@greenkeeperio-bot)
- [#1847](https://github.com/request/request/pull/1847) Use node's latest version when building (@simov)
- [#1836](https://github.com/request/request/pull/1836) Tunnel: fix wrong property name (@KoltesDigital)
- [#1820](https://github.com/request/request/pull/1820) Set href as request.js uses it (@mgenereu)
- [#1840](https://github.com/request/request/pull/1840) Update http-signature to version 1.0.2 🚀 (@greenkeeperio-bot)
- [#1845](https://github.com/request/request/pull/1845) Update istanbul to version 0.4.0 🚀 (@greenkeeperio-bot)

### v2.65.0 (2015/10/11)
- [#1833](https://github.com/request/request/pull/1833) Update aws-sign2 to version 0.6.0 🚀 (@greenkeeperio-bot)
- [#1811](https://github.com/request/request/pull/1811) Enable loose cookie parsing in tough-cookie (@Sebmaster)
- [#1830](https://github.com/request/request/pull/1830) Bring back tilde ranges for all dependencies (@simov)
- [#1821](https://github.com/request/request/pull/1821) Implement support for RFC 2617 MD5-sess algorithm. (@BigDSK)
- [#1828](https://github.com/request/request/pull/1828) Updated qs dependency to 5.2.0 (@acroca)
- [#1818](https://github.com/request/request/pull/1818) Extract `readResponseBody` method out of `onRequestResponse` (@pvoisin)
- [#1819](https://github.com/request/request/pull/1819) Run stringify once (@mgenereu)
- [#1814](https://github.com/request/request/pull/1814) Updated har-validator to version 2.0.2 (@greenkeeperio-bot)
- [#1807](https://github.com/request/request/pull/1807) Updated tough-cookie to version 2.1.0 (@greenkeeperio-bot)
- [#1800](https://github.com/request/request/pull/1800) Add caret ranges for devDependencies, except eslint (@simov)
- [#1799](https://github.com/request/request/pull/1799) Updated karma-browserify to version 4.4.0 (@greenkeeperio-bot)
- [#1797](https://github.com/request/request/pull/1797) Updated tape to version 4.2.0 (@greenkeeperio-bot)
- [#1788](https://github.com/request/request/pull/1788) Pinned all dependencies (@greenkeeperio-bot)

### v2.64.0 (2015/09/25)
- [#1787](https://github.com/request/request/pull/1787) npm ignore examples, release.sh and disabled.appveyor.yml (@thisconnect)
- [#1775](https://github.com/request/request/pull/1775) Fix typo in README.md (@djchie)
- [#1776](https://github.com/request/request/pull/1776) Changed word 'conjuction' to read 'conjunction' in README.md (@ryanwholey)
- [#1785](https://github.com/request/request/pull/1785) Revert: Set default application/json content-type when using json option #1772 (@simov)

### v2.63.0 (2015/09/21)
- [#1772](https://github.com/request/request/pull/1772) Set default application/json content-type when using json option (@jzaefferer)

### v2.62.0 (2015/09/15)
- [#1768](https://github.com/request/request/pull/1768) Add node 4.0 to the list of build targets (@simov)
- [#1767](https://github.com/request/request/pull/1767) Query strings now cooperate with unix sockets (@JoshWillik)
- [#1750](https://github.com/request/request/pull/1750) Revert doc about installation of tough-cookie added in #884 (@LoicMahieu)
- [#1746](https://github.com/request/request/pull/1746) Missed comma in Readme (@nsklkn)
- [#1743](https://github.com/request/request/pull/1743) Fix options not being initialized in defaults method (@simov)

### v2.61.0 (2015/08/19)
- [#1721](https://github.com/request/request/pull/1721) Minor fix in README.md (@arbaaz)
- [#1733](https://github.com/request/request/pull/1733) Avoid useless Buffer transformation (@michelsalib)
- [#1726](https://github.com/request/request/pull/1726) Update README.md (@paulomcnally)
- [#1715](https://github.com/request/request/pull/1715) Fix forever option in node > 0.10 #1709 (@calibr)
- [#1716](https://github.com/request/request/pull/1716) Do not create Buffer from Object in setContentLength(iojs v3.0 issue) (@calibr)
- [#1711](https://github.com/request/request/pull/1711) Add ability to detect connect timeouts (@kevinburke)
- [#1712](https://github.com/request/request/pull/1712) Set certificate expiration to August 2, 2018 (@kevinburke)
- [#1700](https://github.com/request/request/pull/1700) debug() when JSON.parse() on a response body fails (@phillipj)

### v2.60.0 (2015/07/21)
- [#1687](https://github.com/request/request/pull/1687) Fix caseless bug - content-type not being set for multipart/form-data (@simov, @garymathews)

### v2.59.0 (2015/07/20)
- [#1671](https://github.com/request/request/pull/1671) Add tests and docs for using the agent, agentClass, agentOptions and forever options.
 Forever option defaults to using http(s).Agent in node 0.12+ (@simov)
- [#1679](https://github.com/request/request/pull/1679) Fix - do not remove OAuth param when using OAuth realm (@simov, @jhalickman)
- [#1668](https://github.com/request/request/pull/1668) updated dependencies (@deamme)
- [#1656](https://github.com/request/request/pull/1656) Fix form method (@simov)
- [#1651](https://github.com/request/request/pull/1651) Preserve HEAD method when using followAllRedirects (@simov)
- [#1652](https://github.com/request/request/pull/1652) Update `encoding` option documentation in README.md (@daniel347x)
- [#1650](https://github.com/request/request/pull/1650) Allow content-type overriding when using the `form` option (@simov)
- [#1646](https://github.com/request/request/pull/1646) Clarify the nature of setting `ca` in `agentOptions` (@jeffcharles)

### v2.58.0 (2015/06/16)
- [#1638](https://github.com/request/request/pull/1638) Use the `extend` module to deep extend in the defaults method (@simov)
- [#1631](https://github.com/request/request/pull/1631) Move tunnel logic into separate module (@simov)
- [#1634](https://github.com/request/request/pull/1634) Fix OAuth query transport_method (@simov)
- [#1603](https://github.com/request/request/pull/1603) Add codecov (@simov)

### v2.57.0 (2015/05/31)
- [#1615](https://github.com/request/request/pull/1615) Replace '.client' with '.socket' as the former was deprecated in 2.2.0. (@ChALkeR)

### v2.56.0 (2015/05/28)
- [#1610](https://github.com/request/request/pull/1610) Bump module dependencies (@simov)
- [#1600](https://github.com/request/request/pull/1600) Extract the querystring logic into separate module (@simov)
- [#1607](https://github.com/request/request/pull/1607) Re-generate certificates (@simov)
- [#1599](https://github.com/request/request/pull/1599) Move getProxyFromURI logic below the check for Invaild URI (#1595) (@simov)
- [#1598](https://github.com/request/request/pull/1598) Fix the way http verbs are defined in order to please intellisense IDEs (@simov, @flannelJesus)
- [#1591](https://github.com/request/request/pull/1591) A few minor fixes: (@simov)
- [#1584](https://github.com/request/request/pull/1584) Refactor test-default tests (according to comments in #1430) (@simov)
- [#1585](https://github.com/request/request/pull/1585) Fixing documentation regarding TLS options (#1583) (@mainakae)
- [#1574](https://github.com/request/request/pull/1574) Refresh the oauth_nonce on redirect (#1573) (@simov)
- [#1570](https://github.com/request/request/pull/1570) Discovered tests that weren't properly running (@seanstrom)
- [#1569](https://github.com/request/request/pull/1569) Fix pause before response arrives (@kevinoid)
- [#1558](https://github.com/request/request/pull/1558) Emit error instead of throw (@simov)
- [#1568](https://github.com/request/request/pull/1568) Fix stall when piping gzipped response (@kevinoid)
- [#1560](https://github.com/request/request/pull/1560) Update combined-stream (@apechimp)
- [#1543](https://github.com/request/request/pull/1543) Initial support for oauth_body_hash on json payloads (@simov, @aesopwolf)
- [#1541](https://github.com/request/request/pull/1541) Fix coveralls (@simov)
- [#1540](https://github.com/request/request/pull/1540) Fix recursive defaults for convenience methods (@simov)
- [#1536](https://github.com/request/request/pull/1536) More eslint style rules (@froatsnook)
- [#1533](https://github.com/request/request/pull/1533) Adding dependency status bar to README.md (@YasharF)
- [#1539](https://github.com/request/request/pull/1539) ensure the latest version of har-validator is included (@ahmadnassri)
- [#1516](https://github.com/request/request/pull/1516) forever+pool test (@devTristan)

### v2.55.0 (2015/04/05)
- [#1520](https://github.com/request/request/pull/1520) Refactor defaults (@simov)
- [#1525](https://github.com/request/request/pull/1525) Delete request headers with undefined value. (@froatsnook)
- [#1521](https://github.com/request/request/pull/1521) Add promise tests (@simov)
- [#1518](https://github.com/request/request/pull/1518) Fix defaults (@simov)
- [#1515](https://github.com/request/request/pull/1515) Allow static invoking of convenience methods (@simov)
- [#1505](https://github.com/request/request/pull/1505) Fix multipart boundary extraction regexp (@simov)
- [#1510](https://github.com/request/request/pull/1510) Fix basic auth form data (@simov)

### v2.54.0 (2015/03/24)
- [#1501](https://github.com/request/request/pull/1501) HTTP Archive 1.2 support (@ahmadnassri)
- [#1486](https://github.com/request/request/pull/1486) Add a test for the forever agent (@akshayp)
- [#1500](https://github.com/request/request/pull/1500) Adding handling for no auth method and null bearer (@philberg)
- [#1498](https://github.com/request/request/pull/1498) Add table of contents in readme (@simov)
- [#1477](https://github.com/request/request/pull/1477) Add support for qs options via qsOptions key (@simov)
- [#1496](https://github.com/request/request/pull/1496) Parameters encoded to base 64 should be decoded as UTF-8, not ASCII. (@albanm)
- [#1494](https://github.com/request/request/pull/1494) Update eslint (@froatsnook)
- [#1474](https://github.com/request/request/pull/1474) Require Colon in Basic Auth (@erykwalder)
- [#1481](https://github.com/request/request/pull/1481) Fix baseUrl and redirections. (@burningtree)
- [#1469](https://github.com/request/request/pull/1469) Feature/base url (@froatsnook)
- [#1459](https://github.com/request/request/pull/1459) Add option to time request/response cycle (including rollup of redirects) (@aaron-em)
- [#1468](https://github.com/request/request/pull/1468) Re-enable io.js/node 0.12 build (@simov, @mikeal, @BBB)
- [#1442](https://github.com/request/request/pull/1442) Fixed the issue with strictSSL tests on  0.12 & io.js by explicitly setting a cipher that matches the cert. (@BBB, @nickmccurdy, @demohi, @simov, @0x4139)
- [#1460](https://github.com/request/request/pull/1460) localAddress or proxy config is lost when redirecting (@simov, @0x4139)
- [#1453](https://github.com/request/request/pull/1453) Test on Node.js 0.12 and io.js with allowed failures (@nickmccurdy, @demohi)
- [#1426](https://github.com/request/request/pull/1426) Fixing tests to pass on io.js and node 0.12 (only test-https.js stiff failing) (@mikeal)
- [#1446](https://github.com/request/request/pull/1446) Missing HTTP referer header with redirects Fixes #1038 (@simov, @guimon)
- [#1428](https://github.com/request/request/pull/1428) Deprecate Node v0.8.x (@nylen)
- [#1436](https://github.com/request/request/pull/1436) Add ability to set a requester without setting default options (@tikotzky)
- [#1435](https://github.com/request/request/pull/1435) dry up verb methods (@sethpollack)
- [#1423](https://github.com/request/request/pull/1423) Allow fully qualified multipart content-type header (@simov)
- [#1430](https://github.com/request/request/pull/1430) Fix recursive requester (@tikotzky)
- [#1429](https://github.com/request/request/pull/1429) Throw error when making HEAD request with a body (@tikotzky)
- [#1419](https://github.com/request/request/pull/1419) Add note that the project is broken in 0.12.x (@nylen)
- [#1413](https://github.com/request/request/pull/1413) Fix basic auth (@simov)
- [#1397](https://github.com/request/request/pull/1397) Improve pipe-from-file tests (@nylen)

### v2.53.0 (2015/02/02)
- [#1396](https://github.com/request/request/pull/1396) Do not rfc3986 escape JSON bodies (@nylen, @simov)
- [#1392](https://github.com/request/request/pull/1392) Improve `timeout` option description (@watson)

### v2.52.0 (2015/02/02)
- [#1383](https://github.com/request/request/pull/1383) Add missing HTTPS options that were not being passed to tunnel (@brichard19) (@nylen)
- [#1388](https://github.com/request/request/pull/1388) Upgrade mime-types package version (@roderickhsiao)
- [#1389](https://github.com/request/request/pull/1389) Revise Setup Tunnel Function (@seanstrom)
- [#1374](https://github.com/request/request/pull/1374) Allow explicitly disabling tunneling for proxied https destinations (@nylen)
- [#1376](https://github.com/request/request/pull/1376) Use karma-browserify for tests. Add browser test coverage reporter. (@eiriksm)
- [#1366](https://github.com/request/request/pull/1366) Refactor OAuth into separate module (@simov)
- [#1373](https://github.com/request/request/pull/1373) Rewrite tunnel test to be pure Node.js (@nylen)
- [#1371](https://github.com/request/request/pull/1371) Upgrade test reporter (@nylen)
- [#1360](https://github.com/request/request/pull/1360) Refactor basic, bearer, digest auth logic into separate class (@simov)
- [#1354](https://github.com/request/request/pull/1354) Remove circular dependency from debugging code (@nylen)
- [#1351](https://github.com/request/request/pull/1351) Move digest auth into private prototype method (@simov)
- [#1352](https://github.com/request/request/pull/1352) Update hawk dependency to ~2.3.0 (@mridgway)
- [#1353](https://github.com/request/request/pull/1353) Correct travis-ci badge (@dogancelik)
- [#1349](https://github.com/request/request/pull/1349) Make sure we return on errored browser requests. (@eiriksm)
- [#1346](https://github.com/request/request/pull/1346) getProxyFromURI Extraction Refactor (@seanstrom)
- [#1337](https://github.com/request/request/pull/1337) Standardize test ports on 6767 (@nylen)
- [#1341](https://github.com/request/request/pull/1341) Emit FormData error events as Request error events (@nylen, @rwky)
- [#1343](https://github.com/request/request/pull/1343) Clean up readme badges, and add Travis and Coveralls badges (@nylen)
- [#1345](https://github.com/request/request/pull/1345) Update README.md (@Aaron-Hartwig)
- [#1338](https://github.com/request/request/pull/1338) Always wait for server.close() callback in tests (@nylen)
- [#1342](https://github.com/request/request/pull/1342) Add mock https server and redo start of browser tests for this purpose. (@eiriksm)
- [#1339](https://github.com/request/request/pull/1339) Improve auth docs (@nylen)
- [#1335](https://github.com/request/request/pull/1335) Add support for OAuth plaintext signature method (@simov)
- [#1332](https://github.com/request/request/pull/1332) Add clean script to remove test-browser.js after the tests run (@seanstrom)
- [#1327](https://github.com/request/request/pull/1327) Fix errors generating coverage reports. (@nylen)
- [#1330](https://github.com/request/request/pull/1330) Return empty buffer upon empty response body and encoding is set to null (@seanstrom)
- [#1326](https://github.com/request/request/pull/1326) Use faster container-based infrastructure on Travis (@nylen)
- [#1315](https://github.com/request/request/pull/1315) Implement rfc3986 option (@simov, @nylen, @apoco, @DullReferenceException, @mmalecki, @oliamb, @cliffcrosland, @LewisJEllis, @eiriksm, @poislagarde)
- [#1314](https://github.com/request/request/pull/1314) Detect urlencoded form data header via regex (@simov)
- [#1317](https://github.com/request/request/pull/1317) Improve OAuth1.0 server side flow example (@simov)

### v2.51.0 (2014/12/10)
- [#1310](https://github.com/request/request/pull/1310) Revert changes introduced in https://github.com/request/request/pull/1282 (@simov)

### v2.50.0 (2014/12/09)
- [#1308](https://github.com/request/request/pull/1308) Add browser test to keep track of browserify compability. (@eiriksm)
- [#1299](https://github.com/request/request/pull/1299) Add optional support for jsonReviver (@poislagarde)
- [#1277](https://github.com/request/request/pull/1277) Add Coveralls configuration (@simov)
- [#1307](https://github.com/request/request/pull/1307) Upgrade form-data, add back browserify compability. Fixes #455. (@eiriksm)
- [#1305](https://github.com/request/request/pull/1305) Fix typo in README.md (@LewisJEllis)
- [#1288](https://github.com/request/request/pull/1288) Update README.md to explain custom file use case (@cliffcrosland)

### v2.49.0 (2014/11/28)
- [#1295](https://github.com/request/request/pull/1295) fix(proxy): no-proxy false positive (@oliamb)
- [#1292](https://github.com/request/request/pull/1292) Upgrade `caseless` to 0.8.1 (@mmalecki)
- [#1276](https://github.com/request/request/pull/1276) Set transfer encoding for multipart/related to chunked by default (@simov)
- [#1275](https://github.com/request/request/pull/1275) Fix multipart content-type headers detection (@simov)
- [#1269](https://github.com/request/request/pull/1269) adds streams example for review (@tbuchok)
- [#1238](https://github.com/request/request/pull/1238) Add examples README.md (@simov)

### v2.48.0 (2014/11/12)
- [#1263](https://github.com/request/request/pull/1263) Fixed a syntax error / typo in README.md (@xna2)
- [#1253](https://github.com/request/request/pull/1253) Add multipart chunked flag (@simov, @nylen)
- [#1251](https://github.com/request/request/pull/1251) Clarify that defaults() does not modify global defaults (@nylen)
- [#1250](https://github.com/request/request/pull/1250) Improve documentation for pool and maxSockets options (@nylen)
- [#1237](https://github.com/request/request/pull/1237) Documenting error handling when using streams (@vmattos)
- [#1244](https://github.com/request/request/pull/1244) Finalize changelog command (@nylen)
- [#1241](https://github.com/request/request/pull/1241) Fix typo (@alexanderGugel)
- [#1223](https://github.com/request/request/pull/1223) Show latest version number instead of "upcoming" in changelog (@nylen)
- [#1236](https://github.com/request/request/pull/1236) Document how to use custom CA in README (#1229) (@hypesystem)
- [#1228](https://github.com/request/request/pull/1228) Support for oauth with RSA-SHA1 signing (@nylen)
- [#1216](https://github.com/request/request/pull/1216) Made json and multipart options coexist (@nylen, @simov)
- [#1225](https://github.com/request/request/pull/1225) Allow header white/exclusive lists in any case. (@RReverser)

### v2.47.0 (2014/10/26)
- [#1222](https://github.com/request/request/pull/1222) Move from mikeal/request to request/request (@nylen)
- [#1220](https://github.com/request/request/pull/1220) update qs dependency to 2.3.1 (@FredKSchott)
- [#1212](https://github.com/request/request/pull/1212) Improve tests/test-timeout.js (@nylen)
- [#1219](https://github.com/request/request/pull/1219) remove old globalAgent workaround for node 0.4 (@request)
- [#1214](https://github.com/request/request/pull/1214) Remove cruft left over from optional dependencies (@nylen)
- [#1215](https://github.com/request/request/pull/1215) Add proxyHeaderExclusiveList option for proxy-only headers. (@RReverser)
- [#1211](https://github.com/request/request/pull/1211) Allow 'Host' header instead of 'host' and remember case across redirects (@nylen)
- [#1208](https://github.com/request/request/pull/1208) Improve release script (@nylen)
- [#1213](https://github.com/request/request/pull/1213) Support for custom cookie store (@nylen, @mitsuru)
- [#1197](https://github.com/request/request/pull/1197) Clean up some code around setting the agent (@FredKSchott)
- [#1209](https://github.com/request/request/pull/1209) Improve multipart form append test (@simov)
- [#1207](https://github.com/request/request/pull/1207) Update changelog (@nylen)
- [#1185](https://github.com/request/request/pull/1185) Stream multipart/related bodies (@simov)

### v2.46.0 (2014/10/23)
- [#1198](https://github.com/request/request/pull/1198) doc for TLS/SSL protocol options (@shawnzhu)
- [#1200](https://github.com/request/request/pull/1200) Add a Gitter chat badge to README.md (@gitter-badger)
- [#1196](https://github.com/request/request/pull/1196) Upgrade taper test reporter to v0.3.0 (@nylen)
- [#1199](https://github.com/request/request/pull/1199) Fix lint error: undeclared var i (@nylen)
- [#1191](https://github.com/request/request/pull/1191) Move self.proxy decision logic out of init and into a helper (@FredKSchott)
- [#1190](https://github.com/request/request/pull/1190) Move _buildRequest() logic back into init (@FredKSchott)
- [#1186](https://github.com/request/request/pull/1186) Support Smarter Unix URL Scheme (@FredKSchott)
- [#1178](https://github.com/request/request/pull/1178) update form documentation for new usage (@FredKSchott)
- [#1180](https://github.com/request/request/pull/1180) Enable no-mixed-requires linting rule (@nylen)
- [#1184](https://github.com/request/request/pull/1184) Don't forward authorization header across redirects to different hosts (@nylen)
- [#1183](https://github.com/request/request/pull/1183) Correct README about pre and postamble CRLF using multipart and not mult... (@netpoetica)
- [#1179](https://github.com/request/request/pull/1179) Lint tests directory (@nylen)
- [#1169](https://github.com/request/request/pull/1169) add metadata for form-data file field (@dotcypress)
- [#1173](https://github.com/request/request/pull/1173) remove optional dependencies (@seanstrom)
- [#1165](https://github.com/request/request/pull/1165) Cleanup event listeners and remove function creation from init (@FredKSchott)
- [#1174](https://github.com/request/request/pull/1174) update the request.cookie docs to have a valid cookie example (@seanstrom)
- [#1168](https://github.com/request/request/pull/1168) create a detach helper and use detach helper in replace of nextTick (@seanstrom)
- [#1171](https://github.com/request/request/pull/1171) in post can send form data and use callback (@MiroRadenovic)
- [#1159](https://github.com/request/request/pull/1159) accept charset for x-www-form-urlencoded content-type (@seanstrom)
- [#1157](https://github.com/request/request/pull/1157) Update README.md: body with json=true (@Rob--W)
- [#1164](https://github.com/request/request/pull/1164) Disable tests/test-timeout.js on Travis (@nylen)
- [#1153](https://github.com/request/request/pull/1153) Document how to run a single test (@nylen)
- [#1144](https://github.com/request/request/pull/1144) adds documentation for the "response" event within the streaming section (@tbuchok)
- [#1162](https://github.com/request/request/pull/1162) Update eslintrc file to no longer allow past errors (@FredKSchott)
- [#1155](https://github.com/request/request/pull/1155) Support/use self everywhere (@seanstrom)
- [#1161](https://github.com/request/request/pull/1161) fix no-use-before-define lint warnings (@emkay)
- [#1156](https://github.com/request/request/pull/1156) adding curly brackets to get rid of lint errors (@emkay)
- [#1151](https://github.com/request/request/pull/1151) Fix localAddress test on OS X (@nylen)
- [#1145](https://github.com/request/request/pull/1145) documentation: fix outdated reference to setCookieSync old name in README (@FredKSchott)
- [#1131](https://github.com/request/request/pull/1131) Update pool documentation (@FredKSchott)
- [#1143](https://github.com/request/request/pull/1143) Rewrite all tests to use tape (@nylen)
- [#1137](https://github.com/request/request/pull/1137) Add ability to specifiy querystring lib in options. (@jgrund)
- [#1138](https://github.com/request/request/pull/1138) allow hostname and port in place of host on uri (@cappslock)
- [#1134](https://github.com/request/request/pull/1134) Fix multiple redirects and `self.followRedirect` (@blakeembrey)
- [#1130](https://github.com/request/request/pull/1130) documentation fix: add note about npm test for contributing (@FredKSchott)
- [#1120](https://github.com/request/request/pull/1120) Support/refactor request setup tunnel (@seanstrom)
- [#1129](https://github.com/request/request/pull/1129) linting fix: convert double quote strings to use single quotes (@FredKSchott)
- [#1124](https://github.com/request/request/pull/1124) linting fix: remove unneccesary semi-colons (@FredKSchott)

### v2.45.0 (2014/10/06)
- [#1128](https://github.com/request/request/pull/1128) Add test for setCookie regression (@nylen)
- [#1127](https://github.com/request/request/pull/1127) added tests around using objects as values in a query string (@bcoe)
- [#1103](https://github.com/request/request/pull/1103) Support/refactor request constructor (@nylen, @seanstrom)
- [#1119](https://github.com/request/request/pull/1119) add basic linting to request library (@FredKSchott)
- [#1121](https://github.com/request/request/pull/1121) Revert "Explicitly use sync versions of cookie functions" (@nylen)
- [#1118](https://github.com/request/request/pull/1118) linting fix: Restructure bad empty if statement (@FredKSchott)
- [#1117](https://github.com/request/request/pull/1117) Fix a bad check for valid URIs (@FredKSchott)
- [#1113](https://github.com/request/request/pull/1113) linting fix: space out operators (@FredKSchott)
- [#1116](https://github.com/request/request/pull/1116) Fix typo in `noProxyHost` definition (@FredKSchott)
- [#1114](https://github.com/request/request/pull/1114) linting fix: Added a `new` operator that was missing when creating and throwing a new error (@FredKSchott)
- [#1096](https://github.com/request/request/pull/1096) No_proxy support (@samcday)
- [#1107](https://github.com/request/request/pull/1107) linting-fix: remove unused variables (@FredKSchott)
- [#1112](https://github.com/request/request/pull/1112) linting fix: Make return values consistent and more straitforward (@FredKSchott)
- [#1111](https://github.com/request/request/pull/1111) linting fix: authPieces was getting redeclared (@FredKSchott)
- [#1105](https://github.com/request/request/pull/1105) Use strict mode in request (@FredKSchott)
- [#1110](https://github.com/request/request/pull/1110) linting fix: replace lazy '==' with more strict '===' (@FredKSchott)
- [#1109](https://github.com/request/request/pull/1109) linting fix: remove function call from if-else conditional statement (@FredKSchott)
- [#1102](https://github.com/request/request/pull/1102) Fix to allow setting a `requester` on recursive calls to `request.defaults` (@tikotzky)
- [#1095](https://github.com/request/request/pull/1095) Tweaking engines in package.json (@pdehaan)
- [#1082](https://github.com/request/request/pull/1082) Forward the socket event from the httpModule request (@seanstrom)
- [#972](https://github.com/request/request/pull/972) Clarify gzip handling in the README (@kevinoid)
- [#1089](https://github.com/request/request/pull/1089) Mention that encoding defaults to utf8, not Buffer (@stuartpb)
- [#1088](https://github.com/request/request/pull/1088) Fix cookie example in README.md and make it more clear (@pipi32167)
- [#1027](https://github.com/request/request/pull/1027) Add support for multipart form data in request options. (@crocket)
- [#1076](https://github.com/request/request/pull/1076) use Request.abort() to abort the request when the request has timed-out (@seanstrom)
- [#1068](https://github.com/request/request/pull/1068) add optional postamble required by .NET multipart requests (@netpoetica)

### v2.43.0 (2014/09/18)
- [#1057](https://github.com/request/request/pull/1057) Defaults should not overwrite defined options (@davidwood)
- [#1046](https://github.com/request/request/pull/1046) Propagate datastream errors, useful in case gzip fails. (@ZJONSSON, @Janpot)
- [#1063](https://github.com/request/request/pull/1063) copy the input headers object #1060 (@finnp)
- [#1031](https://github.com/request/request/pull/1031) Explicitly use sync versions of cookie functions (@ZJONSSON)
- [#1056](https://github.com/request/request/pull/1056) Fix redirects when passing url.parse(x) as URL to convenience method (@nylen)

### v2.42.0 (2014/09/04)
- [#1053](https://github.com/request/request/pull/1053) Fix #1051 Parse auth properly when using non-tunneling proxy (@isaacs)

### v2.41.0 (2014/09/04)
- [#1050](https://github.com/request/request/pull/1050) Pass whitelisted headers to tunneling proxy.  Organize all tunneling logic. (@isaacs, @Feldhacker)
- [#1035](https://github.com/request/request/pull/1035) souped up nodei.co badge (@rvagg)
- [#1048](https://github.com/request/request/pull/1048) Aws is now possible over a proxy (@steven-aerts)
- [#1039](https://github.com/request/request/pull/1039) extract out helper functions to a helper file (@seanstrom)
- [#1021](https://github.com/request/request/pull/1021) Support/refactor indexjs (@seanstrom)
- [#1033](https://github.com/request/request/pull/1033) Improve and document debug options (@nylen)
- [#1034](https://github.com/request/request/pull/1034) Fix readme headings (@nylen)
- [#1030](https://github.com/request/request/pull/1030) Allow recursive request.defaults (@tikotzky)
- [#1029](https://github.com/request/request/pull/1029) Fix a couple of typos (@nylen)
- [#675](https://github.com/request/request/pull/675) Checking for SSL fault on connection before reading SSL properties (@VRMink)
- [#989](https://github.com/request/request/pull/989) Added allowRedirect function. Should return true if redirect is allowed or false otherwise (@doronin)
- [#1025](https://github.com/request/request/pull/1025) [fixes #1023] Set self._ended to true once response has ended (@mridgway)
- [#1020](https://github.com/request/request/pull/1020) Add back removed debug metadata (@FredKSchott)
- [#1008](https://github.com/request/request/pull/1008) Moving to  module instead of cutomer buffer concatenation. (@mikeal)
- [#770](https://github.com/request/request/pull/770) Added dependency badge for README file; (@timgluz, @mafintosh, @lalitkapoor, @stash, @bobyrizov)
- [#1016](https://github.com/request/request/pull/1016) toJSON no longer results in an infinite loop, returns simple objects (@FredKSchott)
- [#1018](https://github.com/request/request/pull/1018) Remove pre-0.4.4 HTTPS fix (@mmalecki)
- [#1006](https://github.com/request/request/pull/1006) Migrate to caseless, fixes #1001 (@mikeal)
- [#995](https://github.com/request/request/pull/995) Fix parsing array of objects (@sjonnet19)
- [#999](https://github.com/request/request/pull/999) Fix fallback for browserify for optional modules. (@eiriksm)
- [#996](https://github.com/request/request/pull/996) Wrong oauth signature when multiple same param keys exist [updated] (@bengl)

### v2.40.0 (2014/08/06)
- [#992](https://github.com/request/request/pull/992) Fix security vulnerability. Update qs (@poeticninja)
- [#988](https://github.com/request/request/pull/988) “--” -> “—” (@upisfree)
- [#987](https://github.com/request/request/pull/987) Show optional modules as being loaded by the module that reqeusted them (@iarna)

### v2.39.0 (2014/07/24)
- [#976](https://github.com/request/request/pull/976) Update README.md (@pvoznenko)

### v2.38.0 (2014/07/22)
- [#952](https://github.com/request/request/pull/952) Adding support to client certificate with proxy use case (@ofirshaked)
- [#884](https://github.com/request/request/pull/884) Documented tough-cookie installation. (@wbyoung)
- [#935](https://github.com/request/request/pull/935) Correct repository url (@fritx)
- [#963](https://github.com/request/request/pull/963) Update changelog (@nylen)
- [#960](https://github.com/request/request/pull/960) Support gzip with encoding on node pre-v0.9.4 (@kevinoid)
- [#953](https://github.com/request/request/pull/953) Add async Content-Length computation when using form-data (@LoicMahieu)
- [#844](https://github.com/request/request/pull/844) Add support for HTTP[S]_PROXY environment variables.  Fixes #595. (@jvmccarthy)
- [#946](https://github.com/request/request/pull/946) defaults: merge headers (@aj0strow)

### v2.37.0 (2014/07/07)
- [#957](https://github.com/request/request/pull/957) Silence EventEmitter memory leak warning #311 (@watson)
- [#955](https://github.com/request/request/pull/955) check for content-length header before setting it in nextTick (@camilleanne)
- [#951](https://github.com/request/request/pull/951) Add support for gzip content decoding (@kevinoid)
- [#949](https://github.com/request/request/pull/949) Manually enter querystring in form option (@charlespwd)
- [#944](https://github.com/request/request/pull/944) Make request work with browserify (@eiriksm)
- [#943](https://github.com/request/request/pull/943) New mime module (@eiriksm)
- [#927](https://github.com/request/request/pull/927) Bump version of hawk dep. (@samccone)
- [#907](https://github.com/request/request/pull/907) append secureOptions to poolKey (@medovob)

### v2.35.0 (2014/05/17)
- [#901](https://github.com/request/request/pull/901) Fixes #555 (@pigulla)
- [#897](https://github.com/request/request/pull/897) merge with default options (@vohof)
- [#891](https://github.com/request/request/pull/891) fixes 857 - options object is mutated by calling request (@lalitkapoor)
- [#869](https://github.com/request/request/pull/869) Pipefilter test (@tgohn)
- [#866](https://github.com/request/request/pull/866) Fix typo (@dandv)
- [#861](https://github.com/request/request/pull/861) Add support for RFC 6750 Bearer Tokens (@phedny)
- [#809](https://github.com/request/request/pull/809) upgrade tunnel-proxy to 0.4.0 (@ksato9700)
- [#850](https://github.com/request/request/pull/850) Fix word consistency in readme (@0xNobody)
- [#810](https://github.com/request/request/pull/810) add some exposition to mpu example in README.md (@mikermcneil)
- [#840](https://github.com/request/request/pull/840) improve error reporting for invalid protocols (@FND)
- [#821](https://github.com/request/request/pull/821) added secureOptions back (@nw)
- [#815](https://github.com/request/request/pull/815) Create changelog based on pull requests (@lalitkapoor)

### v2.34.0 (2014/02/18)
- [#516](https://github.com/request/request/pull/516) UNIX Socket URL Support (@lyuzashi)
- [#801](https://github.com/request/request/pull/801) 794 ignore cookie parsing and domain errors (@lalitkapoor)
- [#802](https://github.com/request/request/pull/802) Added the Apache license to the package.json. (@keskival)
- [#793](https://github.com/request/request/pull/793) Adds content-length calculation when submitting forms using form-data li... (@Juul)
- [#785](https://github.com/request/request/pull/785) Provide ability to override content-type when `json` option used (@vvo)
- [#781](https://github.com/request/request/pull/781) simpler isReadStream function (@joaojeronimo)

### v2.32.0 (2014/01/16)
- [#767](https://github.com/request/request/pull/767) Use tough-cookie CookieJar sync API (@stash)
- [#764](https://github.com/request/request/pull/764) Case-insensitive authentication scheme (@bobyrizov)
- [#763](https://github.com/request/request/pull/763) Upgrade tough-cookie to 0.10.0 (@stash)
- [#744](https://github.com/request/request/pull/744) Use Cookie.parse (@lalitkapoor)
- [#757](https://github.com/request/request/pull/757) require aws-sign2 (@mafintosh)

### v2.31.0 (2014/01/08)
- [#645](https://github.com/request/request/pull/645) update twitter api url to v1.1 (@mick)
- [#746](https://github.com/request/request/pull/746) README: Markdown code highlight (@weakish)
- [#745](https://github.com/request/request/pull/745) updating setCookie example to make it clear that the callback is required (@emkay)
- [#742](https://github.com/request/request/pull/742) Add note about JSON output body type (@iansltx)
- [#741](https://github.com/request/request/pull/741) README example is using old cookie jar api (@emkay)
- [#736](https://github.com/request/request/pull/736) Fix callback arguments documentation (@mmalecki)
- [#732](https://github.com/request/request/pull/732) JSHINT: Creating global 'for' variable. Should be 'for (var ...'. (@Fritz-Lium)
- [#730](https://github.com/request/request/pull/730) better HTTP DIGEST support (@dai-shi)
- [#728](https://github.com/request/request/pull/728) Fix TypeError when calling request.cookie (@scarletmeow)
- [#727](https://github.com/request/request/pull/727) fix requester bug (@jchris)
- [#724](https://github.com/request/request/pull/724) README.md: add custom HTTP Headers example. (@tcort)
- [#719](https://github.com/request/request/pull/719) Made a comment gender neutral. (@unsetbit)
- [#715](https://github.com/request/request/pull/715) Request.multipart no longer crashes when header 'Content-type' present (@pastaclub)
- [#710](https://github.com/request/request/pull/710) Fixing listing in callback part of docs. (@lukasz-zak)
- [#696](https://github.com/request/request/pull/696) Edited README.md for formatting and clarity of phrasing (@Zearin)
- [#694](https://github.com/request/request/pull/694) Typo in README (@VRMink)
- [#690](https://github.com/request/request/pull/690) Handle blank password in basic auth. (@diversario)
- [#682](https://github.com/request/request/pull/682) Optional dependencies (@Turbo87)
- [#683](https://github.com/request/request/pull/683) Travis CI support (@Turbo87)
- [#674](https://github.com/request/request/pull/674) change cookie module,to tough-cookie.please check it . (@sxyizhiren)
- [#666](https://github.com/request/request/pull/666) make `ciphers` and `secureProtocol` to work in https request (@richarddong)
- [#656](https://github.com/request/request/pull/656) Test case for #304. (@diversario)
- [#662](https://github.com/request/request/pull/662) option.tunnel to explicitly disable tunneling (@seanmonstar)
- [#659](https://github.com/request/request/pull/659) fix failure when running with NODE_DEBUG=request, and a test for that (@jrgm)
- [#630](https://github.com/request/request/pull/630) Send random cnonce for HTTP Digest requests (@wprl)
- [#619](https://github.com/request/request/pull/619) decouple things a bit (@joaojeronimo)
- [#613](https://github.com/request/request/pull/613) Fixes #583, moved initialization of self.uri.pathname (@lexander)
- [#605](https://github.com/request/request/pull/605) Only include ":" + pass in Basic Auth if it's defined (fixes #602) (@bendrucker)
- [#596](https://github.com/request/request/pull/596) Global agent is being used when pool is specified (@Cauldrath)
- [#594](https://github.com/request/request/pull/594) Emit complete event when there is no callback (@RomainLK)
- [#601](https://github.com/request/request/pull/601) Fixed a small typo (@michalstanko)
- [#589](https://github.com/request/request/pull/589) Prevent setting headers after they are sent (@geek)
- [#587](https://github.com/request/request/pull/587) Global cookie jar disabled by default (@threepointone)
- [#544](https://github.com/request/request/pull/544) Update http-signature version. (@davidlehn)
- [#581](https://github.com/request/request/pull/581) Fix spelling of "ignoring." (@bigeasy)
- [#568](https://github.com/request/request/pull/568) use agentOptions to create agent when specified in request (@SamPlacette)
- [#564](https://github.com/request/request/pull/564) Fix redirections (@criloz)
- [#541](https://github.com/request/request/pull/541) The exported request function doesn't have an auth method (@tschaub)
- [#542](https://github.com/request/request/pull/542) Expose Request class (@regality)
- [#536](https://github.com/request/request/pull/536) Allow explicitly empty user field for basic authentication. (@mikeando)
- [#532](https://github.com/request/request/pull/532) fix typo (@fredericosilva)
- [#497](https://github.com/request/request/pull/497) Added redirect event (@Cauldrath)
- [#503](https://github.com/request/request/pull/503) Fix basic auth for passwords that contain colons (@tonistiigi)
- [#521](https://github.com/request/request/pull/521) Improving test-localAddress.js (@noway)
- [#529](https://github.com/request/request/pull/529) dependencies versions bump (@jodaka)
- [#523](https://github.com/request/request/pull/523) Updating dependencies (@noway)
- [#520](https://github.com/request/request/pull/520) Fixing test-tunnel.js (@noway)
- [#519](https://github.com/request/request/pull/519) Update internal path state on post-creation QS changes (@jblebrun)
- [#510](https://github.com/request/request/pull/510) Add HTTP Signature support. (@davidlehn)
- [#502](https://github.com/request/request/pull/502) Fix POST (and probably other) requests that are retried after 401 Unauthorized (@nylen)
- [#508](https://github.com/request/request/pull/508) Honor the .strictSSL option when using proxies (tunnel-agent) (@jhs)
- [#512](https://github.com/request/request/pull/512) Make password optional to support the format: http://username@hostname/ (@pajato1)
- [#513](https://github.com/request/request/pull/513) add 'localAddress' support (@yyfrankyy)
- [#498](https://github.com/request/request/pull/498) Moving response emit above setHeaders on destination streams (@kenperkins)
- [#490](https://github.com/request/request/pull/490) Empty response body (3-rd argument) must be passed to callback as an empty string (@Olegas)
- [#479](https://github.com/request/request/pull/479) Changing so if Accept header is explicitly set, sending json does not ov... (@RoryH)
- [#475](https://github.com/request/request/pull/475) Use `unescape` from `querystring` (@shimaore)
- [#473](https://github.com/request/request/pull/473) V0.10 compat (@isaacs)
- [#471](https://github.com/request/request/pull/471) Using querystring library from visionmedia (@kbackowski)
- [#461](https://github.com/request/request/pull/461) Strip the UTF8 BOM from a UTF encoded response (@kppullin)
- [#460](https://github.com/request/request/pull/460) hawk 0.10.0 (@hueniverse)
- [#462](https://github.com/request/request/pull/462) if query params are empty, then request path shouldn't end with a '?' (merges cleanly now) (@jaipandya)
- [#456](https://github.com/request/request/pull/456) hawk 0.9.0 (@hueniverse)
- [#429](https://github.com/request/request/pull/429) Copy options before adding callback. (@nrn, @nfriedly, @youurayy, @jplock, @kapetan, @landeiro, @othiym23, @mmalecki)
- [#454](https://github.com/request/request/pull/454) Destroy the response if present when destroying the request (clean merge) (@mafintosh)
- [#310](https://github.com/request/request/pull/310) Twitter Oauth Stuff Out of Date; Now Updated (@joemccann, @isaacs, @mscdex)
- [#413](https://github.com/request/request/pull/413) rename googledoodle.png to .jpg (@nfriedly, @youurayy, @jplock, @kapetan, @landeiro, @othiym23, @mmalecki)
- [#448](https://github.com/request/request/pull/448) Convenience method for PATCH (@mloar)
- [#444](https://github.com/request/request/pull/444) protect against double callbacks on error path (@spollack)
- [#433](https://github.com/request/request/pull/433) Added support for HTTPS cert & key (@mmalecki)
- [#430](https://github.com/request/request/pull/430) Respect specified {Host,host} headers, not just {host} (@andrewschaaf)
- [#415](https://github.com/request/request/pull/415) Fixed a typo. (@jerem)
- [#338](https://github.com/request/request/pull/338) Add more auth options, including digest support (@nylen)
- [#403](https://github.com/request/request/pull/403) Optimize environment lookup to happen once only (@mmalecki)
- [#398](https://github.com/request/request/pull/398) Add more reporting to tests (@mmalecki)
- [#388](https://github.com/request/request/pull/388) Ensure "safe" toJSON doesn't break EventEmitters (@othiym23)
- [#381](https://github.com/request/request/pull/381) Resolving "Invalid signature. Expected signature base string: " (@landeiro)
- [#380](https://github.com/request/request/pull/380) Fixes missing host header on retried request when using forever agent (@mac-)
- [#376](https://github.com/request/request/pull/376) Headers lost on redirect (@kapetan)
- [#375](https://github.com/request/request/pull/375) Fix for missing oauth_timestamp parameter (@jplock)
- [#374](https://github.com/request/request/pull/374) Correct Host header for proxy tunnel CONNECT (@youurayy)
- [#370](https://github.com/request/request/pull/370) Twitter reverse auth uses x_auth_mode not x_auth_type (@drudge)
- [#369](https://github.com/request/request/pull/369) Don't remove x_auth_mode for Twitter reverse auth (@drudge)
- [#344](https://github.com/request/request/pull/344) Make AWS auth signing find headers correctly (@nlf)
- [#363](https://github.com/request/request/pull/363) rfc3986 on base_uri, now passes tests (@jeffmarshall)
- [#362](https://github.com/request/request/pull/362) Running `rfc3986` on `base_uri` in `oauth.hmacsign` instead of just `encodeURIComponent` (@jeffmarshall)
- [#361](https://github.com/request/request/pull/361) Don't create a Content-Length header if we already have it set (@danjenkins)
- [#360](https://github.com/request/request/pull/360) Delete self._form along with everything else on redirect (@jgautier)
- [#355](https://github.com/request/request/pull/355) stop sending erroneous headers on redirected requests (@azylman)
- [#332](https://github.com/request/request/pull/332) Fix #296 - Only set Content-Type if body exists (@Marsup)
- [#343](https://github.com/request/request/pull/343) Allow AWS to work in more situations, added a note in the README on its usage (@nlf)
- [#320](https://github.com/request/request/pull/320) request.defaults() doesn't need to wrap jar() (@StuartHarris)
- [#322](https://github.com/request/request/pull/322) Fix + test for piped into request bumped into redirect. #321 (@alexindigo)
- [#326](https://github.com/request/request/pull/326) Do not try to remove listener from an undefined connection (@CartoDB)
- [#318](https://github.com/request/request/pull/318) Pass servername to tunneling secure socket creation (@isaacs)
- [#317](https://github.com/request/request/pull/317) Workaround for #313 (@isaacs)
- [#293](https://github.com/request/request/pull/293) Allow parser errors to bubble up to request (@mscdex)
- [#290](https://github.com/request/request/pull/290) A test for #289 (@isaacs)
- [#280](https://github.com/request/request/pull/280) Like in node.js print options if NODE_DEBUG contains the word request (@Filirom1)
- [#207](https://github.com/request/request/pull/207) Fix #206 Change HTTP/HTTPS agent when redirecting between protocols (@isaacs)
- [#214](https://github.com/request/request/pull/214) documenting additional behavior of json option (@jphaas, @vpulim)
- [#272](https://github.com/request/request/pull/272) Boundary begins with CRLF? (@elspoono, @timshadel, @naholyr, @nanodocumet, @TehShrike)
- [#284](https://github.com/request/request/pull/284) Remove stray `console.log()` call in multipart generator. (@bcherry)
- [#241](https://github.com/request/request/pull/241) Composability updates suggested by issue #239 (@polotek)
- [#282](https://github.com/request/request/pull/282) OAuth Authorization header contains non-"oauth_" parameters (@jplock)
- [#279](https://github.com/request/request/pull/279) fix tests with boundary by injecting boundry from header (@benatkin)
- [#273](https://github.com/request/request/pull/273) Pipe back pressure issue (@mafintosh)
- [#268](https://github.com/request/request/pull/268) I'm not OCD seriously (@TehShrike)
- [#263](https://github.com/request/request/pull/263) Bug in OAuth key generation for sha1 (@nanodocumet)
- [#265](https://github.com/request/request/pull/265) uncaughtException when redirected to invalid URI (@naholyr)
- [#262](https://github.com/request/request/pull/262) JSON test should check for equality (@timshadel)
- [#261](https://github.com/request/request/pull/261) Setting 'pool' to 'false' does NOT disable Agent pooling (@timshadel)
- [#249](https://github.com/request/request/pull/249) Fix for the fix of your (closed) issue #89 where self.headers[content-length] is set to 0 for all methods (@sethbridges, @polotek, @zephrax, @jeromegn)
- [#255](https://github.com/request/request/pull/255) multipart allow body === '' ( the empty string ) (@Filirom1)
- [#260](https://github.com/request/request/pull/260) fixed just another leak of 'i' (@sreuter)
- [#246](https://github.com/request/request/pull/246) Fixing the set-cookie header (@jeromegn)
- [#243](https://github.com/request/request/pull/243) Dynamic boundary (@zephrax)
- [#240](https://github.com/request/request/pull/240) don't error when null is passed for options (@polotek)
- [#211](https://github.com/request/request/pull/211) Replace all occurrences of special chars in RFC3986 (@chriso, @vpulim)
- [#224](https://github.com/request/request/pull/224) Multipart content-type change (@janjongboom)
- [#217](https://github.com/request/request/pull/217) need to use Authorization (titlecase) header with Tumblr OAuth (@visnup)
- [#203](https://github.com/request/request/pull/203) Fix cookie and redirect bugs and add auth support for HTTPS tunnel (@vpulim)
- [#199](https://github.com/request/request/pull/199) Tunnel (@isaacs)
- [#198](https://github.com/request/request/pull/198) Bugfix on forever usage of util.inherits (@isaacs)
- [#197](https://github.com/request/request/pull/197) Make ForeverAgent work with HTTPS (@isaacs)
- [#193](https://github.com/request/request/pull/193) Fixes GH-119 (@goatslacker)
- [#188](https://github.com/request/request/pull/188) Add abort support to the returned request (@itay)
- [#176](https://github.com/request/request/pull/176) Querystring option (@csainty)
- [#182](https://github.com/request/request/pull/182) Fix request.defaults to support (uri, options, callback) api (@twilson63)
- [#180](https://github.com/request/request/pull/180) Modified the post, put, head and del shortcuts to support uri optional param (@twilson63)
- [#179](https://github.com/request/request/pull/179) fix to add opts in .pipe(stream, opts) (@substack)
- [#177](https://github.com/request/request/pull/177) Issue #173 Support uri as first and optional config as second argument (@twilson63)
- [#170](https://github.com/request/request/pull/170) can't create a cookie in a wrapped request (defaults) (@fabianonunes)
- [#168](https://github.com/request/request/pull/168) Picking off an EasyFix by adding some missing mimetypes. (@serby)
- [#161](https://github.com/request/request/pull/161) Fix cookie jar/headers.cookie collision (#125) (@papandreou)
- [#162](https://github.com/request/request/pull/162) Fix issue #159 (@dpetukhov)
- [#90](https://github.com/request/request/pull/90) add option followAllRedirects to follow post/put redirects (@jroes)
- [#148](https://github.com/request/request/pull/148) Retry Agent (@thejh)
- [#146](https://github.com/request/request/pull/146) Multipart should respect content-type if previously set (@apeace)
- [#144](https://github.com/request/request/pull/144) added "form" option to readme (@petejkim)
- [#133](https://github.com/request/request/pull/133) Fixed cookies parsing (@afanasy)
- [#135](https://github.com/request/request/pull/135) host vs hostname (@iangreenleaf)
- [#132](https://github.com/request/request/pull/132) return the body as a Buffer when encoding is set to null (@jahewson)
- [#112](https://github.com/request/request/pull/112) Support using a custom http-like module (@jhs)
- [#104](https://github.com/request/request/pull/104) Cookie handling contains bugs (@janjongboom)
- [#121](https://github.com/request/request/pull/121) Another patch for cookie handling regression (@jhurliman)
- [#117](https://github.com/request/request/pull/117) Remove the global `i` (@3rd-Eden)
- [#110](https://github.com/request/request/pull/110) Update to Iris Couch URL (@jhs)
- [#86](https://github.com/request/request/pull/86) Can't post binary to multipart requests (@kkaefer)
- [#105](https://github.com/request/request/pull/105) added test for proxy option. (@dominictarr)
- [#102](https://github.com/request/request/pull/102) Implemented cookies - closes issue 82: https://github.com/mikeal/request/issues/82 (@alessioalex)
- [#97](https://github.com/request/request/pull/97) Typo in previous pull causes TypeError in non-0.5.11 versions (@isaacs)
- [#96](https://github.com/request/request/pull/96) Authless parsed url host support (@isaacs)
- [#81](https://github.com/request/request/pull/81) Enhance redirect handling (@danmactough)
- [#78](https://github.com/request/request/pull/78) Don't try to do strictSSL for non-ssl connections (@isaacs)
- [#76](https://github.com/request/request/pull/76) Bug when a request fails and a timeout is set (@Marsup)
- [#70](https://github.com/request/request/pull/70) add test script to package.json (@isaacs, @aheckmann)
- [#73](https://github.com/request/request/pull/73) Fix #71 Respect the strictSSL flag (@isaacs)
- [#69](https://github.com/request/request/pull/69) Flatten chunked requests properly (@isaacs)
- [#67](https://github.com/request/request/pull/67) fixed global variable leaks (@aheckmann)
- [#66](https://github.com/request/request/pull/66) Do not overwrite established content-type headers for read stream deliver (@voodootikigod)
- [#53](https://github.com/request/request/pull/53) Parse json: Issue #51 (@benatkin)
- [#45](https://github.com/request/request/pull/45) Added timeout option (@mbrevoort)
- [#35](https://github.com/request/request/pull/35) The "end" event isn't emitted for some responses (@voxpelli)
- [#31](https://github.com/request/request/pull/31) Error on piping a request to a destination (@tobowers)