/** @public */
export interface MinKeyExtended {
  $minKey: 1;
}

/**
 * A class representation of the BSON MinKey type.
 * @public
 * @category BSONType
 */
export class MinKey {
  _bsontype!: 'MinKey';

  constructor() {
    if (!(this instanceof MinKey)) return new <PERSON><PERSON><PERSON>();
  }

  /** @internal */
  toExtendedJSON(): MinKeyExtended {
    return { $minKey: 1 };
  }

  /** @internal */
  static fromExtendedJSON(): <PERSON><PERSON><PERSON> {
    return new MinK<PERSON>();
  }

  /** @internal */
  [Symbol.for('nodejs.util.inspect.custom')](): string {
    return this.inspect();
  }

  inspect(): string {
    return 'new <PERSON>Key()';
  }
}

Object.defineProperty(MinKey.prototype, '_bsontype', { value: 'MinKey' });
