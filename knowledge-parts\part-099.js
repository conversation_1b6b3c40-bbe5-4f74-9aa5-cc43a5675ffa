// 知识库上传脚本 - 第99部分 (共99部分)

  console.log(`✅ 成功: ${successCount} 批`);
  console.log(`❌ 失败: ${failCount} 批`);
  console.log(`📁 总文件数: ${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)}`);
}

// 检查上传状态
async function checkUploadStatus() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: {
        action: 'count'
      }
    });
    
    if (result.result.success) {
      console.log(`📊 当前云端知识库统计:`);
      console.log(`📁 总记录数: ${result.result.total}`);
      console.log(`📄 示例记录:`, result.result.sample);
    }
    
    return result.result;
  } catch (error) {
    console.error('检查状态失败:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.uploadBatch = uploadBatch;
  window.uploadAllBatches = uploadAllBatches;
  window.checkUploadStatus = checkUploadStatus;
  window.ALL_BATCHES = ALL_BATCHES;
  
  console.log('📚 知识库上传脚本已加载！');
  console.log(`📊 数据统计: ${ALL_BATCHES.length} 批，共 ${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)} 个文件`);
  console.log('');
  console.log('🔧 可用命令:');
  console.log('  checkUploadStatus() - 检查当前上传状态');
  console.log('  uploadBatch(n) - 上传第n批数据');
  console.log('  uploadAllBatches() - 上传所有批次');
  console.log('');
}
