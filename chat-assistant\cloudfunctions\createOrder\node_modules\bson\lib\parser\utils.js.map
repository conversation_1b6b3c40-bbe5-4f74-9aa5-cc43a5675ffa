{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/parser/utils.ts"], "names": [], "mappings": ";;;AAAA,iCAAgC;AAChC,0CAA4C;AAI5C;;;GAGG;AACH,SAAgB,wBAAwB,CAAC,EAAY;IACnD,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC1D,CAAC;AAFD,4DAEC;AAED,SAAS,aAAa;IACpB,IAAM,CAAC,GAAG,IAAA,kBAAS,GAAwC,CAAC;IAC5D,OAAO,OAAO,CAAC,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,KAAK,aAAa,CAAC;AAClF,CAAC;AAED,IAAM,mBAAmB,GAAwB,SAAS,mBAAmB,CAAC,IAAY;IACxF,IAAM,eAAe,GAAG,aAAa,EAAE;QACrC,CAAC,CAAC,0IAA0I;QAC5I,CAAC,CAAC,+GAA+G,CAAC;IACpH,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAE9B,IAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC;QAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IAC3E,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAWF,IAAM,iBAAiB,GAAG;IACxB,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,mCAAmC;YACnC,IAAM,QAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,iBAAiB;YAClE,IAAI,QAAM,IAAI,QAAM,CAAC,eAAe,EAAE;gBACpC,OAAO,UAAA,IAAI,IAAI,OAAA,QAAM,CAAC,eAAe,CAAC,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAA1C,CAA0C,CAAC;aAC3D;SACF;QAED,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;YACnF,gHAAgH;YAChH,OAAO,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAjD,CAAiD,CAAC;SAClE;QAED,OAAO,mBAAmB,CAAC;KAC5B;SAAM;QACL,IAAI,mBAAmB,SAAwC,CAAC;QAChE,IAAI;YACF,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC;SACrD;QAAC,OAAO,CAAC,EAAE;YACV,oBAAoB;SACrB;QAED,0EAA0E;QAE1E,OAAO,mBAAmB,IAAI,mBAAmB,CAAC;KACnD;AACH,CAAC,CAAC;AAEW,QAAA,WAAW,GAAG,iBAAiB,EAAE,CAAC;AAE/C,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,OAAO,CAAC,sBAAsB,EAAE,4BAA4B,CAAC,CAAC,QAAQ,CACpE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CACtC,CAAC;AACJ,CAAC;AAJD,4CAIC;AAED,SAAgB,YAAY,CAAC,KAAc;IACzC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,qBAAqB,CAAC;AACzE,CAAC;AAFD,oCAEC;AAED,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,wBAAwB,CAAC;AAC5E,CAAC;AAFD,0CAEC;AAED,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,yBAAyB,CAAC;AAC7E,CAAC;AAFD,4CAEC;AAED,SAAgB,QAAQ,CAAC,CAAU;IACjC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC;AACjE,CAAC;AAFD,4BAEC;AAED,SAAgB,KAAK,CAAC,CAAU;IAC9B,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC;AAC9D,CAAC;AAFD,sBAEC;AAED,qDAAqD;AACrD,SAAgB,UAAU;IACxB,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC;AAC/E,CAAC;AAFD,gCAEC;AAED,6CAA6C;AAC7C,SAAgB,MAAM,CAAC,CAAU;IAC/B,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC;AAClF,CAAC;AAFD,wBAEC;AAED;;;;GAIG;AACH,SAAgB,YAAY,CAAC,SAAkB;IAC7C,OAAO,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,CAAC;AAC7D,CAAC;AAFD,oCAEC;AAGD,SAAgB,SAAS,CAAqB,EAAK,EAAE,OAAe;IAClE,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,SAAS,UAAU;QAAgB,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACnD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,GAAG,IAAI,CAAC;SACf;QACD,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,UAA0B,CAAC;AACpC,CAAC;AAVD,8BAUC"}