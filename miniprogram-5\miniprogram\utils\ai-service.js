// AI分析服务模块 - 集成DeepSeek API
const DEEPSEEK_API_KEY = '***********************************';
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

// 导入知识库模块
const { getRelevantKnowledge, searchKnowledge } = require('./knowledge-base.js');

/**
 * 调用DeepSeek API进行AI分析
 * @param {string} prompt - 分析提示词
 * @param {object} context - 上下文信息（卦象、八字、紫微等）
 * @param {string} questionType - 问题类型
 * @returns {Promise<string>} AI分析结果
 */
async function callDeepSeekAPI(prompt, context = {}, questionType = '') {
  try {
    // 构建系统提示词
    const systemPrompt = buildSystemPrompt(questionType);
    
    // 构建用户提示词
    const userPrompt = await buildUserPrompt(prompt, context);
    
    const response = await new Promise((resolve, reject) => {
      wx.request({
        url: DEEPSEEK_API_URL,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
        },
        data: {
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
          stream: false
        },
        success: resolve,
        fail: reject
      });
    });

    if (response.statusCode === 200 && response.data.choices && response.data.choices.length > 0) {
      return response.data.choices[0].message.content;
    } else {
      console.error('DeepSeek API调用失败:', response);
      return '抱歉，AI分析服务暂时不可用，请稍后再试。';
    }
  } catch (error) {
    console.error('DeepSeek API调用错误:', error);
    return '抱歉，AI分析服务暂时不可用，请稍后再试。';
  }
}

/**
 * 构建系统提示词
 * @param {string} questionType - 问题类型
 * @returns {string} 系统提示词
 */
function buildSystemPrompt(questionType) {
  const basePrompt = `你是一位精通中国传统命理学的大师，拥有深厚的易经、八字、紫微斗数知识。
你的分析必须：
1. 严格基于传统古籍理论
2. 提供具体的时间预测（精确到年月日）
3. 给出实用的建议和化解方法
4. 语言通俗易懂，让现代人能够理解
5. 保持准确性和专业性

你的回答格式应该包含：
- 【卦象/命理含义】
- 【精准时间预测】
- 【具体事件预测】
- 【实用建议】
- 【化解方法】（如适用）`;

  const typeSpecificPrompts = {
    '财运': '特别关注财星、妻财爻的分析，提供投资时机和收益预测',
    '事业': '重点分析官星、官鬼爻，提供升职跳槽的具体时间',
    '婚姻': '专注配偶星、夫妻宫的分析，预测结婚时机和对象特征',
    '健康': '分析疾病相关的六亲和宫位，提供康复时间和预防建议',
    '学业': '重点看父母爻、文昌星，提供考试运势和学习建议'
  };

  const specificPrompt = typeSpecificPrompts[questionType] || '根据问题类型进行针对性分析';
  
  return `${basePrompt}\n\n针对${questionType}问题：${specificPrompt}`;
}

/**
 * 构建用户提示词（集成知识库检索）
 * @param {string} question - 用户问题
 * @param {object} context - 上下文信息
 * @returns {Promise<string>} 用户提示词
 */
async function buildUserPrompt(question, context) {
  let prompt = `用户问题：${question}\n\n`;

  // 添加卦象信息
  if (context.hexagram) {
    prompt += `卦象信息：
- 卦名：${context.hexagram.name}
- 卦象：${context.hexagram.symbol || ''}
- 动爻：${context.hexagram.changingLines ? context.hexagram.changingLines.join('、') : '无'}
- 起卦方式：${context.hexagram.method || ''}
- 起卦时间：${context.hexagram.date || ''}
`;
  }

  // 添加八字信息
  if (context.bazi) {
    prompt += `八字信息：
- 四柱：${context.bazi.year} ${context.bazi.month} ${context.bazi.day} ${context.bazi.hour}
- 日主：${context.bazi.dayMaster}（${context.bazi.dayMasterElement}）
- 格局：${context.bazi.pattern}
- 用神：${context.bazi.useGod || ''}
- 当前大运：${context.bazi.currentDayun || ''}
`;
  }

  // 添加紫微斗数信息
  if (context.ziwei) {
    prompt += `紫微斗数信息：
- 命宫主星：${context.ziwei.mingGong || ''}
- 财帛宫：${context.ziwei.caibogong || ''}
- 官禄宫：${context.ziwei.guanlugong || ''}
- 夫妻宫：${context.ziwei.fuqigong || ''}
- 格局：${context.ziwei.pattern || ''}
`;
  }

  // 检索相关知识库内容
  try {
    const questionType = detectQuestionType(question);
    const relevantKnowledge = await getRelevantKnowledge(questionType, question);

    if (relevantKnowledge && relevantKnowledge.length > 0) {
      prompt += `\n【相关古籍依据】\n`;
      for (const knowledge of relevantKnowledge) {
        prompt += `\n《${knowledge.title}》相关内容：\n`;
        // 添加匹配的句子，限制长度
        const sentences = knowledge.matchedSentences.slice(0, 3);
        for (const sentence of sentences) {
          if (sentence.trim().length > 10) {
            prompt += `- ${sentence.trim()}\n`;
          }
        }
      }
    }
  } catch (error) {
    console.warn('知识库检索失败:', error);
  }

  prompt += `\n请严格基于以上信息和古籍依据，结合传统命理学理论，为用户提供详细的分析和建议。`;

  return prompt;
}

/**
 * 梅花易数AI分析
 * @param {string} question - 用户问题
 * @param {object} hexagram - 卦象信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeMeihuaWithAI(question, hexagram) {
  const context = { hexagram };
  const questionType = detectQuestionType(question);
  
  const prompt = `请基于梅花易数理论分析以下卦象：`;
  
  return await callDeepSeekAPI(prompt, context, questionType);
}

/**
 * 周易六爻AI分析
 * @param {string} question - 用户问题
 * @param {object} hexagram - 卦象信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeYijingWithAI(question, hexagram) {
  const context = { hexagram };
  const questionType = detectQuestionType(question);
  
  const prompt = `请基于周易六爻理论分析以下卦象：`;
  
  return await callDeepSeekAPI(prompt, context, questionType);
}

/**
 * 八字AI分析
 * @param {string} question - 用户问题
 * @param {object} bazi - 八字信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeBaziWithAI(question, bazi) {
  const context = { bazi };
  const questionType = detectQuestionType(question);
  
  const prompt = `请基于子平八字理论分析以下命盘：`;
  
  return await callDeepSeekAPI(prompt, context, questionType);
}

/**
 * 紫微斗数AI分析
 * @param {string} question - 用户问题
 * @param {object} ziwei - 紫微斗数信息
 * @returns {Promise<string>} AI分析结果
 */
async function analyzeZiweiWithAI(question, ziwei) {
  const context = { ziwei };
  const questionType = detectQuestionType(question);
  
  const prompt = `请基于紫微斗数理论分析以下命盘：`;
  
  return await callDeepSeekAPI(prompt, context, questionType);
}

/**
 * 检测问题类型
 * @param {string} question - 用户问题
 * @returns {string} 问题类型
 */
function detectQuestionType(question) {
  const keywords = {
    '财运': ['财运', '赚钱', '投资', '理财', '收入', '财富', '经济'],
    '事业': ['工作', '事业', '升职', '跳槽', '职业', '升迁', '当官'],
    '婚姻': ['婚姻', '结婚', '感情', '恋爱', '配偶', '对象', '桃花'],
    '健康': ['健康', '疾病', '身体', '病情', '康复', '医疗'],
    '学业': ['学习', '考试', '学业', '读书', '升学', '文凭']
  };
  
  for (const [type, words] of Object.entries(keywords)) {
    if (words.some(word => question.includes(word))) {
      return type;
    }
  }
  
  return '综合';
}

module.exports = {
  callDeepSeekAPI,
  analyzeMeihuaWithAI,
  analyzeYijingWithAI,
  analyzeBaziWithAI,
  analyzeZiweiWithAI,
  detectQuestionType
};
