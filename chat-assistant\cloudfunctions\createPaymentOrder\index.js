const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  const { price, days, productName } = event
  const { OPENID } = cloud.getWXContext()
  
  try {
    const db = cloud.database()
    const order = {
      userId: OPENID,
      price: price,
      days: days,
      productName: productName,
      status: 'pending',
      createTime: new Date(),
      orderId: `ORDER${Date.now()}${Math.random().toString(36).substr(2, 8)}`
    }
    
    // 创建订单记录
    await db.collection('orders').add({
      data: order
    })
    
    // 这里需要调用微信支付统一下单接口
    // 返回支付参数
    return {
      success: true,
      payment: {
        timeStamp: '',
        nonceStr: '',
        package: '',
        signType: 'MD5',
        paySign: ''
      },
      orderId: order.orderId
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}
