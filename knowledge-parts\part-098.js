// 知识库上传脚本 - 第98部分 (共99部分)

    ],
    "file_size": 117041,
    "created_at": "2025-06-28T14:54:58.027Z",
    "updated_at": "2025-06-28T14:54:58.027Z"
  }
];


// 所有批次数组
const ALL_BATCHES = [
  KNOWLEDGE_BATCH_1,
  KNOWLEDGE_BATCH_2,
  K<PERSON>OWLEDGE_BATCH_3,
  K<PERSON><PERSON>LEDGE_BATCH_4,
  <PERSON><PERSON><PERSON><PERSON>DGE_BATCH_5,
  KNOWLEDGE_BATCH_6,
  <PERSON><PERSON><PERSON>LEDGE_BATCH_7,
  KNOWLEDGE_BATCH_8,
  <PERSON><PERSON><PERSON><PERSON>DGE_BATCH_9,
  <PERSON><PERSON><PERSON><PERSON>DGE_BATCH_10,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_11,
  <PERSON><PERSON><PERSON><PERSON>DGE_BATCH_12,
  KN<PERSON>LEDGE_BATCH_13,
  KNOWLEDGE_BATCH_14,
  <PERSON><PERSON><PERSON>LEDGE_BATCH_15,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_16,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_17,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_18,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_19,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_20,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_BATCH_21,
  <PERSON><PERSON><PERSON><PERSON>DGE_BATCH_22,
  KNOWLEDGE_BATCH_23,
  KNOWLEDGE_BATCH_24,
  KNOWLEDGE_BATCH_25,
  KNOWLEDGE_BATCH_26,
  KNOWLEDGE_BATCH_27,
  KNOWLEDGE_BATCH_28,
  KNOWLEDGE_BATCH_29,
  KNOWLEDGE_BATCH_30
];

// 上传函数
async function uploadBatch(batchIndex) {
  if (batchIndex < 1 || batchIndex > ALL_BATCHES.length) {
    console.error('批次索引超出范围！');
    return;
  }
  
  const batch = ALL_BATCHES[batchIndex - 1];
  console.log(`开始上传第 ${batchIndex} 批数据，共 ${batch.length} 个文件...`);
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-upload',
      data: {
        action: 'upload_batch',
        batchData: batch
      }
    });
    
    if (result.result.success) {
      console.log(`✅ 第 ${batchIndex} 批上传成功！`);
      console.log(`上传了 ${result.result.message}`);
    } else {
      console.error(`❌ 第 ${batchIndex} 批上传失败:`, result.result.message);
    }
    
    return result.result;
  } catch (error) {
    console.error(`❌ 第 ${batchIndex} 批上传出错:`, error);
    return { success: false, error: error.message };
  }
}

// 上传所有批次
async function uploadAllBatches() {
  console.log(`准备上传 ${ALL_BATCHES.length} 批数据...`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 1; i <= ALL_BATCHES.length; i++) {
    console.log(`\n--- 上传第 ${i}/${ALL_BATCHES.length} 批 ---`);
    
    const result = await uploadBatch(i);
    
    if (result && result.success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 每批之间暂停1秒，避免请求过快
    if (i < ALL_BATCHES.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`\n📊 上传完成统计:`);