const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { userId, expireDate } = event
  
  try {
    const result = await db.collection('members').where({
      userId: userId
    }).update({
      data: {
        expireDate: expireDate,
        updateTime: db.serverDate()
      }
    })
    
    return {
      success: true,
      ...result
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}
