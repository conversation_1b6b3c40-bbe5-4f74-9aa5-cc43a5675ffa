# CSV数据导入说明

## 📁 文件说明
- 总共生成 9 个CSV文件
- 每个文件包含最多 50 条记录
- 总记录数: 438

## 🚀 导入步骤

### 1. 打开微信云开发控制台
- 访问 https://console.cloud.tencent.com/tcb
- 选择您的环境: cloud1-0g3xctv612d8f755

### 2. 进入数据库管理
- 点击左侧菜单 "数据库"
- 创建集合 "knowledge_base"（如果不存在）

### 3. 导入CSV文件
- 点击 "导入" 按钮
- 选择 "CSV格式"
- 依次上传每个CSV文件：
  - knowledge_batch_01.csv
  - knowledge_batch_02.csv
  - knowledge_batch_03.csv
  - knowledge_batch_04.csv
  - knowledge_batch_05.csv
  - knowledge_batch_06.csv
  - knowledge_batch_07.csv
  - knowledge_batch_08.csv
  - knowledge_batch_09.csv

### 4. 字段映射
确保字段映射正确：
- filePath -> filePath (字符串)
- title -> title (字符串)
- author -> author (字符串)
- dynasty -> dynasty (字符串)
- category -> category (字符串)
- keywords -> keywords (字符串)
- content -> content (字符串)
- file_size -> file_size (数字)
- created_at -> created_at (日期)
- updated_at -> updated_at (日期)

## ⚠️ 注意事项
1. 每次只能导入一个CSV文件
2. 导入前请确保数据库集合已创建
3. 如果导入失败，请检查CSV格式是否正确
4. 建议先导入一个小文件测试

## 📊 数据统计
- 总文件数: 438
- CSV文件数: 9
- 每个CSV最大记录数: 50
