/* 知识库上传页面样式 */
.upload-container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

.stats-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 30rpx;
  color: #34495e;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #3498db;
}

.action-section {
  margin-bottom: 30rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  border: none;
  background: #ecf0f1;
  color: #7f8c8d;
}

.action-btn.primary {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
}

.action-btn.danger {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
}

.action-btn:disabled {
  opacity: 0.6;
}

.progress-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background: #ecf0f1;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #27ae60, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

.log-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.log-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.log-content {
  height: 400rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 15rpx;
  padding: 10rpx;
  background: white;
  border-radius: 8rpx;
  border-left: 4rpx solid #3498db;
}

.log-time {
  display: block;
  font-size: 24rpx;
  color: #95a5a6;
  margin-bottom: 5rpx;
}

.log-message {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
}

.log-message.error {
  color: #e74c3c;
}

.log-message.success {
  color: #27ae60;
}

.log-message.warning {
  color: #f39c12;
}

.sample-data-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 20rpx;
}

.sample-files {
  max-height: 600rpx;
  overflow-y: auto;
}

.sample-file {
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #9b59b6;
}

.file-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.file-author {
  display: inline-block;
  font-size: 26rpx;
  color: #7f8c8d;
  margin-right: 20rpx;
}

.file-category {
  display: inline-block;
  font-size: 24rpx;
  color: #9b59b6;
  background: rgba(155, 89, 182, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.file-size {
  display: inline-block;
  font-size: 24rpx;
  color: #27ae60;
}
