{"version": 3, "file": "objectid.js", "sourceRoot": "", "sources": ["../src/objectid.ts"], "names": [], "mappings": ";;;AAAA,iCAAgC;AAChC,iDAA+C;AAC/C,iCAAwC;AACxC,wCAAsE;AAEtE,+CAA+C;AAC/C,IAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAE1D,qEAAqE;AACrE,IAAI,cAAc,GAAsB,IAAI,CAAC;AAc7C,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAEzB;;;;GAIG;AACH;IAaE;;;;OAIG;IACH,kBAAY,OAAyE;QACnF,IAAI,CAAC,CAAC,IAAI,YAAY,QAAQ,CAAC;YAAE,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE9D,oFAAoF;QACpF,IAAI,SAAS,CAAC;QACd,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,EAAE;YAC7D,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACrE,MAAM,IAAI,qBAAa,CACrB,qEAAqE,CACtE,CAAC;aACH;YACD,IAAI,aAAa,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;gBACzE,SAAS,GAAG,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;aACvD;iBAAM;gBACL,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;aACxB;SACF;aAAM;YACL,SAAS,GAAG,OAAO,CAAC;SACrB;QAED,6DAA6D;QAC7D,IAAI,SAAS,IAAI,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACtD,6DAA6D;YAC7D,oBAAoB;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;SACtF;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,UAAU,KAAK,EAAE,EAAE;YACvE,qFAAqF;YACrF,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,YAAY,eAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAY,EAAC,SAAS,CAAC,CAAC;SAC/E;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE;gBAC3B,IAAM,KAAK,GAAG,eAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrC,IAAI,KAAK,CAAC,UAAU,KAAK,EAAE,EAAE;oBAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;iBACnB;qBAAM;oBACL,MAAM,IAAI,qBAAa,CAAC,iDAAiD,CAAC,CAAC;iBAC5E;aACF;iBAAM,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACvE,IAAI,CAAC,GAAG,CAAC,GAAG,eAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;aAC3C;iBAAM;gBACL,MAAM,IAAI,qBAAa,CACrB,gGAAgG,CACjG,CAAC;aACH;SACF;aAAM;YACL,MAAM,IAAI,qBAAa,CAAC,sDAAsD,CAAC,CAAC;SACjF;QACD,mCAAmC;QACnC,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACrC;IACH,CAAC;IAMD,sBAAI,wBAAE;QAJN;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;aAED,UAAO,KAAa;YAClB,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAClB,IAAI,QAAQ,CAAC,cAAc,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACnC;QACH,CAAC;;;OAPA;IAaD,sBAAI,oCAAc;QAJlB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC;aAED,UAAmB,KAAa;YAC9B,iCAAiC;YACjC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;;;OALA;IAOD,0EAA0E;IAC1E,8BAAW,GAAX;QACE,IAAI,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE;YACxC,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,QAAQ,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACzC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;SACvB;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,eAAM,GAAb;QACE,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,iBAAQ,GAAf,UAAgB,IAAa;QAC3B,IAAI,QAAQ,KAAK,OAAO,IAAI,EAAE;YAC5B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;SACtC;QAED,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC9B,IAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEhC,mBAAmB;QACnB,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE9B,4CAA4C;QAC5C,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,cAAc,GAAG,IAAA,mBAAW,EAAC,CAAC,CAAC,CAAC;SACjC;QAED,wBAAwB;QACxB,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAE9B,iBAAiB;QACjB,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;QACxB,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;QAE/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,2BAAQ,GAAR,UAAS,MAAe;QACtB,8EAA8E;QAC9E,IAAI,MAAM;YAAE,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IAED,uEAAuE;IACvE,yBAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,yBAAM,GAAN,UAAO,OAAyC;QAC9C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,IAAI,EAAE;YAC7C,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7E;QAED,IACE,OAAO,OAAO,KAAK,QAAQ;YAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;YACzB,OAAO,CAAC,MAAM,KAAK,EAAE;YACrB,IAAA,oBAAY,EAAC,IAAI,CAAC,EAAE,CAAC,EACrB;YACA,OAAO,OAAO,KAAK,eAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SACtE;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACrF,OAAO,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;SACrD;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACrF,OAAO,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7C;QAED,IACE,OAAO,OAAO,KAAK,QAAQ;YAC3B,aAAa,IAAI,OAAO;YACxB,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EACzC;YACA,IAAM,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,OAAO,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC;SAC1F;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,0FAA0F;IAC1F,+BAAY,GAAZ;QACE,IAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3C,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAgB;IACT,iBAAQ,GAAf;QACE,OAAO,IAAI,QAAQ,EAAE,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,uBAAc,GAArB,UAAsB,IAAY;QAChC,IAAM,MAAM,GAAG,eAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjE,iCAAiC;QACjC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC9B,0BAA0B;QAC1B,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACI,4BAAmB,GAA1B,UAA2B,SAAiB;QAC1C,2CAA2C;QAC3C,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC,EAAE;YACtF,MAAM,IAAI,qBAAa,CACrB,yFAAyF,CAC1F,CAAC;SACH;QAED,OAAO,IAAI,QAAQ,CAAC,eAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,gBAAO,GAAd,UAAe,EAAmE;QAChF,IAAI,EAAE,IAAI,IAAI;YAAE,OAAO,KAAK,CAAC;QAE7B,IAAI;YACF,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;SACb;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,gBAAgB;IAChB,iCAAc,GAAd;QACE,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC1D,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;IACxC,CAAC;IAED,gBAAgB;IACT,yBAAgB,GAAvB,UAAwB,GAAqB;QAC3C,OAAO,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACH,mBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,0BAAO,GAAP;QACE,OAAO,yBAAiB,IAAI,CAAC,WAAW,EAAE,QAAI,CAAC;IACjD,CAAC;IAzSD,gBAAgB;IACT,cAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;IAyStD,eAAC;CAAA,AA7SD,IA6SC;AA7SY,4BAAQ;AA+SrB,qBAAqB;AACrB,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE;IACpD,KAAK,EAAE,IAAA,iBAAS,EACd,UAAC,IAAY,IAAK,OAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAvB,CAAuB,EACzC,yDAAyD,CAC1D;CACF,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;IAClD,KAAK,EAAE,IAAA,iBAAS,EAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,EAAjB,CAAiB,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE;IACnD,KAAK,EAAE,IAAA,iBAAS,EAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,EAAjB,CAAiB,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE;IACzC,KAAK,EAAE,IAAA,iBAAS,EAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,EAAjB,CAAiB,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC"}