.TH sshpk\-conv 1 "Jan 2016" sshpk "sshpk Commands"
.SH NAME
.PP
sshpk\-conv \- convert between key formats
.SH SYNOPSYS
.PP
\fB\fCsshpk\-conv\fR \-t FORMAT [FILENAME] [OPTIONS...]
.PP
\fB\fCsshpk\-conv\fR \-i [FILENAME] [OPTIONS...]
.SH DESCRIPTION
.PP
Reads in a public or private key and converts it between different formats,
particularly formats used in the SSH protocol and the well\-known PEM PKCS#1/7
formats.
.PP
In the second form, with the \fB\fC\-i\fR option given, identifies a key and prints to 
stderr information about its nature, size and fingerprint.
.SH EXAMPLES
.PP
Assume the following SSH\-format public key in \fB\fCid_ecdsa.pub\fR:
.PP
.RS
.nf
ecdsa\-sha2\-nistp256 AAAAE2VjZHNhLXNoYTI...9M/4c4= user@host
.fi
.RE
.PP
Identify it with \fB\fC\-i\fR:
.PP
.RS
.nf
$ sshpk\-conv \-i id_ecdsa.pub
id_ecdsa: a 256 bit ECDSA public key
ECDSA curve: nistp256
Comment: user@host
Fingerprint:
  SHA256:vCNX7eUkdvqqW0m4PoxQAZRv+CM4P4fS8+CbliAvS4k
  81:ad:d5:57:e5:6f:7d:a2:93:79:56:af:d7:c0:38:51
.fi
.RE
.PP
Convert it to \fB\fCpkcs8\fR format, for use with e.g. OpenSSL:
.PP
.RS
.nf
$ sshpk\-conv \-t pkcs8 id_ecdsa
\-\-\-\-\-BEGIN PUBLIC KEY\-\-\-\-\-
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEAsA4R6N6AS3gzaPBeLjG2ObSgUsR
zOt+kWJoijLnw3ZMYUKmAx+lD0I5XUxdrPcs1vH5f3cn9TvRvO9L0z/hzg==
\-\-\-\-\-END PUBLIC KEY\-\-\-\-\-
.fi
.RE
.PP
Retrieve the public half of a private key:
.PP
.RS
.nf
$ openssl genrsa 2048 | sshpk\-conv \-t ssh \-c foo@bar
ssh\-rsa AAAAB3NzaC1yc2EAAA...koK7 foo@bar
.fi
.RE
.PP
Convert a private key to PKCS#1 (OpenSSL) format from a new\-style OpenSSH key
format (the \fB\fCssh\-keygen \-o\fR format):
.PP
.RS
.nf
$ ssh\-keygen \-o \-f foobar
\&...
$ sshpk\-conv \-p \-t pkcs1 foobar
\-\-\-\-\-BEGIN RSA PRIVATE KEY\-\-\-\-\-
MIIDpAIBAAKCAQEA6T/GYJndb1TRH3+NL....
\-\-\-\-\-END RSA PRIVATE KEY\-\-\-\-\-
.fi
.RE
.SH OPTIONS
.TP
\fB\fC\-i, \-\-identify\fR
Instead of converting the key, output identifying information about it to 
stderr, including its type, size and fingerprints.
.TP
\fB\fC\-p, \-\-private\fR
Treat the key as a private key instead of a public key (the default). If you
supply \fB\fCsshpk\-conv\fR with a private key and do not give this option, it will
extract only the public half of the key from it and work with that.
.TP
\fB\fC\-f PATH, \-\-file=PATH\fR
Input file to take the key from instead of stdin. If a filename is supplied
as a positional argument, it is equivalent to using this option.
.TP
\fB\fC\-o PATH, \-\-out=PATH\fR
Output file name to use instead of stdout.
.PP
\fB\fC\-T FORMAT, \-\-informat=FORMAT\fR
.TP
\fB\fC\-t FORMAT, \-\-outformat=FORMAT\fR
Selects the input and output formats to be used (see FORMATS, below).
.TP
\fB\fC\-c TEXT, \-\-comment=TEXT\fR
Sets the key comment for the output file, if supported.
.SH FORMATS
.PP
Currently supported formats:
.TP
\fB\fCpem, pkcs1\fR
The standard PEM format used by older OpenSSH and most TLS libraries such as
OpenSSL. The classic \fB\fCid_rsa\fR file is usually in this format. It is an ASN.1
encoded structure, base64\-encoded and placed between PEM headers.
.TP
\fB\fCssh\fR
The SSH public key text format (the format of an \fB\fCid_rsa.pub\fR file). A single
line, containing 3 space separated parts: the key type, key body and optional
key comment.
.TP
\fB\fCpkcs8\fR
A newer PEM format, usually used only for public keys by TLS libraries such
as OpenSSL. The ASN.1 structure is more generic than that of \fB\fCpkcs1\fR\&.
.TP
\fB\fCopenssh\fR
The new \fB\fCssh\-keygen \-o\fR format from OpenSSH. This can be mistaken for a PEM
encoding but is actually an OpenSSH internal format.
.TP
\fB\fCrfc4253\fR
The internal binary format of keys when sent over the wire in the SSH
protocol. This is also the format that the \fB\fCssh\-agent\fR uses in its protocol.
.SH SEE ALSO
.PP
.BR ssh-keygen (1), 
.BR openssl (1)
.SH BUGS
.PP
Encrypted (password\-protected) keys are not supported.
.PP
Report bugs at Github
\[la]https://github.com/arekinath/node-sshpk/issues\[ra]
