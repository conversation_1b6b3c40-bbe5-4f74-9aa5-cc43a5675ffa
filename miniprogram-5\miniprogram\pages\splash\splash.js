// pages/splash/splash.js - 启动页面
const app = getApp();

Page({
  data: {
    showLogo: false,
    showTitle: false,
    showSubtitle: false,
    showButton: false,
    animationStep: 0,
    userInfo: null,
    hasUserInfo: false
  },

  onLoad() {
    console.log('启动页面加载');
    this.startAnimation();
  },

  onShow() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  // 开始启动动画
  startAnimation() {
    // 延迟显示各个元素，营造水墨渐现效果
    setTimeout(() => {
      this.setData({ showLogo: true, animationStep: 1 });
    }, 500);

    setTimeout(() => {
      this.setData({ showTitle: true, animationStep: 2 });
    }, 1200);

    setTimeout(() => {
      this.setData({ showSubtitle: true, animationStep: 3 });
    }, 1800);

    setTimeout(() => {
      this.setData({ showButton: true, animationStep: 4 });
    }, 2500);
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo;
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },

  // 微信授权登录
  async onWechatLogin() {
    try {
      app.showLoading('正在登录...');

      // 获取用户信息
      const userInfo = await app.getUserInfo();
      console.log('获取用户信息成功:', userInfo);

      // 获取OpenID
      const openid = await app.getOpenId();
      console.log('获取OpenID成功:', openid);

      // 简化版本：直接使用本地存储管理用户数据
      let userData = wx.getStorageSync('userData_' + openid);

      if (!userData) {
        // 新用户，创建用户数据
        userData = {
          openid: openid,
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl,
          phone: null,
          registerTime: new Date(),
          lastLoginTime: new Date()
        };

        // 保存到本地存储
        wx.setStorageSync('userData_' + openid, userData);
        console.log('新用户注册成功');
      } else {
        // 老用户，更新最后登录时间
        userData.lastLoginTime = new Date();
        wx.setStorageSync('userData_' + openid, userData);
        console.log('老用户登录成功');
      }

      // 更新全局数据
      app.globalData.userInfo = userInfo;
      app.globalData.openid = openid;
      app.globalData.phone = userData.phone;

      // 保存登录状态
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('openid', openid);

      app.hideLoading();
      app.showSuccess('登录成功');

      // 跳转到主页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      app.hideLoading();
      console.error('登录失败:', error);
      app.showError('登录失败，请重试');
    }
  },







  // 跳过登录，直接进入（游客模式）
  onSkipLogin() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
