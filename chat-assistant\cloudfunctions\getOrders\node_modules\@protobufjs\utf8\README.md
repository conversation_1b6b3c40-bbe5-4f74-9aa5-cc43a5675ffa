@protobufjs/utf8
================
[![npm](https://img.shields.io/npm/v/@protobufjs/utf8.svg)](https://www.npmjs.com/package/@protobufjs/utf8)

A minimal UTF8 implementation for number arrays.

API
---

* **utf8.length(string: `string`): `number`**<br />
  Calculates the UTF8 byte length of a string.

* **utf8.read(buffer: `Uint8Array`, start: `number`, end: `number`): `string`**<br />
  Reads UTF8 bytes as a string.

* **utf8.write(string: `string`, buffer: `Uint8Array`, offset: `number`): `number`**<br />
  Writes a string as UTF8 bytes.


**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)
