const fs = require('fs');

console.log('开始拆分batch-024.json...');

try {
  // 读取batch-024.json
  const batch024 = JSON.parse(fs.readFileSync('./upload-batches/batch-024.json', 'utf8'));
  
  console.log(`原文件包含 ${batch024.fileCount} 个文件，大小 ${batch024.sizeInMB}MB`);
  
  // 拆分成3个更小的文件，每个3-4个文件
  const splits = [
    { files: batch024.data.slice(0, 3), suffix: 'a' },
    { files: batch024.data.slice(3, 6), suffix: 'b' },
    { files: batch024.data.slice(6, 10), suffix: 'c' }
  ];
  
  splits.forEach((split, index) => {
    const newBatch = {
      batchNumber: `024${split.suffix}`,
      totalBatches: 46, // 原44 + 2个额外拆分
      fileCount: split.files.length,
      totalSize: split.files.reduce((sum, file) => sum + (file.content ? file.content.length : 0), 0),
      sizeInMB: 0,
      files: split.files.map(f => f.filePath),
      data: split.files
    };
    
    newBatch.sizeInMB = Math.round(newBatch.totalSize / 1024 / 1024 * 100) / 100;
    
    const filename = `./upload-batches/batch-024${split.suffix}.json`;
    fs.writeFileSync(filename, JSON.stringify(newBatch, null, 2), 'utf8');
    
    console.log(`✅ 创建 ${filename}: ${newBatch.fileCount} 个文件, ${newBatch.sizeInMB}MB`);
  });
  
  // 备份原文件
  fs.renameSync('./upload-batches/batch-024.json', './upload-batches/batch-024-original.json');
  console.log('✅ 原文件已备份为 batch-024-original.json');
  
  console.log('\n🎉 拆分完成！现在可以分别导入：');
  console.log('- batch-024a.json (3个文件)');
  console.log('- batch-024b.json (3个文件)');
  console.log('- batch-024c.json (4个文件)');
  
} catch (error) {
  console.error('❌ 拆分失败:', error.message);
}
