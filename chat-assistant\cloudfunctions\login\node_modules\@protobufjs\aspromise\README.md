@protobufjs/aspromise
=====================
[![npm](https://img.shields.io/npm/v/@protobufjs/aspromise.svg)](https://www.npmjs.com/package/@protobufjs/aspromise)

Returns a promise from a node-style callback function.

API
---

* **asPromise(fn: `function`, ctx: `Object`, ...params: `*`): `Promise<*>`**<br />
  Returns a promise from a node-style callback function.

**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)
