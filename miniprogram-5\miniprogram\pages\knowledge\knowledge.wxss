/* pages/knowledge/knowledge.wxss - 古籍查询页面样式 */

page {
  background: var(--paper-white);
  padding-bottom: 120rpx;
}

.knowledge-container {
  min-height: 100vh;
  padding: 32rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  font-size: 48rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.header-subtitle {
  font-size: 28rpx;
  color: var(--ink-gray);
  letter-spacing: 2rpx;
}

/* 搜索区域 */
.search-section {
  background: var(--paper-white);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
}

.search-box {
  position: relative;
}

.search-actions {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 12rpx;
}

.search-btn, .clear-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--ancient-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.search-icon, .clear-icon {
  font-size: 28rpx;
  color: var(--paper-white);
}

/* 分类筛选 */
.category-section {
  margin-bottom: 32rpx;
}

.category-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  gap: 16rpx;
}

.category-item {
  background: var(--ancient-paper);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.category-item.active {
  background: var(--ancient-gold);
  border-color: var(--ink-black);
}

.category-name {
  font-size: 26rpx;
  color: var(--ink-black);
  font-weight: 500;
}

.category-item.active .category-name {
  color: var(--paper-white);
}

.category-count {
  font-size: 22rpx;
  color: var(--ink-gray);
  margin-left: 8rpx;
}

.category-item.active .category-count {
  color: var(--ancient-paper);
}

/* 通用区块标题 */
.section-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 24rpx;
  letter-spacing: 1rpx;
}

/* 搜索结果 */
.results-section {
  margin-bottom: 40rpx;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
  border-left: 4rpx solid var(--ancient-gold);
}

.result-header {
  margin-bottom: 16rpx;
}

.result-title {
  font-size: 30rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 8rpx;
}

.result-meta {
  display: flex;
  gap: 16rpx;
}

.result-author, .result-category {
  font-size: 24rpx;
  color: var(--ink-gray);
  padding: 4rpx 12rpx;
  background: var(--ancient-paper);
  border-radius: 12rpx;
}

.result-content {
  margin-bottom: 16rpx;
}

.matched-text {
  font-size: 26rpx;
  color: var(--ink-black);
  line-height: 1.6;
}

.result-footer {
  text-align: right;
}

.relevance {
  font-size: 22rpx;
  color: var(--ancient-gold);
  font-weight: 500;
}

/* 热门关键词 */
.hot-keywords-section {
  margin-bottom: 40rpx;
}

.keywords-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.keyword-item {
  background: var(--ancient-paper);
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  border: 2rpx solid var(--ink-light);
  transition: all 0.3s ease;
}

.keyword-item:hover {
  background: var(--ancient-gold);
  border-color: var(--ink-black);
}

.keyword-text {
  font-size: 26rpx;
  color: var(--ink-black);
}

.keyword-item:hover .keyword-text {
  color: var(--paper-white);
}

/* 推荐古籍 */
.recent-books-section {
  margin-bottom: 40rpx;
}

.books-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.book-item {
  background: var(--paper-white);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.04);
  border-left: 4rpx solid var(--ink-light);
}

.book-header {
  margin-bottom: 12rpx;
}

.book-title {
  font-size: 30rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 8rpx;
}

.book-author {
  font-size: 24rpx;
  color: var(--ink-gray);
}

.book-category {
  font-size: 22rpx;
  color: var(--ancient-gold);
  background: rgba(212, 175, 55, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-bottom: 16rpx;
}

.book-description {
  font-size: 26rpx;
  color: var(--ink-black);
  line-height: 1.6;
}

/* 状态页面 */
.searching-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--ink-gray);
  line-height: 1.6;
}