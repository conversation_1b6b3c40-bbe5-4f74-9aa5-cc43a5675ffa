// 八字十神分析系统
// 基于传统子平八字理论

import { FIVE_ELEMENTS, YIN_YANG } from './bazi-calculator.js';

// 十神定义
export const TEN_GODS = {
  '比肩': { nature: '中性', meaning: '兄弟朋友、竞争、自我' },
  '劫财': { nature: '凶', meaning: '破财、小人、争夺' },
  '食神': { nature: '吉', meaning: '才华、享受、子女' },
  '伤官': { nature: '凶', meaning: '聪明、叛逆、伤害' },
  '偏财': { nature: '吉', meaning: '横财、父亲、情人' },
  '正财': { nature: '吉', meaning: '正当收入、妻子、财富' },
  '七杀': { nature: '凶', meaning: '压力、小人、疾病' },
  '正官': { nature: '吉', meaning: '地位、名声、丈夫' },
  '偏印': { nature: '凶', meaning: '偏业、继母、孤独' },
  '正印': { nature: '吉', meaning: '学问、母亲、贵人' }
};

// 五行生克关系
export const FIVE_ELEMENTS_RELATION = {
  '木': { generates: '火', destroys: '土', generatedBy: '水', destroyedBy: '金' },
  '火': { generates: '土', destroys: '金', generatedBy: '木', destroyedBy: '水' },
  '土': { generates: '金', destroys: '水', generatedBy: '火', destroyedBy: '木' },
  '金': { generates: '水', destroys: '木', generatedBy: '土', destroyedBy: '火' },
  '水': { generates: '木', destroys: '火', generatedBy: '金', destroyedBy: '土' }
};

/**
 * 计算十神关系
 * @param {string} dayMaster - 日主天干
 * @param {string} targetStem - 目标天干
 * @returns {string} 十神名称
 */
export function calculateTenGod(dayMaster, targetStem) {
  const dayElement = FIVE_ELEMENTS[dayMaster];
  const targetElement = FIVE_ELEMENTS[targetStem];
  const dayYinYang = YIN_YANG[dayMaster];
  const targetYinYang = YIN_YANG[targetStem];
  
  // 同五行
  if (dayElement === targetElement) {
    return dayYinYang === targetYinYang ? '比肩' : '劫财';
  }
  
  // 日主生目标（食伤）
  if (FIVE_ELEMENTS_RELATION[dayElement].generates === targetElement) {
    return dayYinYang === targetYinYang ? '食神' : '伤官';
  }
  
  // 日主克目标（财星）
  if (FIVE_ELEMENTS_RELATION[dayElement].destroys === targetElement) {
    return dayYinYang === targetYinYang ? '偏财' : '正财';
  }
  
  // 目标克日主（官杀）
  if (FIVE_ELEMENTS_RELATION[targetElement].destroys === dayElement) {
    return dayYinYang === targetYinYang ? '七杀' : '正官';
  }
  
  // 目标生日主（印星）
  if (FIVE_ELEMENTS_RELATION[targetElement].generates === dayElement) {
    return dayYinYang === targetYinYang ? '偏印' : '正印';
  }
  
  return '未知';
}

/**
 * 分析八字十神分布
 * @param {Object} bazi - 八字信息
 * @returns {Object} 十神分析结果
 */
export function analyzeTenGods(bazi) {
  const dayMaster = bazi.dayMaster;
  const tenGodsDistribution = {};
  
  // 初始化十神统计
  Object.keys(TEN_GODS).forEach(god => {
    tenGodsDistribution[god] = [];
  });
  
  // 分析年月时三柱的天干（日柱天干是日主，不分析）
  const pillars = [
    { name: '年干', stem: bazi.year.stem },
    { name: '月干', stem: bazi.month.stem },
    { name: '时干', stem: bazi.hour.stem }
  ];
  
  pillars.forEach(pillar => {
    const tenGod = calculateTenGod(dayMaster, pillar.stem);
    tenGodsDistribution[tenGod].push(pillar.name);
  });
  
  return {
    distribution: tenGodsDistribution,
    dayMaster: dayMaster,
    dayMasterElement: FIVE_ELEMENTS[dayMaster]
  };
}

/**
 * 判断日主强弱
 * @param {Object} bazi - 八字信息
 * @returns {Object} 强弱分析结果
 */
export function analyzeDayMasterStrength(bazi) {
  const dayMaster = bazi.dayMaster;
  const dayElement = FIVE_ELEMENTS[dayMaster];
  const monthBranch = bazi.month.branch;
  const monthElement = FIVE_ELEMENTS[monthBranch];
  
  let strength = 0;
  let analysis = [];
  
  // 月令得气（最重要）
  if (monthElement === dayElement) {
    strength += 3;
    analysis.push('月令本气，日主得气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].generates === dayElement) {
    strength += 2;
    analysis.push('月令生助，日主有气');
  } else if (FIVE_ELEMENTS_RELATION[monthElement].destroys === dayElement) {
    strength -= 2;
    analysis.push('月令克制，日主失气');
  } else if (FIVE_ELEMENTS_RELATION[dayElement].destroys === monthElement) {
    strength -= 1;
    analysis.push('日主克月令，耗气');
  }
  
  // 统计其他柱的帮扶和克制
  [bazi.year, bazi.month, bazi.hour].forEach(pillar => {
    const stemElement = FIVE_ELEMENTS[pillar.stem];
    const branchElement = FIVE_ELEMENTS[pillar.branch];
    
    // 天干帮扶
    if (stemElement === dayElement) {
      strength += 1;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干同类帮身`);
    } else if (FIVE_ELEMENTS_RELATION[stemElement].generates === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}干生助`);
    }
    
    // 地支帮扶（权重较小）
    if (branchElement === dayElement) {
      strength += 0.5;
      analysis.push(`${pillar === bazi.year ? '年' : pillar === bazi.month ? '月' : '时'}支同类`);
    }
  });
  
  // 判断强弱
  let strengthLevel;
  if (strength >= 3) {
    strengthLevel = '偏旺';
  } else if (strength >= 1) {
    strengthLevel = '中和偏强';
  } else if (strength >= -1) {
    strengthLevel = '中和';
  } else if (strength >= -3) {
    strengthLevel = '偏弱';
  } else {
    strengthLevel = '很弱';
  }
  
  return {
    strength: strength,
    level: strengthLevel,
    analysis: analysis
  };
}

/**
 * 确定用神喜忌
 * @param {Object} bazi - 八字信息
 * @param {Object} strengthAnalysis - 强弱分析
 * @returns {Object} 用神喜忌分析
 */
export function analyzeUseGod(bazi, strengthAnalysis) {
  const dayElement = FIVE_ELEMENTS[bazi.dayMaster];
  const strengthLevel = strengthAnalysis.level;
  
  let useGod = [];
  let avoidGod = [];
  let analysis = [];
  
  if (strengthLevel === '偏旺' || strengthLevel === '很旺') {
    // 日主偏旺，需要克泄耗
    const drainElement = FIVE_ELEMENTS_RELATION[dayElement].generates; // 食伤
    const restrainElement = FIVE_ELEMENTS_RELATION[dayElement].destroyedBy; // 官杀
    const consumeElement = FIVE_ELEMENTS_RELATION[dayElement].destroys; // 财星
    
    useGod.push(drainElement, restrainElement, consumeElement);
    avoidGod.push(dayElement, FIVE_ELEMENTS_RELATION[dayElement].generatedBy);
    analysis.push('日主偏旺，喜克泄耗，忌生助');
    
  } else if (strengthLevel === '偏弱' || strengthLevel === '很弱') {
    // 日主偏弱，需要生助
    const supportElement = FIVE_ELEMENTS_RELATION[dayElement].generatedBy; // 印星
    
    useGod.push(dayElement, supportElement);
    avoidGod.push(
      FIVE_ELEMENTS_RELATION[dayElement].generates,
      FIVE_ELEMENTS_RELATION[dayElement].destroyedBy,
      FIVE_ELEMENTS_RELATION[dayElement].destroys
    );
    analysis.push('日主偏弱，喜生助，忌克泄耗');
    
  } else {
    // 中和，根据具体情况调节
    analysis.push('日主中和，需要根据具体格局和大运流年调节');
    useGod.push('因时制宜');
  }
  
  return {
    useGod: useGod,
    avoidGod: avoidGod,
    analysis: analysis
  };
}

/**
 * 分析格局
 * @param {Object} bazi - 八字信息
 * @param {Object} tenGodsAnalysis - 十神分析
 * @returns {Object} 格局分析
 */
export function analyzePattern(bazi, tenGodsAnalysis) {
  const distribution = tenGodsAnalysis.distribution;
  
  // 检查是否有明显的格局特征
  let pattern = '普通格局';
  let analysis = [];
  
  // 正官格
  if (distribution['正官'].length > 0 && distribution['七杀'].length === 0) {
    pattern = '正官格';
    analysis.push('正官透干，为正官格，主贵气');
  }
  
  // 七杀格
  else if (distribution['七杀'].length > 0 && distribution['正官'].length === 0) {
    pattern = '七杀格';
    analysis.push('七杀透干，为七杀格，主威权');
  }
  
  // 正财格
  else if (distribution['正财'].length > 0 && distribution['偏财'].length === 0) {
    pattern = '正财格';
    analysis.push('正财透干，为正财格，主富裕');
  }
  
  // 偏财格
  else if (distribution['偏财'].length > 0 && distribution['正财'].length === 0) {
    pattern = '偏财格';
    analysis.push('偏财透干，为偏财格，主横财');
  }
  
  // 食神格
  else if (distribution['食神'].length > 0 && distribution['伤官'].length === 0) {
    pattern = '食神格';
    analysis.push('食神透干，为食神格，主才华');
  }
  
  // 伤官格
  else if (distribution['伤官'].length > 0 && distribution['食神'].length === 0) {
    pattern = '伤官格';
    analysis.push('伤官透干，为伤官格，主聪明');
  }
  
  // 正印格
  else if (distribution['正印'].length > 0 && distribution['偏印'].length === 0) {
    pattern = '正印格';
    analysis.push('正印透干，为正印格，主学问');
  }
  
  // 偏印格
  else if (distribution['偏印'].length > 0 && distribution['正印'].length === 0) {
    pattern = '偏印格';
    analysis.push('偏印透干，为偏印格，主偏业');
  }
  
  // 比肩格
  else if (distribution['比肩'].length >= 2) {
    pattern = '比肩格';
    analysis.push('比肩多见，为比肩格，主自立');
  }
  
  // 劫财格
  else if (distribution['劫财'].length >= 2) {
    pattern = '劫财格';
    analysis.push('劫财多见，为劫财格，需要制化');
  }
  
  return {
    pattern: pattern,
    analysis: analysis
  };
}

/**
 * 综合八字分析
 * @param {Object} bazi - 八字信息
 * @returns {Object} 综合分析结果
 */
export function comprehensiveBaziAnalysis(bazi) {
  const tenGodsAnalysis = analyzeTenGods(bazi);
  const strengthAnalysis = analyzeDayMasterStrength(bazi);
  const useGodAnalysis = analyzeUseGod(bazi, strengthAnalysis);
  const patternAnalysis = analyzePattern(bazi, tenGodsAnalysis);
  
  return {
    tenGods: tenGodsAnalysis,
    strength: strengthAnalysis,
    useGod: useGodAnalysis,
    pattern: patternAnalysis,
    dayMaster: bazi.dayMaster,
    dayMasterElement: FIVE_ELEMENTS[bazi.dayMaster]
  };
}
