// 知识库自动上传脚本
// 用于批量读取knowledge目录下的所有文件并生成上传数据

const fs = require('fs');
const path = require('path');

// 配置
const KNOWLEDGE_DIR = './knowledge';
const OUTPUT_FILE = './knowledge-data.js';
const BATCH_SIZE = 15; // 每批处理的文件数量

/**
 * 解析文件名获取书籍信息
 */
function parseFilename(filename) {
  const name = path.basename(filename, '.txt');
  
  let title = name;
  let author = '';
  let dynasty = '';
  let category = 'other';
  
  // 匹配模式：书名-朝代-作者
  const patterns = [
    /^(.+)-(.+)-(.+)$/, // 书名-朝代-作者
    /^(.+)-(.+)$/, // 书名-作者 或 作者-书名
    /^(\d+)\.(.+)$/, // 编号.书名
    /^(.+)$/  // 仅书名
  ];
  
  // 尝试匹配不同模式
  for (const pattern of patterns) {
    const match = name.match(pattern);
    if (match) {
      if (match.length === 4) {
        // 书名-朝代-作者
        title = match[1];
        dynasty = match[2];
        author = match[3];
      } else if (match.length === 3) {
        if (/^\d+$/.test(match[1])) {
          // 编号.书名
          title = match[2];
        } else {
          // 判断是 书名-作者 还是 作者-书名
          if (match[2].includes('宋') || match[2].includes('唐') || 
              match[2].includes('明') || match[2].includes('清') ||
              match[2].includes('元') || match[2].includes('汉')) {
            title = match[1];
            dynasty = match[2];
          } else if (match[1].includes('宋') || match[1].includes('唐') || 
                     match[1].includes('明') || match[1].includes('清') ||
                     match[1].includes('元') || match[1].includes('汉')) {
            dynasty = match[1];
            title = match[2];
          } else {
            // 默认第一个是书名，第二个是作者
            title = match[1];
            author = match[2];
          }
        }
      }
      break;
    }
  }
  
  // 根据书名判断分类
  if (title.includes('周易') || title.includes('易经') || title.includes('易传')) {
    category = 'zhouyi';
  } else if (title.includes('梅花易数')) {
    category = 'meihua';
  } else if (title.includes('紫微斗数') || title.includes('紫薇')) {
    category = 'ziwei';
  } else if (title.includes('八字') || title.includes('子平')) {
    category = 'bazi';
  } else if (title.includes('六壬') || title.includes('奇门')) {
    category = 'liuren';
  }
  
  return {
    title: title.trim(),
    author: author.trim(),
    dynasty: dynasty.trim(),
    category
  };
}

/**
 * 提取关键词
 */
function extractKeywords(content) {
  const keywords = [];
  
  // 易经相关关键词
  const yijingKeywords = ['乾', '坤', '震', '巽', '坎', '离', '艮', '兑', '太极', '阴阳', '八卦', '六十四卦'];
  // 紫微斗数关键词
  const ziweiKeywords = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'];
  // 梅花易数关键词
  const meihuaKeywords = ['梅花易数', '先天', '后天', '体卦', '用卦', '变卦', '互卦'];
  
  const allKeywords = [...yijingKeywords, ...ziweiKeywords, ...meihuaKeywords];
  
  allKeywords.forEach(keyword => {
    if (content.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return keywords.slice(0, 10); // 最多返回10个关键词
}

/**
 * 读取所有文件并生成数据
 */
function generateKnowledgeData() {
  console.log('开始扫描knowledge目录...');
  
  if (!fs.existsSync(KNOWLEDGE_DIR)) {
    console.error('knowledge目录不存在！');
    return;
  }
  
  const files = fs.readdirSync(KNOWLEDGE_DIR)
    .filter(file => file.endsWith('.txt'))
    .sort();
  
  console.log(`找到 ${files.length} 个txt文件`);
  
  const allData = [];
  let processedCount = 0;
  
  files.forEach((file, index) => {
    try {
      const filePath = path.join(KNOWLEDGE_DIR, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      if (content.trim().length === 0) {
        console.log(`跳过空文件: ${file}`);
        return;
      }
      
      const fileInfo = parseFilename(file);
      const keywords = extractKeywords(content);
      
      const fileData = {
        filePath: file,
        content: content.trim(),
        ...fileInfo,
        keywords,
        file_size: content.length,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      allData.push(fileData);
      processedCount++;
      
      if (processedCount % 10 === 0) {
        console.log(`已处理 ${processedCount}/${files.length} 个文件...`);
      }
      
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error.message);
    }
  });
  
  console.log(`总共处理了 ${processedCount} 个文件`);
  
  // 分批生成数据
  const batches = [];
  for (let i = 0; i < allData.length; i += BATCH_SIZE) {
    batches.push(allData.slice(i, i + BATCH_SIZE));
  }
  
  console.log(`分为 ${batches.length} 批，每批最多 ${BATCH_SIZE} 个文件`);
  
  // 生成JavaScript文件
  let jsContent = `// 自动生成的知识库数据文件
// 生成时间: ${new Date().toLocaleString()}
// 总文件数: ${allData.length}
// 分批数量: ${batches.length}

`;
  
  batches.forEach((batch, index) => {
    jsContent += `// 第 ${index + 1} 批数据 (${batch.length} 个文件)
const KNOWLEDGE_BATCH_${index + 1} = ${JSON.stringify(batch, null, 2)};

`;
  });
  
  jsContent += `
// 所有批次数组
const ALL_BATCHES = [
${batches.map((_, index) => `  KNOWLEDGE_BATCH_${index + 1}`).join(',\n')}
];

// 上传函数
async function uploadBatch(batchIndex) {
  if (batchIndex < 1 || batchIndex > ALL_BATCHES.length) {
    console.error('批次索引超出范围！');
    return;
  }
  
  const batch = ALL_BATCHES[batchIndex - 1];
  console.log(\`开始上传第 \${batchIndex} 批数据，共 \${batch.length} 个文件...\`);
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-upload',
      data: {
        action: 'upload_batch',
        batchData: batch
      }
    });
    
    if (result.result.success) {
      console.log(\`✅ 第 \${batchIndex} 批上传成功！\`);
      console.log(\`上传了 \${result.result.message}\`);
    } else {
      console.error(\`❌ 第 \${batchIndex} 批上传失败:\`, result.result.message);
    }
    
    return result.result;
  } catch (error) {
    console.error(\`❌ 第 \${batchIndex} 批上传出错:\`, error);
    return { success: false, error: error.message };
  }
}

// 上传所有批次
async function uploadAllBatches() {
  console.log(\`准备上传 \${ALL_BATCHES.length} 批数据...\`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 1; i <= ALL_BATCHES.length; i++) {
    console.log(\`\\n--- 上传第 \${i}/\${ALL_BATCHES.length} 批 ---\`);
    
    const result = await uploadBatch(i);
    
    if (result && result.success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 每批之间暂停1秒，避免请求过快
    if (i < ALL_BATCHES.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(\`\\n📊 上传完成统计:\`);
  console.log(\`✅ 成功: \${successCount} 批\`);
  console.log(\`❌ 失败: \${failCount} 批\`);
  console.log(\`📁 总文件数: \${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)}\`);
}

// 检查上传状态
async function checkUploadStatus() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: {
        action: 'count'
      }
    });
    
    if (result.result.success) {
      console.log(\`📊 当前云端知识库统计:\`);
      console.log(\`📁 总记录数: \${result.result.total}\`);
      console.log(\`📄 示例记录:\`, result.result.sample);
    }
    
    return result.result;
  } catch (error) {
    console.error('检查状态失败:', error);
  }
}

// 导出函数供控制台使用
if (typeof window !== 'undefined') {
  window.uploadBatch = uploadBatch;
  window.uploadAllBatches = uploadAllBatches;
  window.checkUploadStatus = checkUploadStatus;
  window.ALL_BATCHES = ALL_BATCHES;
  
  console.log('📚 知识库上传脚本已加载！');
  console.log(\`📊 数据统计: \${ALL_BATCHES.length} 批，共 \${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)} 个文件\`);
  console.log('');
  console.log('🔧 可用命令:');
  console.log('  checkUploadStatus() - 检查当前上传状态');
  console.log('  uploadBatch(n) - 上传第n批数据');
  console.log('  uploadAllBatches() - 上传所有批次');
  console.log('');
}
`;
  
  // 写入文件
  fs.writeFileSync(OUTPUT_FILE, jsContent, 'utf-8');
  console.log(`\n✅ 数据文件已生成: ${OUTPUT_FILE}`);
  console.log(`📊 统计信息:`);
  console.log(`  - 总文件数: ${allData.length}`);
  console.log(`  - 分批数量: ${batches.length}`);
  console.log(`  - 每批大小: ${BATCH_SIZE}`);
  console.log(`\n🚀 使用方法:`);
  console.log(`1. 在小程序开发者工具控制台中加载生成的文件`);
  console.log(`2. 执行 checkUploadStatus() 检查当前状态`);
  console.log(`3. 执行 uploadAllBatches() 开始批量上传`);
}

// 运行脚本
if (require.main === module) {
  generateKnowledgeData();
}

module.exports = {
  generateKnowledgeData,
  parseFilename,
  extractKeywords
};
