# 知识库上传脚本使用说明

## 📁 文件说明
- 原文件 knowledge-data.js 已分割为 99 个部分
- 每个部分约 100 行，避免控制台崩溃

## 🚀 使用步骤

### 1. 打开微信开发者工具
- 进入您的小程序项目 miniprogram-5
- 打开控制台 (Console)

### 2. 按顺序执行脚本
依次复制粘贴以下文件内容到控制台：

1. part-001.js
2. part-002.js
3. part-003.js
4. part-004.js
5. part-005.js
6. part-006.js
7. part-007.js
8. part-008.js
9. part-009.js
10. part-010.js
11. part-011.js
12. part-012.js
13. part-013.js
14. part-014.js
15. part-015.js
16. part-016.js
17. part-017.js
18. part-018.js
19. part-019.js
20. part-020.js
21. part-021.js
22. part-022.js
23. part-023.js
24. part-024.js
25. part-025.js
26. part-026.js
27. part-027.js
28. part-028.js
29. part-029.js
30. part-030.js
31. part-031.js
32. part-032.js
33. part-033.js
34. part-034.js
35. part-035.js
36. part-036.js
37. part-037.js
38. part-038.js
39. part-039.js
40. part-040.js
41. part-041.js
42. part-042.js
43. part-043.js
44. part-044.js
45. part-045.js
46. part-046.js
47. part-047.js
48. part-048.js
49. part-049.js
50. part-050.js
51. part-051.js
52. part-052.js
53. part-053.js
54. part-054.js
55. part-055.js
56. part-056.js
57. part-057.js
58. part-058.js
59. part-059.js
60. part-060.js
61. part-061.js
62. part-062.js
63. part-063.js
64. part-064.js
65. part-065.js
66. part-066.js
67. part-067.js
68. part-068.js
69. part-069.js
70. part-070.js
71. part-071.js
72. part-072.js
73. part-073.js
74. part-074.js
75. part-075.js
76. part-076.js
77. part-077.js
78. part-078.js
79. part-079.js
80. part-080.js
81. part-081.js
82. part-082.js
83. part-083.js
84. part-084.js
85. part-085.js
86. part-086.js
87. part-087.js
88. part-088.js
89. part-089.js
90. part-090.js
91. part-091.js
92. part-092.js
93. part-093.js
94. part-094.js
95. part-095.js
96. part-096.js
97. part-097.js
98. part-098.js
99. part-099.js

### 3. 开始上传
所有部分执行完成后，运行以下命令：

```javascript
// 检查当前状态
checkUploadStatus()

// 一键上传所有批次
uploadAllBatches()

// 或者分批上传
uploadBatch(1)  // 上传第1批
uploadBatch(2)  // 上传第2批
// ... 依此类推到第30批
```

## 📊 数据统计
- 总文件数: 438个
- 分批数量: 30批
- 每批大小: 15个文件

## ⚠️ 注意事项
1. 必须按顺序执行所有部分
2. 确保每个部分都执行成功
3. 网络稳定时建议使用 uploadAllBatches()
4. 网络不稳定时建议使用 uploadBatch(n) 分批上传
