const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { userId } = event
  
  try {
    const orders = await db.collection('orders')
      .where({ userId })
      .orderBy('createTime', 'desc')
      .get()
      
    return {
      success: true,
      data: orders.data
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}