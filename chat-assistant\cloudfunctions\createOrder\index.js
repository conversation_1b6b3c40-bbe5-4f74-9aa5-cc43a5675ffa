const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

// 生成订单号
function generateOrderId() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const date = String(now.getDate()).padStart(2, '0')
  const random = Math.random().toString().substr(2, 6)
  return `${year}${month}${date}${random}`
}

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext()
  const { price, days, productName } = event
  
  try {
    // 创建订单记录
    const order = {
      _id: generateOrderId(),
      userId: OPENID,
      price: price,
      days: days,
      productName: productName,
      status: 'pending', // pending, success, fail
      createTime: db.serverDate(),
      updateTime: db.serverDate(),
      payTime: null,
      tradeNo: null, // 微信支付订单号
      refundStatus: null,
      refundTime: null,
      remark: ''
    }
    
    // 保存订单到数据库
    await db.collection('orders').add({
      data: order
    })

    // 生成支付参数
    const res = await cloud.cloudPay.unifiedOrder({
      body: productName, // 商品描述
      outTradeNo: order._id, // 商户订单号
      spbillCreateIp: '127.0.0.1', // IP地址
      subMchId: '1702832956', // 你的商户号
      totalFee: Math.floor(price * 100), // 单位为分，需要向下取整
      envId: cloud.DYNAMIC_CURRENT_ENV,
      functionName: 'payCallback', // 支付回调云函数名称
      nonceStr: Math.random().toString(36).substr(2, 15), // 随机字符串
      tradeType: 'JSAPI' // 交易类型
    })

    if (!res.returnCode || res.returnCode !== 'SUCCESS') {
      throw new Error('支付订单创建失败：' + res.returnMsg)
    }

    // 更新订单状态
    await db.collection('orders').doc(order._id).update({
      data: {
        tradeNo: res.payment.tradeNo || null,
        updateTime: db.serverDate()
      }
    })

    return {
      success: true,
      orderId: order._id,
      payment: res.payment // 返回支付所需参数
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    
    // 如果订单已创建，更新订单状态为失败
    if (order && order._id) {
      try {
        await db.collection('orders').doc(order._id).update({
          data: {
            status: 'fail',
            updateTime: db.serverDate(),
            remark: error.message || '支付订单创建失败'
          }
        })
      } catch (updateError) {
        console.error('更新订单状态失败:', updateError)
      }
    }

    return {
      success: false,
      error: error.message || '创建订单失败',
      orderId: order ? order._id : null
    }
  }
}