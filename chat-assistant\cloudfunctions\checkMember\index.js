const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { userId } = event
  
  try {
    const user = await db.collection('users').doc(userId).get()
    return {
      success: true,
      isMember: <PERSON><PERSON><PERSON>(user.data.isMember),
      memberExpireDate: user.data.memberExpireDate
    }
  } catch (error) {
    // 如果用户不存在，创建新用户
    if(error.errCode === -1) {
      await db.collection('users').add({
        data: {
          _id: userId,
          isMember: false,
          memberExpireDate: null,
          createTime: db.serverDate()
        }
      })
      return {
        success: true,
        isMember: false,
        memberExpireDate: null
      }
    }
    return {
      success: false,
      error
    }
  }
}