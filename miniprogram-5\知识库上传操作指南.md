# 知识库上传操作指南

## 第一步：部署云函数

### 1.1 部署knowledge-upload云函数
1. 在微信开发者工具中，右键点击 `cloudfunctions/knowledge-upload` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成（可能需要1-2分钟）

### 1.2 部署knowledge-search云函数
1. 右键点击 `cloudfunctions/knowledge-search` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

## 第二步：创建数据库集合

1. 在微信开发者工具中，点击顶部的"云开发"按钮
2. 进入"数据库"页面
3. 点击"添加集合"按钮
4. 集合名称输入：`knowledge_base`
5. 点击确定创建

## 第三步：准备文件数据

由于小程序无法直接读取本地文件系统，您需要手动准备文件数据。有以下几种方式：

### 方式一：使用上传页面（推荐新手）
1. 在小程序中访问 `/pages/upload/upload` 页面
2. 点击"检查云端数据"查看当前状态
3. 点击"开始上传知识库"（目前只有示例数据）

### 方式二：使用控制台脚本（推荐）
1. 在微信开发者工具中，打开"调试器"
2. 切换到"Console"标签
3. 将 `batch-upload-script.js` 文件内容复制粘贴到控制台
4. 按回车执行，会看到提示信息
5. 执行以下命令：
   ```javascript
   // 检查云端数据
   checkCloudData()
   
   // 上传示例数据
   batchUploadKnowledge()
   
   // 再次检查确认上传成功
   checkCloudData()
   ```

### 方式三：手动添加文件数据
1. 编辑 `batch-upload-script.js` 文件
2. 在 `KNOWLEDGE_FILES_DATA` 数组中添加更多文件数据
3. 每个文件对象格式如下：
   ```javascript
   {
     filePath: '文件名.txt',
     content: '文件内容...'
   }
   ```

## 第四步：批量上传真实数据

### 4.1 准备文件内容
您需要将 `knowledge` 目录下的所有txt文件内容手动复制到脚本中：

1. 打开一个txt文件（如 `周易本义-宋-朱熹.txt`）
2. 复制全部内容
3. 在 `batch-upload-script.js` 中添加：
   ```javascript
   {
     filePath: '周易本义-宋-朱熹.txt',
     content: `这里粘贴文件内容...`
   }
   ```

### 4.2 分批上传
由于文件较多（249个），建议分批上传：

1. 每次准备20-30个文件的数据
2. 执行 `batchUploadKnowledge()` 上传
3. 检查上传结果
4. 继续下一批

### 4.3 自动化脚本（高级用户）
如果您熟悉Node.js，可以创建一个本地脚本：

```javascript
// 在项目根目录创建 upload-helper.js
const fs = require('fs');
const path = require('path');

const knowledgeDir = './knowledge';
const files = fs.readdirSync(knowledgeDir);

const fileData = files
  .filter(f => f.endsWith('.txt'))
  .map(filename => {
    const content = fs.readFileSync(path.join(knowledgeDir, filename), 'utf8');
    return {
      filePath: filename,
      content: content
    };
  });

console.log('生成的文件数据：');
console.log(JSON.stringify(fileData, null, 2));
```

## 第五步：验证上传结果

### 5.1 检查数据库
1. 在云开发控制台的数据库页面
2. 查看 `knowledge_base` 集合
3. 确认记录数量和内容

### 5.2 测试搜索功能
1. 在小程序中访问古籍查询页面
2. 搜索一些关键词（如"乾卦"、"紫微"）
3. 确认能够返回正确的搜索结果

## 常见问题

### Q1: 云函数部署失败
A: 检查网络连接，确保已登录微信开发者账号，重试部署。

### Q2: 数据库集合创建失败
A: 确保已开通云开发服务，检查权限设置。

### Q3: 上传时提示超时
A: 单个文件内容过大，建议分段上传或减少单次上传的文件数量。

### Q4: 搜索无结果
A: 检查数据是否上传成功，确认搜索关键词在文件内容中存在。

## 注意事项

1. **数据备份**：上传前建议备份原始文件
2. **分批上传**：避免一次性上传过多文件导致超时
3. **内容检查**：确保文件内容格式正确，避免特殊字符问题
4. **权限设置**：确保云函数和数据库权限配置正确
5. **成本控制**：注意云开发的使用量，避免超出免费额度

## 完成后的功能

上传完成后，您的小程序将具备：

1. **真实古籍搜索**：在古籍查询页面可以搜索真实的古籍内容
2. **AI知识库集成**：AI分析时会引用真实的古籍内容
3. **分类浏览**：可以按易经、八字、紫微等分类浏览
4. **内容预览**：搜索结果显示匹配的内容片段

祝您上传顺利！如有问题请随时询问。
