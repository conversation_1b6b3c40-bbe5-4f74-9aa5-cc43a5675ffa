{"version": "2.0.0", "name": "har-schema", "description": "JSON Schema for HTTP Archive (HAR)", "author": "<PERSON> <<EMAIL>> (https://www.ahmadnassri.com/)", "contributors": ["<PERSON><PERSON><PERSON> <e.pobe<PERSON><PERSON>@me.com>"], "homepage": "https://github.com/ahmadnassri/har-schema", "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-schema.git"}, "license": "ISC", "main": "lib/index.js", "keywords": ["har", "http", "archive", "JSON", "schema", "JSON-schema"], "engines": {"node": ">=4"}, "files": ["lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-schema/issues"}, "scripts": {"test": "tap test --reporter spec", "pretest": "snazzy && echint", "coverage": "tap test --reporter silent --coverage", "codeclimate": "tap --coverage-report=text-lcov | codeclimate-test-reporter", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "devDependencies": {"ajv": "^5.0.0", "codeclimate-test-reporter": "^0.4.0", "cz-conventional-changelog": "^1.2.0", "echint": "^2.1.0", "semantic-release": "^6.3.2", "snazzy": "^5.0.0", "tap": "^8.0.1"}}