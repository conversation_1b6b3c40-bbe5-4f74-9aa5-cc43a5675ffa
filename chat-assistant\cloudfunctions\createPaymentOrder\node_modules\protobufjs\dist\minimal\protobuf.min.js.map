{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/index-minimal", "../src/reader.js", "../src/reader_buffer.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "floor", "log", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "Uint8Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "inquire", "moduleName", "mod", "eval", "Object", "keys", "e", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "_configure", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "build", "rpc", "roots", "LongBits", "indexOutOfRange", "reader", "write<PERSON><PERSON>th", "RangeError", "create", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "value", "isArray", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "bytes", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "skip", "skipType", "wireType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "toString", "Service", "rpcImpl", "requestDelimited", "responseDelimited", "TypeError", "Boolean", "rpcCall", "method", "requestCtor", "responseCtor", "request", "callback", "self", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "finish", "response", "endedByRPC", "zero", "toNumber", "zzEncode", "zeroHash", "fromNumber", "from", "isString", "parseInt", "fromString", "low", "high", "unsigned", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "ifNotSet", "newError", "CustomError", "message", "properties", "defineProperty", "get", "captureStackTrace", "stack", "writable", "enumerable", "configurable", "set", "pool", "isNode", "process", "versions", "node", "window", "emptyArray", "freeze", "emptyObject", "isInteger", "Number", "isFinite", "isObject", "isset", "isSet", "obj", "prop", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "new<PERSON>uffer", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "lcFirst", "str", "toLowerCase", "substring", "ProtocolError", "oneOfGetter", "fieldNames", "fieldMap", "oneOfSetter", "toJSONOptions", "longs", "enums", "json", "encoding", "allocUnsafe", "Op", "next", "noop", "State", "writer", "head", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "fork", "reset", "l<PERSON>im", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;AAAA,CAAA,SAAAA,GAAA,aAAA,CAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,EAAA,EAAAJ,EAAAE,EAAAA,EAAAE,OAAA,EACAF,EAAAE,OACA,EAEAN,EAAA,EAAA,EAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,YAAA,OAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,UAAA,GAEAZ,CACA,CAAA,EAGA,UAAA,OAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,EAEA,EAAA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,CAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,CAAA,IAAAF,UAAAG,CAAA,IACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,EAAA,CAAA,EACAI,EACAD,EAAAC,CAAA,MACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,CAAA,IAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,CAAA,CACA,CAEA,EACA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,CAAA,CAMA,CALA,MAAAU,GACAJ,IACAA,EAAA,CAAA,EACAG,EAAAC,CAAA,EAEA,CACA,CAAA,CACA,C,yBCrCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,GAAA,CAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,EAAA,EAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,KACA,EAAAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,MAAA,EAAA,EAAAY,CACA,EASA,IAxBA,IAkBAG,EAAAjB,MAAA,EAAA,EAGAkB,EAAAlB,MAAA,GAAA,EAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,CAAA,GASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,CAAA,IACA,OAAAK,GACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,CAAA,IAAAF,EAAA,GAAAW,GACAD,EAAA,CAEA,CACA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,EAEA,CAOA,OANAQ,IACAD,EAAAP,CAAA,IAAAF,EAAAO,GACAE,EAAAP,CAAA,IAAA,GACA,IAAAQ,IACAD,EAAAP,CAAA,IAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EAEA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,CAAA,EAAA,EACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,EACA,MAAA6D,MAAAJ,CAAA,EACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,IAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,CAEA,CACA,CACA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,CAAA,EACA,OAAA/B,EAAAmB,CACA,EAOAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,CAAA,CACA,C,yBCjIA,SAAA4B,IAOAC,KAAAC,EAAA,EACA,EAhBAhD,EAAAR,QAAAsD,GAyBAG,UAAAC,GAAA,SAAAC,EAAAhD,EAAAC,GAKA,OAJA2C,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAAhB,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAA2C,IACA,CAAA,EACAA,IACA,EAQAD,EAAAG,UAAAG,IAAA,SAAAD,EAAAhD,GACA,GAAAgD,IAAApE,EACAgE,KAAAC,EAAA,QAEA,GAAA7C,IAAApB,EACAgE,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACA1B,EAAA,EAAAA,EAAA4B,EAAA7C,QACA6C,EAAA5B,GAAAtB,KAAAA,EACAkD,EAAAC,OAAA7B,EAAA,CAAA,EAEA,EAAAA,EAGA,OAAAsB,IACA,EAQAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA/B,EAAA,EACAA,EAAAlB,UAAAC,QACAgD,EAAArB,KAAA5B,UAAAkB,CAAA,GAAA,EACA,IAAAA,EAAA,EAAAA,EAAA4B,EAAA7C,QACA6C,EAAA5B,GAAAtB,GAAAa,MAAAqC,EAAA5B,CAAA,IAAArB,IAAAoD,CAAA,CACA,CACA,OAAAT,IACA,C,yBCYA,SAAAU,EAAAjE,GAsDA,SAAAkE,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,EACA,CAAAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,CAAA,EACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA1C,KAAA4C,MAAAL,EAAA,oBAAA,KAAA,GAIAG,GAAA,GAAA,KAFAG,EAAA7C,KAAA8C,MAAA9C,KAAA+C,IAAAR,CAAA,EAAAvC,KAAAgD,GAAA,IAEA,GADA,QAAAhD,KAAA4C,MAAAL,EAAAvC,KAAAiD,IAAA,EAAA,CAAAJ,CAAA,EAAA,OAAA,KACA,EAVAL,EAAAC,CAAA,CAYA,CAKA,SAAAS,EAAAC,EAAAX,EAAAC,GACAW,EAAAD,EAAAX,EAAAC,CAAA,EACAC,EAAA,GAAAU,GAAA,IAAA,EACAP,EAAAO,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAR,EACAQ,EACAC,IACAC,EAAAA,EAAAb,EACA,GAAAG,EACA,qBAAAH,EAAAW,EACAX,EAAA1C,KAAAiD,IAAA,EAAAJ,EAAA,GAAA,GAAA,QAAAQ,EACA,CA/EA,SAAAG,EAAAjB,EAAAC,EAAAC,GACAgB,EAAA,GAAAlB,EACAC,EAAAC,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,EACA,CAEA,SAAAC,EAAApB,EAAAC,EAAAC,GACAgB,EAAA,GAAAlB,EACAC,EAAAC,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,EACA,CAOA,SAAAE,EAAApB,EAAAC,GAKA,OAJAiB,EAAA,GAAAlB,EAAAC,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAgB,EAAA,EACA,CAEA,SAAAI,EAAArB,EAAAC,GAKA,OAJAiB,EAAA,GAAAlB,EAAAC,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAgB,EAAA,EACA,CAzCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAA1B,EAAA2B,EAAAC,EAAA3B,EAAAC,EAAAC,GACA,IAaAY,EAbAX,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,EACA,CAAAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAwB,CAAA,EACA3B,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAyB,CAAA,GACAvB,MAAAJ,CAAA,GACAD,EAAA,EAAAE,EAAAC,EAAAwB,CAAA,EACA3B,EAAA,WAAAE,EAAAC,EAAAyB,CAAA,GACA,sBAAA3B,GACAD,EAAA,EAAAE,EAAAC,EAAAwB,CAAA,EACA3B,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAyB,CAAA,GAGA3B,EAAA,wBAEAD,GADAe,EAAAd,EAAA,UACA,EAAAC,EAAAC,EAAAwB,CAAA,EACA3B,GAAAI,GAAA,GAAAW,EAAA,cAAA,EAAAb,EAAAC,EAAAyB,CAAA,IAMA5B,EAAA,kBADAe,EAAAd,EAAAvC,KAAAiD,IAAA,EAAA,EADAJ,EADA,QADAA,EAAA7C,KAAA8C,MAAA9C,KAAA+C,IAAAR,CAAA,EAAAvC,KAAAgD,GAAA,GAEA,KACAH,EAAA,KACA,EAAAL,EAAAC,EAAAwB,CAAA,EACA3B,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAQ,EAAA,WAAA,EAAAb,EAAAC,EAAAyB,CAAA,EAGA,CAKA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAA1B,EAAAC,GACA2B,EAAAjB,EAAAX,EAAAC,EAAAwB,CAAA,EACAI,EAAAlB,EAAAX,EAAAC,EAAAyB,CAAA,EACAxB,EAAA,GAAA2B,GAAA,IAAA,EACAxB,EAAAwB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAAvB,EACAQ,EACAC,IACAC,EAAAA,EAAAb,EACA,GAAAG,EACA,OAAAH,EAAAW,EACAX,EAAA1C,KAAAiD,IAAA,EAAAJ,EAAA,IAAA,GAAAQ,EAAA,iBACA,CA3GA,SAAAiB,EAAA/B,EAAAC,EAAAC,GACAqB,EAAA,GAAAvB,EACAC,EAAAC,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,EACA,CAEA,SAAAa,EAAAhC,EAAAC,EAAAC,GACAqB,EAAA,GAAAvB,EACAC,EAAAC,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,GACAlB,EAAAC,EAAA,GAAAiB,EAAA,EACA,CAOA,SAAAc,EAAAhC,EAAAC,GASA,OARAiB,EAAA,GAAAlB,EAAAC,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAqB,EAAA,EACA,CAEA,SAAAW,EAAAjC,EAAAC,GASA,OARAiB,EAAA,GAAAlB,EAAAC,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAiB,EAAA,GAAAlB,EAAAC,EAAA,GACAqB,EAAA,EACA,CA+DA,MArNA,aAAA,OAAAY,cAEAjB,EAAA,IAAAiB,aAAA,CAAA,CAAA,EAAA,EACAhB,EAAA,IAAAiB,WAAAlB,EAAAnD,MAAA,EACAyD,EAAA,MAAAL,EAAA,GAmBAvF,EAAAyG,aAAAb,EAAAP,EAAAG,EAEAxF,EAAA0G,aAAAd,EAAAJ,EAAAH,EAmBArF,EAAA2G,YAAAf,EAAAH,EAAAC,EAEA1F,EAAA4G,YAAAhB,EAAAF,EAAAD,IAwBAzF,EAAAyG,aAAAvC,EAAA2C,KAAA,KAAAC,CAAA,EACA9G,EAAA0G,aAAAxC,EAAA2C,KAAA,KAAAE,CAAA,EAgBA/G,EAAA2G,YAAA5B,EAAA8B,KAAA,KAAAG,CAAA,EACAhH,EAAA4G,YAAA7B,EAAA8B,KAAA,KAAAI,CAAA,GAKA,aAAA,OAAAC,cAEAvB,EAAA,IAAAuB,aAAA,CAAA,CAAA,EAAA,EACA3B,EAAA,IAAAiB,WAAAb,EAAAxD,MAAA,EACAyD,EAAA,MAAAL,EAAA,GA2BAvF,EAAAmH,cAAAvB,EAAAO,EAAAC,EAEApG,EAAAoH,cAAAxB,EAAAQ,EAAAD,EA2BAnG,EAAAqH,aAAAzB,EAAAS,EAAAC,EAEAtG,EAAAsH,aAAA1B,EAAAU,EAAAD,IAmCArG,EAAAmH,cAAAtB,EAAAgB,KAAA,KAAAC,EAAA,EAAA,CAAA,EACA9G,EAAAoH,cAAAvB,EAAAgB,KAAA,KAAAE,EAAA,EAAA,CAAA,EAiBA/G,EAAAqH,aAAArB,EAAAa,KAAA,KAAAG,EAAA,EAAA,CAAA,EACAhH,EAAAsH,aAAAtB,EAAAa,KAAA,KAAAI,EAAA,EAAA,CAAA,GAIAjH,CACA,CAIA,SAAA8G,EAAA1C,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CAEA,SAAA2C,EAAA3C,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,CACA,CAEA,SAAA4C,EAAA3C,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,CACA,CAEA,SAAA2C,EAAA5C,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,CACA,CA5UA9D,EAAAR,QAAAiE,EAAAA,CAAA,C,yBCOA,SAAAsD,EAAAC,GACA,IACA,IAAAC,EAAAC,KAAA,SAAA,EAAAF,CAAA,EACA,GAAAC,IAAAA,EAAAzG,QAAA2G,OAAAC,KAAAH,CAAA,EAAAzG,QACA,OAAAyG,CACA,CAAA,MAAAI,IACA,OAAA,IACA,CAfArH,EAAAR,QAAAuH,C,yBCAA/G,EAAAR,QA6BA,SAAA8H,EAAAhF,EAAAiF,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAjH,EAAA+G,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,CAAA,EACAC,EAAA/G,EAAA8G,IACAG,EAAAJ,EAAAE,CAAA,EACA/G,EAAA,GAEAoD,EAAAvB,EAAA/C,KAAAmI,EAAAjH,EAAAA,GAAA8G,CAAA,EAGA,OAFA,EAAA9G,IACAA,EAAA,GAAA,EAAAA,IACAoD,CACA,CACA,C,yBCjCA8D,EAAAnH,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAkF,EAAA,EAEAnG,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,CAAA,GACA,IACAmG,GAAA,EACAlF,EAAA,KACAkF,GAAA,EACA,QAAA,MAAAlF,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,CAAA,IACA,EAAAA,EACAmG,GAAA,GAEAA,GAAA,EAEA,OAAAA,CACA,EASAD,EAAAE,KAAA,SAAAlG,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,CAAA,KACA,IACAI,EAAAP,CAAA,IAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,CAAA,IACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,IAAA,GAAAD,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,KAAA,MACAI,EAAAP,CAAA,IAAA,OAAAK,GAAA,IACAE,EAAAP,CAAA,IAAA,OAAA,KAAAK,IAEAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,IACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EASAkG,EAAAG,MAAA,SAAA5G,EAAAS,EAAAlB,GAIA,IAHA,IACAsH,EACAC,EAFApG,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACAsG,EAAA7G,EAAAyB,WAAAlB,CAAA,GACA,IACAE,EAAAlB,CAAA,IAAAsH,GACAA,EAAA,KACApG,EAAAlB,CAAA,IAAAsH,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAA9G,EAAAyB,WAAAlB,EAAA,CAAA,KAEA,EAAAA,EACAE,EAAAlB,CAAA,KAFAsH,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACArG,EAAAlB,CAAA,IAAAsH,GAAA,GAAA,GAAA,KAIApG,EAAAlB,CAAA,IAAAsH,GAAA,GAAA,IAHApG,EAAAlB,CAAA,IAAAsH,GAAA,EAAA,GAAA,KANApG,EAAAlB,CAAA,IAAA,GAAAsH,EAAA,KAcA,OAAAtH,EAAAmB,CACA,C,yBCvGA,IAAAzC,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAAwI,EAAA,EACA9I,EAAA+I,OAAAD,EAAA9I,EAAAgJ,YAAA,EACAhJ,EAAAiJ,OAAAH,EAAA9I,EAAAkJ,YAAA,CACA,CAvBAlJ,EAAAmJ,MAAA,UAGAnJ,EAAA+I,OAAAhI,EAAA,EAAA,EACAf,EAAAgJ,aAAAjI,EAAA,EAAA,EACAf,EAAAiJ,OAAAlI,EAAA,CAAA,EACAf,EAAAkJ,aAAAnI,EAAA,EAAA,EAGAf,EAAAM,KAAAS,EAAA,EAAA,EACAf,EAAAoJ,IAAArI,EAAA,EAAA,EACAf,EAAAqJ,MAAAtI,EAAA,EAAA,EACAf,EAAAY,UAAAA,EAcAA,EAAA,C,gEClCAC,EAAAR,QAAA4I,EAEA,IAEAC,EAFA5I,EAAAS,EAAA,EAAA,EAIAuI,EAAAhJ,EAAAgJ,SACAd,EAAAlI,EAAAkI,KAGA,SAAAe,EAAAC,EAAAC,GACA,OAAAC,WAAA,uBAAAF,EAAA7E,IAAA,OAAA8E,GAAA,GAAA,MAAAD,EAAAf,GAAA,CACA,CAQA,SAAAQ,EAAAzG,GAMAoB,KAAAc,IAAAlC,EAMAoB,KAAAe,IAAA,EAMAf,KAAA6E,IAAAjG,EAAAnB,MACA,CAeA,SAAAsI,IACA,OAAArJ,EAAAsJ,OACA,SAAApH,GACA,OAAAyG,EAAAU,OAAA,SAAAnH,GACA,OAAAlC,EAAAsJ,OAAAC,SAAArH,CAAA,EACA,IAAA0G,EAAA1G,CAAA,EAEAsH,EAAAtH,CAAA,CACA,GAAAA,CAAA,CACA,EAEAsH,CACA,CAzBA,IA4CAC,EA5CAD,EAAA,aAAA,OAAAjD,WACA,SAAArE,GACA,GAAAA,aAAAqE,YAAA1F,MAAA6I,QAAAxH,CAAA,EACA,OAAA,IAAAyG,EAAAzG,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAEA,SAAAjB,GACA,GAAArB,MAAA6I,QAAAxH,CAAA,EACA,OAAA,IAAAyG,EAAAzG,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAqEA,SAAAwG,IAEA,IAAAC,EAAA,IAAAZ,EAAA,EAAA,CAAA,EACAhH,EAAA,EACA,GAAAsB,EAAA,EAAAA,KAAA6E,IAAA7E,KAAAe,KAaA,CACA,KAAArC,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAsB,KAAAe,KAAAf,KAAA6E,IACA,MAAAc,EAAA3F,IAAA,EAGA,GADAsG,EAAA5D,IAAA4D,EAAA5D,IAAA,IAAA1C,KAAAc,IAAAd,KAAAe,OAAA,EAAArC,KAAA,EACAsB,KAAAc,IAAAd,KAAAe,GAAA,IAAA,IACA,OAAAuF,CACA,CAGA,OADAA,EAAA5D,IAAA4D,EAAA5D,IAAA,IAAA1C,KAAAc,IAAAd,KAAAe,GAAA,MAAA,EAAArC,KAAA,EACA4H,CACA,CAzBA,KAAA5H,EAAA,EAAA,EAAAA,EAGA,GADA4H,EAAA5D,IAAA4D,EAAA5D,IAAA,IAAA1C,KAAAc,IAAAd,KAAAe,OAAA,EAAArC,KAAA,EACAsB,KAAAc,IAAAd,KAAAe,GAAA,IAAA,IACA,OAAAuF,EAKA,GAFAA,EAAA5D,IAAA4D,EAAA5D,IAAA,IAAA1C,KAAAc,IAAAd,KAAAe,OAAA,MAAA,EACAuF,EAAA3D,IAAA2D,EAAA3D,IAAA,IAAA3C,KAAAc,IAAAd,KAAAe,OAAA,KAAA,EACAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,IACA,OAAAuF,EAgBA,GAfA5H,EAAA,EAeA,EAAAsB,KAAA6E,IAAA7E,KAAAe,KACA,KAAArC,EAAA,EAAA,EAAAA,EAGA,GADA4H,EAAA3D,IAAA2D,EAAA3D,IAAA,IAAA3C,KAAAc,IAAAd,KAAAe,OAAA,EAAArC,EAAA,KAAA,EACAsB,KAAAc,IAAAd,KAAAe,GAAA,IAAA,IACA,OAAAuF,CACA,MAEA,KAAA5H,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAsB,KAAAe,KAAAf,KAAA6E,IACA,MAAAc,EAAA3F,IAAA,EAGA,GADAsG,EAAA3D,IAAA2D,EAAA3D,IAAA,IAAA3C,KAAAc,IAAAd,KAAAe,OAAA,EAAArC,EAAA,KAAA,EACAsB,KAAAc,IAAAd,KAAAe,GAAA,IAAA,IACA,OAAAuF,CACA,CAGA,MAAAzG,MAAA,yBAAA,CACA,CAiCA,SAAA0G,EAAAzF,EAAAhC,GACA,OAAAgC,EAAAhC,EAAA,GACAgC,EAAAhC,EAAA,IAAA,EACAgC,EAAAhC,EAAA,IAAA,GACAgC,EAAAhC,EAAA,IAAA,MAAA,CACA,CA8BA,SAAA0H,IAGA,GAAAxG,KAAAe,IAAA,EAAAf,KAAA6E,IACA,MAAAc,EAAA3F,KAAA,CAAA,EAEA,OAAA,IAAA0F,EAAAa,EAAAvG,KAAAc,IAAAd,KAAAe,KAAA,CAAA,EAAAwF,EAAAvG,KAAAc,IAAAd,KAAAe,KAAA,CAAA,CAAA,CACA,CA5KAsE,EAAAU,OAAAA,EAAA,EAEAV,EAAAnF,UAAAuG,EAAA/J,EAAAa,MAAA2C,UAAAwG,UAAAhK,EAAAa,MAAA2C,UAAAX,MAOA8F,EAAAnF,UAAAyG,QACAR,EAAA,WACA,WACA,GAAAA,GAAA,IAAAnG,KAAAc,IAAAd,KAAAe,QAAA,EAAAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,MACAoF,GAAAA,GAAA,IAAAnG,KAAAc,IAAAd,KAAAe,OAAA,KAAA,EAAAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,MACAoF,GAAAA,GAAA,IAAAnG,KAAAc,IAAAd,KAAAe,OAAA,MAAA,EAAAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,MACAoF,GAAAA,GAAA,IAAAnG,KAAAc,IAAAd,KAAAe,OAAA,MAAA,EAAAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,MACAoF,GAAAA,GAAA,GAAAnG,KAAAc,IAAAd,KAAAe,OAAA,MAAA,EAAAf,KAAAc,IAAAd,KAAAe,GAAA,IAAA,KAGA,GAAAf,KAAAe,KAAA,GAAAf,KAAA6E,SAIA,OAAAsB,EAFA,MADAnG,KAAAe,IAAAf,KAAA6E,IACAc,EAAA3F,KAAA,EAAA,CAGA,GAOAqF,EAAAnF,UAAA0G,MAAA,WACA,OAAA,EAAA5G,KAAA2G,OAAA,CACA,EAMAtB,EAAAnF,UAAA2G,OAAA,WACA,IAAAV,EAAAnG,KAAA2G,OAAA,EACA,OAAAR,IAAA,EAAA,EAAA,EAAAA,GAAA,CACA,EAoFAd,EAAAnF,UAAA4G,KAAA,WACA,OAAA,IAAA9G,KAAA2G,OAAA,CACA,EAaAtB,EAAAnF,UAAA6G,QAAA,WAGA,GAAA/G,KAAAe,IAAA,EAAAf,KAAA6E,IACA,MAAAc,EAAA3F,KAAA,CAAA,EAEA,OAAAuG,EAAAvG,KAAAc,IAAAd,KAAAe,KAAA,CAAA,CACA,EAMAsE,EAAAnF,UAAA8G,SAAA,WAGA,GAAAhH,KAAAe,IAAA,EAAAf,KAAA6E,IACA,MAAAc,EAAA3F,KAAA,CAAA,EAEA,OAAA,EAAAuG,EAAAvG,KAAAc,IAAAd,KAAAe,KAAA,CAAA,CACA,EAkCAsE,EAAAnF,UAAA+G,MAAA,WAGA,GAAAjH,KAAAe,IAAA,EAAAf,KAAA6E,IACA,MAAAc,EAAA3F,KAAA,CAAA,EAEA,IAAAmG,EAAAzJ,EAAAuK,MAAA7D,YAAApD,KAAAc,IAAAd,KAAAe,GAAA,EAEA,OADAf,KAAAe,KAAA,EACAoF,CACA,EAOAd,EAAAnF,UAAAgH,OAAA,WAGA,GAAAlH,KAAAe,IAAA,EAAAf,KAAA6E,IACA,MAAAc,EAAA3F,KAAA,CAAA,EAEA,IAAAmG,EAAAzJ,EAAAuK,MAAAnD,aAAA9D,KAAAc,IAAAd,KAAAe,GAAA,EAEA,OADAf,KAAAe,KAAA,EACAoF,CACA,EAMAd,EAAAnF,UAAAiH,MAAA,WACA,IAAA1J,EAAAuC,KAAA2G,OAAA,EACA9H,EAAAmB,KAAAe,IACAjC,EAAAkB,KAAAe,IAAAtD,EAGA,GAAAqB,EAAAkB,KAAA6E,IACA,MAAAc,EAAA3F,KAAAvC,CAAA,EAGA,OADAuC,KAAAe,KAAAtD,EACAF,MAAA6I,QAAApG,KAAAc,GAAA,EACAd,KAAAc,IAAAvB,MAAAV,EAAAC,CAAA,EAEAD,IAAAC,GACAsI,EAAA1K,EAAAsJ,QAEAoB,EAAA7C,MAAA,CAAA,EACA,IAAAvE,KAAAc,IAAAuG,YAAA,CAAA,EAEArH,KAAAyG,EAAAjK,KAAAwD,KAAAc,IAAAjC,EAAAC,CAAA,CACA,EAMAuG,EAAAnF,UAAA/B,OAAA,WACA,IAAAgJ,EAAAnH,KAAAmH,MAAA,EACA,OAAAvC,EAAAE,KAAAqC,EAAA,EAAAA,EAAA1J,MAAA,CACA,EAOA4H,EAAAnF,UAAAoH,KAAA,SAAA7J,GACA,GAAA,UAAA,OAAAA,EAAA,CAEA,GAAAuC,KAAAe,IAAAtD,EAAAuC,KAAA6E,IACA,MAAAc,EAAA3F,KAAAvC,CAAA,EACAuC,KAAAe,KAAAtD,CACA,MACA,GAEA,GAAAuC,KAAAe,KAAAf,KAAA6E,IACA,MAAAc,EAAA3F,IAAA,CAAA,OACA,IAAAA,KAAAc,IAAAd,KAAAe,GAAA,KAEA,OAAAf,IACA,EAOAqF,EAAAnF,UAAAqH,SAAA,SAAAC,GACA,OAAAA,GACA,KAAA,EACAxH,KAAAsH,KAAA,EACA,MACA,KAAA,EACAtH,KAAAsH,KAAA,CAAA,EACA,MACA,KAAA,EACAtH,KAAAsH,KAAAtH,KAAA2G,OAAA,CAAA,EACA,MACA,KAAA,EACA,KAAA,IAAAa,EAAA,EAAAxH,KAAA2G,OAAA,IACA3G,KAAAuH,SAAAC,CAAA,EAEA,MACA,KAAA,EACAxH,KAAAsH,KAAA,CAAA,EACA,MAGA,QACA,MAAAzH,MAAA,qBAAA2H,EAAA,cAAAxH,KAAAe,GAAA,CACA,CACA,OAAAf,IACA,EAEAqF,EAAAH,EAAA,SAAAuC,GACAnC,EAAAmC,EACApC,EAAAU,OAAAA,EAAA,EACAT,EAAAJ,EAAA,EAEA,IAAA9H,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAAgL,MAAArC,EAAAnF,UAAA,CAEAyH,MAAA,WACA,OAAAtB,EAAA7J,KAAAwD,IAAA,EAAA5C,GAAA,CAAA,CAAA,CACA,EAEAwK,OAAA,WACA,OAAAvB,EAAA7J,KAAAwD,IAAA,EAAA5C,GAAA,CAAA,CAAA,CACA,EAEAyK,OAAA,WACA,OAAAxB,EAAA7J,KAAAwD,IAAA,EAAA8H,SAAA,EAAA1K,GAAA,CAAA,CAAA,CACA,EAEA2K,QAAA,WACA,OAAAvB,EAAAhK,KAAAwD,IAAA,EAAA5C,GAAA,CAAA,CAAA,CACA,EAEA4K,SAAA,WACA,OAAAxB,EAAAhK,KAAAwD,IAAA,EAAA5C,GAAA,CAAA,CAAA,CACA,CAEA,CAAA,CACA,C,+BC9ZAH,EAAAR,QAAA6I,EAGA,IAAAD,EAAAlI,EAAA,CAAA,EAGAT,IAFA4I,EAAApF,UAAAkE,OAAA2B,OAAAV,EAAAnF,SAAA,GAAAmH,YAAA/B,EAEAnI,EAAA,EAAA,GASA,SAAAmI,EAAA1G,GACAyG,EAAA7I,KAAAwD,KAAApB,CAAA,CAOA,CAEA0G,EAAAJ,EAAA,WAEAxI,EAAAsJ,SACAV,EAAApF,UAAAuG,EAAA/J,EAAAsJ,OAAA9F,UAAAX,MACA,EAMA+F,EAAApF,UAAA/B,OAAA,WACA,IAAA0G,EAAA7E,KAAA2G,OAAA,EACA,OAAA3G,KAAAc,IAAAmH,UACAjI,KAAAc,IAAAmH,UAAAjI,KAAAe,IAAAf,KAAAe,IAAAzC,KAAA4J,IAAAlI,KAAAe,IAAA8D,EAAA7E,KAAA6E,GAAA,CAAA,EACA7E,KAAAc,IAAAqH,SAAA,QAAAnI,KAAAe,IAAAf,KAAAe,IAAAzC,KAAA4J,IAAAlI,KAAAe,IAAA8D,EAAA7E,KAAA6E,GAAA,CAAA,CACA,EASAS,EAAAJ,EAAA,C,mCCjDAjI,EAAAR,QAAA,E,0BCKAA,EA6BA2L,QAAAjL,EAAA,EAAA,C,+BClCAF,EAAAR,QAAA2L,EAEA,IAAA1L,EAAAS,EAAA,EAAA,EAsCA,SAAAiL,EAAAC,EAAAC,EAAAC,GAEA,GAAA,YAAA,OAAAF,EACA,MAAAG,UAAA,4BAAA,EAEA9L,EAAAqD,aAAAvD,KAAAwD,IAAA,EAMAA,KAAAqI,QAAAA,EAMArI,KAAAsI,iBAAAG,CAAAA,CAAAH,EAMAtI,KAAAuI,kBAAAE,CAAAA,CAAAF,CACA,GA3DAH,EAAAlI,UAAAkE,OAAA2B,OAAArJ,EAAAqD,aAAAG,SAAA,GAAAmH,YAAAe,GAwEAlI,UAAAwI,QAAA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAA,CAAAD,EACA,MAAAN,UAAA,2BAAA,EAEA,IAAAQ,EAAAhJ,KACA,GAAA,CAAA+I,EACA,OAAArM,EAAAuM,UAAAP,EAAAM,EAAAL,EAAAC,EAAAC,EAAAC,CAAA,EAEA,GAAA,CAAAE,EAAAX,QAEA,OADAa,WAAA,WAAAH,EAAAlJ,MAAA,eAAA,CAAA,CAAA,EAAA,CAAA,EACA7D,EAGA,IACA,OAAAgN,EAAAX,QACAM,EACAC,EAAAI,EAAAV,iBAAA,kBAAA,UAAAQ,CAAA,EAAAK,OAAA,EACA,SAAAnL,EAAAoL,GAEA,GAAApL,EAEA,OADAgL,EAAAxI,KAAA,QAAAxC,EAAA2K,CAAA,EACAI,EAAA/K,CAAA,EAGA,GAAA,OAAAoL,EAEA,OADAJ,EAAAlK,IAAA,CAAA,CAAA,EACA9C,EAGA,GAAA,EAAAoN,aAAAP,GACA,IACAO,EAAAP,EAAAG,EAAAT,kBAAA,kBAAA,UAAAa,CAAA,CAIA,CAHA,MAAApL,GAEA,OADAgL,EAAAxI,KAAA,QAAAxC,EAAA2K,CAAA,EACAI,EAAA/K,CAAA,CACA,CAIA,OADAgL,EAAAxI,KAAA,OAAA4I,EAAAT,CAAA,EACAI,EAAA,KAAAK,CAAA,CACA,CACA,CAKA,CAJA,MAAApL,GAGA,OAFAgL,EAAAxI,KAAA,QAAAxC,EAAA2K,CAAA,EACAO,WAAA,WAAAH,EAAA/K,CAAA,CAAA,EAAA,CAAA,EACAhC,CACA,CACA,EAOAoM,EAAAlI,UAAApB,IAAA,SAAAuK,GAOA,OANArJ,KAAAqI,UACAgB,GACArJ,KAAAqI,QAAA,KAAA,KAAA,IAAA,EACArI,KAAAqI,QAAA,KACArI,KAAAQ,KAAA,KAAA,EAAAH,IAAA,GAEAL,IACA,C,+BC5IA/C,EAAAR,QAAAiJ,EAEA,IAAAhJ,EAAAS,EAAA,EAAA,EAUA,SAAAuI,EAAAhD,EAAAC,GASA3C,KAAA0C,GAAAA,IAAA,EAMA1C,KAAA2C,GAAAA,IAAA,CACA,CAOA,IAAA2G,EAAA5D,EAAA4D,KAAA,IAAA5D,EAAA,EAAA,CAAA,EAoFA9F,GAlFA0J,EAAAC,SAAA,WAAA,OAAA,CAAA,EACAD,EAAAE,SAAAF,EAAAxB,SAAA,WAAA,OAAA9H,IAAA,EACAsJ,EAAA7L,OAAA,WAAA,OAAA,CAAA,EAOAiI,EAAA+D,SAAA,mBAOA/D,EAAAgE,WAAA,SAAAvD,GACA,IAEAnF,EAGA0B,EALA,OAAA,IAAAyD,EACAmD,GAIA5G,GADAyD,GAFAnF,EAAAmF,EAAA,GAEA,CAAAA,EACAA,KAAA,EACAxD,GAAAwD,EAAAzD,GAAA,aAAA,EACA1B,IACA2B,EAAA,CAAAA,IAAA,EACAD,EAAA,CAAAA,IAAA,EACA,WAAA,EAAAA,IACAA,EAAA,EACA,WAAA,EAAAC,IACAA,EAAA,KAGA,IAAA+C,EAAAhD,EAAAC,CAAA,EACA,EAOA+C,EAAAiE,KAAA,SAAAxD,GACA,GAAA,UAAA,OAAAA,EACA,OAAAT,EAAAgE,WAAAvD,CAAA,EACA,GAAAzJ,EAAAkN,SAAAzD,CAAA,EAAA,CAEA,GAAAzJ,CAAAA,EAAAI,KAGA,OAAA4I,EAAAgE,WAAAG,SAAA1D,EAAA,EAAA,CAAA,EAFAA,EAAAzJ,EAAAI,KAAAgN,WAAA3D,CAAA,CAGA,CACA,OAAAA,EAAA4D,KAAA5D,EAAA6D,KAAA,IAAAtE,EAAAS,EAAA4D,MAAA,EAAA5D,EAAA6D,OAAA,CAAA,EAAAV,CACA,EAOA5D,EAAAxF,UAAAqJ,SAAA,SAAAU,GACA,IAEAtH,EAFA,MAAA,CAAAsH,GAAAjK,KAAA2C,KAAA,IACAD,EAAA,EAAA,CAAA1C,KAAA0C,KAAA,EACAC,EAAA,CAAA3C,KAAA2C,KAAA,EAGA,EAAAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA3C,KAAA0C,GAAA,WAAA1C,KAAA2C,EACA,EAOA+C,EAAAxF,UAAAgK,OAAA,SAAAD,GACA,OAAAvN,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAAkD,KAAA0C,GAAA,EAAA1C,KAAA2C,GAAA8F,CAAAA,CAAAwB,CAAA,EAEA,CAAAF,IAAA,EAAA/J,KAAA0C,GAAAsH,KAAA,EAAAhK,KAAA2C,GAAAsH,SAAAxB,CAAAA,CAAAwB,CAAA,CACA,EAEA5K,OAAAa,UAAAN,YAOA8F,EAAAyE,SAAA,SAAAC,GACA,MAjFA1E,qBAiFA0E,EACAd,EACA,IAAA5D,GACA9F,EAAApD,KAAA4N,EAAA,CAAA,EACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,EACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,GACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,MAAA,GAEAxK,EAAApD,KAAA4N,EAAA,CAAA,EACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,EACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,GACAxK,EAAApD,KAAA4N,EAAA,CAAA,GAAA,MAAA,CACA,CACA,EAMA1E,EAAAxF,UAAAmK,OAAA,WACA,OAAAhL,OAAAC,aACA,IAAAU,KAAA0C,GACA1C,KAAA0C,KAAA,EAAA,IACA1C,KAAA0C,KAAA,GAAA,IACA1C,KAAA0C,KAAA,GACA,IAAA1C,KAAA2C,GACA3C,KAAA2C,KAAA,EAAA,IACA3C,KAAA2C,KAAA,GAAA,IACA3C,KAAA2C,KAAA,EACA,CACA,EAMA+C,EAAAxF,UAAAsJ,SAAA,WACA,IAAAc,EAAAtK,KAAA2C,IAAA,GAGA,OAFA3C,KAAA2C,KAAA3C,KAAA2C,IAAA,EAAA3C,KAAA0C,KAAA,IAAA4H,KAAA,EACAtK,KAAA0C,IAAA1C,KAAA0C,IAAA,EAAA4H,KAAA,EACAtK,IACA,EAMA0F,EAAAxF,UAAA4H,SAAA,WACA,IAAAwC,EAAA,EAAA,EAAAtK,KAAA0C,IAGA,OAFA1C,KAAA0C,KAAA1C,KAAA0C,KAAA,EAAA1C,KAAA2C,IAAA,IAAA2H,KAAA,EACAtK,KAAA2C,IAAA3C,KAAA2C,KAAA,EAAA2H,KAAA,EACAtK,IACA,EAMA0F,EAAAxF,UAAAzC,OAAA,WACA,IAAA8M,EAAAvK,KAAA0C,GACA8H,GAAAxK,KAAA0C,KAAA,GAAA1C,KAAA2C,IAAA,KAAA,EACA8H,EAAAzK,KAAA2C,KAAA,GACA,OAAA,GAAA8H,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,EACA,C,+BCtMA,IAAA/N,EAAAD,EA2OA,SAAAiL,EAAAgD,EAAAC,EAAAC,GACA,IAAA,IAAAvG,EAAAD,OAAAC,KAAAsG,CAAA,EAAAjM,EAAA,EAAAA,EAAA2F,EAAA5G,OAAA,EAAAiB,EACAgM,EAAArG,EAAA3F,MAAA1C,GAAA4O,IACAF,EAAArG,EAAA3F,IAAAiM,EAAAtG,EAAA3F,KACA,OAAAgM,CACA,CAmBA,SAAAG,EAAAvO,GAEA,SAAAwO,EAAAC,EAAAC,GAEA,GAAA,EAAAhL,gBAAA8K,GACA,OAAA,IAAAA,EAAAC,EAAAC,CAAA,EAKA5G,OAAA6G,eAAAjL,KAAA,UAAA,CAAAkL,IAAA,WAAA,OAAAH,CAAA,CAAA,CAAA,EAGAlL,MAAAsL,kBACAtL,MAAAsL,kBAAAnL,KAAA8K,CAAA,EAEA1G,OAAA6G,eAAAjL,KAAA,QAAA,CAAAmG,MAAAtG,MAAA,EAAAuL,OAAA,EAAA,CAAA,EAEAJ,GACAtD,EAAA1H,KAAAgL,CAAA,CACA,CA2BA,OAzBAF,EAAA5K,UAAAkE,OAAA2B,OAAAlG,MAAAK,UAAA,CACAmH,YAAA,CACAlB,MAAA2E,EACAO,SAAA,CAAA,EACAC,WAAA,CAAA,EACAC,aAAA,CAAA,CACA,EACAjP,KAAA,CACA4O,IAAA,WAAA,OAAA5O,CAAA,EACAkP,IAAAxP,EACAsP,WAAA,CAAA,EAKAC,aAAA,CAAA,CACA,EACApD,SAAA,CACAhC,MAAA,WAAA,OAAAnG,KAAA1D,KAAA,KAAA0D,KAAA+K,OAAA,EACAM,SAAA,CAAA,EACAC,WAAA,CAAA,EACAC,aAAA,CAAA,CACA,CACA,CAAA,EAEAT,CACA,CAhTApO,EAAAuM,UAAA9L,EAAA,CAAA,EAGAT,EAAAwB,OAAAf,EAAA,CAAA,EAGAT,EAAAqD,aAAA5C,EAAA,CAAA,EAGAT,EAAAuK,MAAA9J,EAAA,CAAA,EAGAT,EAAAsH,QAAA7G,EAAA,CAAA,EAGAT,EAAAkI,KAAAzH,EAAA,CAAA,EAGAT,EAAA+O,KAAAtO,EAAA,CAAA,EAGAT,EAAAgJ,SAAAvI,EAAA,EAAA,EAOAT,EAAAgP,OAAAjD,CAAAA,EAAA,aAAA,OAAA9L,QACAA,QACAA,OAAAgP,SACAhP,OAAAgP,QAAAC,UACAjP,OAAAgP,QAAAC,SAAAC,MAOAnP,EAAAC,OAAAD,EAAAgP,QAAA/O,QACA,aAAA,OAAAmP,QAAAA,QACA,aAAA,OAAA9C,MAAAA,MACAhJ,KAQAtD,EAAAqP,WAAA3H,OAAA4H,OAAA5H,OAAA4H,OAAA,EAAA,EAAA,GAOAtP,EAAAuP,YAAA7H,OAAA4H,OAAA5H,OAAA4H,OAAA,EAAA,EAAA,GAQAtP,EAAAwP,UAAAC,OAAAD,WAAA,SAAA/F,GACA,MAAA,UAAA,OAAAA,GAAAiG,SAAAjG,CAAA,GAAA7H,KAAA8C,MAAA+E,CAAA,IAAAA,CACA,EAOAzJ,EAAAkN,SAAA,SAAAzD,GACA,MAAA,UAAA,OAAAA,GAAAA,aAAA9G,MACA,EAOA3C,EAAA2P,SAAA,SAAAlG,GACA,OAAAA,GAAA,UAAA,OAAAA,CACA,EAUAzJ,EAAA4P,MAQA5P,EAAA6P,MAAA,SAAAC,EAAAC,GACA,IAAAtG,EAAAqG,EAAAC,GACA,OAAA,MAAAtG,GAAAqG,EAAAE,eAAAD,CAAA,IACA,UAAA,OAAAtG,GAAA,GAAA5I,MAAA6I,QAAAD,CAAA,EAAAA,EAAA/B,OAAAC,KAAA8B,CAAA,GAAA1I,OAEA,EAaAf,EAAAsJ,OAAA,WACA,IACA,IAAAA,EAAAtJ,EAAAsH,QAAA,QAAA,EAAAgC,OAEA,OAAAA,EAAA9F,UAAAyM,UAAA3G,EAAA,IAIA,CAHA,MAAA1B,GAEA,OAAA,IACA,CACA,EAAA,EAGA5H,EAAAkQ,EAAA,KAGAlQ,EAAAmQ,EAAA,KAOAnQ,EAAAoQ,UAAA,SAAAC,GAEA,MAAA,UAAA,OAAAA,EACArQ,EAAAsJ,OACAtJ,EAAAmQ,EAAAE,CAAA,EACA,IAAArQ,EAAAa,MAAAwP,CAAA,EACArQ,EAAAsJ,OACAtJ,EAAAkQ,EAAAG,CAAA,EACA,aAAA,OAAA9J,WACA8J,EACA,IAAA9J,WAAA8J,CAAA,CACA,EAMArQ,EAAAa,MAAA,aAAA,OAAA0F,WAAAA,WAAA1F,MAeAb,EAAAI,KAAAJ,EAAAC,OAAAqQ,SAAAtQ,EAAAC,OAAAqQ,QAAAlQ,MACAJ,EAAAC,OAAAG,MACAJ,EAAAsH,QAAA,MAAA,EAOAtH,EAAAuQ,OAAA,mBAOAvQ,EAAAwQ,QAAA,wBAOAxQ,EAAAyQ,QAAA,6CAOAzQ,EAAA0Q,WAAA,SAAAjH,GACA,OAAAA,EACAzJ,EAAAgJ,SAAAiE,KAAAxD,CAAA,EAAAkE,OAAA,EACA3N,EAAAgJ,SAAA+D,QACA,EAQA/M,EAAA2Q,aAAA,SAAAjD,EAAAH,GACA3D,EAAA5J,EAAAgJ,SAAAyE,SAAAC,CAAA,EACA,OAAA1N,EAAAI,KACAJ,EAAAI,KAAAwQ,SAAAhH,EAAA5D,GAAA4D,EAAA3D,GAAAsH,CAAA,EACA3D,EAAAiD,SAAAd,CAAAA,CAAAwB,CAAA,CACA,EAiBAvN,EAAAgL,MAAAA,EAOAhL,EAAA6Q,QAAA,SAAAC,GACA,OAAAA,EAAA,IAAAA,IAAAC,YAAA,EAAAD,EAAAE,UAAA,CAAA,CACA,EA0DAhR,EAAAmO,SAAAA,EAmBAnO,EAAAiR,cAAA9C,EAAA,eAAA,EAoBAnO,EAAAkR,YAAA,SAAAC,GAEA,IADA,IAAAC,EAAA,GACApP,EAAA,EAAAA,EAAAmP,EAAApQ,OAAA,EAAAiB,EACAoP,EAAAD,EAAAnP,IAAA,EAOA,OAAA,WACA,IAAA,IAAA2F,EAAAD,OAAAC,KAAArE,IAAA,EAAAtB,EAAA2F,EAAA5G,OAAA,EAAA,CAAA,EAAAiB,EAAA,EAAAA,EACA,GAAA,IAAAoP,EAAAzJ,EAAA3F,KAAAsB,KAAAqE,EAAA3F,MAAA1C,GAAA,OAAAgE,KAAAqE,EAAA3F,IACA,OAAA2F,EAAA3F,EACA,CACA,EAeAhC,EAAAqR,YAAA,SAAAF,GAQA,OAAA,SAAAvR,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAAmP,EAAApQ,OAAA,EAAAiB,EACAmP,EAAAnP,KAAApC,GACA,OAAA0D,KAAA6N,EAAAnP,GACA,CACA,EAkBAhC,EAAAsR,cAAA,CACAC,MAAA5O,OACA6O,MAAA7O,OACA8H,MAAA9H,OACA8O,KAAA,CAAA,CACA,EAGAzR,EAAAwI,EAAA,WACA,IAAAc,EAAAtJ,EAAAsJ,OAEAA,GAMAtJ,EAAAkQ,EAAA5G,EAAA2D,OAAA1G,WAAA0G,MAAA3D,EAAA2D,MAEA,SAAAxD,EAAAiI,GACA,OAAA,IAAApI,EAAAG,EAAAiI,CAAA,CACA,EACA1R,EAAAmQ,EAAA7G,EAAAqI,aAEA,SAAA7J,GACA,OAAA,IAAAwB,EAAAxB,CAAA,CACA,GAdA9H,EAAAkQ,EAAAlQ,EAAAmQ,EAAA,IAeA,C,2DCpbA5P,EAAAR,QAAA0I,EAEA,IAEAC,EAFA1I,EAAAS,EAAA,EAAA,EAIAuI,EAAAhJ,EAAAgJ,SACAxH,EAAAxB,EAAAwB,OACA0G,EAAAlI,EAAAkI,KAWA,SAAA0J,EAAAlR,EAAAyH,EAAAhE,GAMAb,KAAA5C,GAAAA,EAMA4C,KAAA6E,IAAAA,EAMA7E,KAAAuO,KAAAvS,EAMAgE,KAAAa,IAAAA,CACA,CAGA,SAAA2N,KAUA,SAAAC,EAAAC,GAMA1O,KAAA2O,KAAAD,EAAAC,KAMA3O,KAAA4O,KAAAF,EAAAE,KAMA5O,KAAA6E,IAAA6J,EAAA7J,IAMA7E,KAAAuO,KAAAG,EAAAG,MACA,CAOA,SAAA1J,IAMAnF,KAAA6E,IAAA,EAMA7E,KAAA2O,KAAA,IAAAL,EAAAE,EAAA,EAAA,CAAA,EAMAxO,KAAA4O,KAAA5O,KAAA2O,KAMA3O,KAAA6O,OAAA,IAOA,CAEA,SAAA9I,IACA,OAAArJ,EAAAsJ,OACA,WACA,OAAAb,EAAAY,OAAA,WACA,OAAA,IAAAX,CACA,GAAA,CACA,EAEA,WACA,OAAA,IAAAD,CACA,CACA,CAqCA,SAAA2J,EAAAjO,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,CACA,CAmBA,SAAAkO,EAAAlK,EAAAhE,GACAb,KAAA6E,IAAAA,EACA7E,KAAAuO,KAAAvS,EACAgE,KAAAa,IAAAA,CACA,CA6CA,SAAAmO,EAAAnO,EAAAC,EAAAC,GACA,KAAAF,EAAA8B,IACA7B,EAAAC,CAAA,IAAA,IAAAF,EAAA6B,GAAA,IACA7B,EAAA6B,IAAA7B,EAAA6B,KAAA,EAAA7B,EAAA8B,IAAA,MAAA,EACA9B,EAAA8B,MAAA,EAEA,KAAA,IAAA9B,EAAA6B,IACA5B,EAAAC,CAAA,IAAA,IAAAF,EAAA6B,GAAA,IACA7B,EAAA6B,GAAA7B,EAAA6B,KAAA,EAEA5B,EAAAC,CAAA,IAAAF,EAAA6B,EACA,CA0CA,SAAAuM,EAAApO,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CA9JAsE,EAAAY,OAAAA,EAAA,EAOAZ,EAAAZ,MAAA,SAAAC,GACA,OAAA,IAAA9H,EAAAa,MAAAiH,CAAA,CACA,EAIA9H,EAAAa,QAAAA,QACA4H,EAAAZ,MAAA7H,EAAA+O,KAAAtG,EAAAZ,MAAA7H,EAAAa,MAAA2C,UAAAwG,QAAA,GAUAvB,EAAAjF,UAAAgP,EAAA,SAAA9R,EAAAyH,EAAAhE,GAGA,OAFAb,KAAA4O,KAAA5O,KAAA4O,KAAAL,KAAA,IAAAD,EAAAlR,EAAAyH,EAAAhE,CAAA,EACAb,KAAA6E,KAAAA,EACA7E,IACA,GA6BA+O,EAAA7O,UAAAkE,OAAA2B,OAAAuI,EAAApO,SAAA,GACA9C,GAxBA,SAAAyD,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,CAAA,IAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,CACA,EAyBAsE,EAAAjF,UAAAyG,OAAA,SAAAR,GAWA,OARAnG,KAAA6E,MAAA7E,KAAA4O,KAAA5O,KAAA4O,KAAAL,KAAA,IAAAQ,GACA5I,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,CAAA,GAAAtB,IACA7E,IACA,EAQAmF,EAAAjF,UAAA0G,MAAA,SAAAT,GACA,OAAAA,EAAA,EACAnG,KAAAkP,EAAAF,EAAA,GAAAtJ,EAAAgE,WAAAvD,CAAA,CAAA,EACAnG,KAAA2G,OAAAR,CAAA,CACA,EAOAhB,EAAAjF,UAAA2G,OAAA,SAAAV,GACA,OAAAnG,KAAA2G,QAAAR,GAAA,EAAAA,GAAA,MAAA,CAAA,CACA,EAiCAhB,EAAAjF,UAAAyH,MAZAxC,EAAAjF,UAAA0H,OAAA,SAAAzB,GACAG,EAAAZ,EAAAiE,KAAAxD,CAAA,EACA,OAAAnG,KAAAkP,EAAAF,EAAA1I,EAAA7I,OAAA,EAAA6I,CAAA,CACA,EAiBAnB,EAAAjF,UAAA2H,OAAA,SAAA1B,GACAG,EAAAZ,EAAAiE,KAAAxD,CAAA,EAAAqD,SAAA,EACA,OAAAxJ,KAAAkP,EAAAF,EAAA1I,EAAA7I,OAAA,EAAA6I,CAAA,CACA,EAOAnB,EAAAjF,UAAA4G,KAAA,SAAAX,GACA,OAAAnG,KAAAkP,EAAAJ,EAAA,EAAA3I,EAAA,EAAA,CAAA,CACA,EAwBAhB,EAAAjF,UAAA8G,SAVA7B,EAAAjF,UAAA6G,QAAA,SAAAZ,GACA,OAAAnG,KAAAkP,EAAAD,EAAA,EAAA9I,IAAA,CAAA,CACA,EA4BAhB,EAAAjF,UAAA8H,SAZA7C,EAAAjF,UAAA6H,QAAA,SAAA5B,GACAG,EAAAZ,EAAAiE,KAAAxD,CAAA,EACA,OAAAnG,KAAAkP,EAAAD,EAAA,EAAA3I,EAAA5D,EAAA,EAAAwM,EAAAD,EAAA,EAAA3I,EAAA3D,EAAA,CACA,EAiBAwC,EAAAjF,UAAA+G,MAAA,SAAAd,GACA,OAAAnG,KAAAkP,EAAAxS,EAAAuK,MAAA/D,aAAA,EAAAiD,CAAA,CACA,EAQAhB,EAAAjF,UAAAgH,OAAA,SAAAf,GACA,OAAAnG,KAAAkP,EAAAxS,EAAAuK,MAAArD,cAAA,EAAAuC,CAAA,CACA,EAEA,IAAAgJ,EAAAzS,EAAAa,MAAA2C,UAAAsL,IACA,SAAA3K,EAAAC,EAAAC,GACAD,EAAA0K,IAAA3K,EAAAE,CAAA,CACA,EAEA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAArC,EAAA,EAAAA,EAAAmC,EAAApD,OAAA,EAAAiB,EACAoC,EAAAC,EAAArC,GAAAmC,EAAAnC,EACA,EAOAyG,EAAAjF,UAAAiH,MAAA,SAAAhB,GACA,IAIArF,EAJA+D,EAAAsB,EAAA1I,SAAA,EACA,OAAAoH,GAEAnI,EAAAkN,SAAAzD,CAAA,IACArF,EAAAqE,EAAAZ,MAAAM,EAAA3G,EAAAT,OAAA0I,CAAA,CAAA,EACAjI,EAAAwB,OAAAyG,EAAArF,EAAA,CAAA,EACAqF,EAAArF,GAEAd,KAAA2G,OAAA9B,CAAA,EAAAqK,EAAAC,EAAAtK,EAAAsB,CAAA,GANAnG,KAAAkP,EAAAJ,EAAA,EAAA,CAAA,CAOA,EAOA3J,EAAAjF,UAAA/B,OAAA,SAAAgI,GACA,IAAAtB,EAAAD,EAAAnH,OAAA0I,CAAA,EACA,OAAAtB,EACA7E,KAAA2G,OAAA9B,CAAA,EAAAqK,EAAAtK,EAAAG,MAAAF,EAAAsB,CAAA,EACAnG,KAAAkP,EAAAJ,EAAA,EAAA,CAAA,CACA,EAOA3J,EAAAjF,UAAAkP,KAAA,WAIA,OAHApP,KAAA6O,OAAA,IAAAJ,EAAAzO,IAAA,EACAA,KAAA2O,KAAA3O,KAAA4O,KAAA,IAAAN,EAAAE,EAAA,EAAA,CAAA,EACAxO,KAAA6E,IAAA,EACA7E,IACA,EAMAmF,EAAAjF,UAAAmP,MAAA,WAUA,OATArP,KAAA6O,QACA7O,KAAA2O,KAAA3O,KAAA6O,OAAAF,KACA3O,KAAA4O,KAAA5O,KAAA6O,OAAAD,KACA5O,KAAA6E,IAAA7E,KAAA6O,OAAAhK,IACA7E,KAAA6O,OAAA7O,KAAA6O,OAAAN,OAEAvO,KAAA2O,KAAA3O,KAAA4O,KAAA,IAAAN,EAAAE,EAAA,EAAA,CAAA,EACAxO,KAAA6E,IAAA,GAEA7E,IACA,EAMAmF,EAAAjF,UAAAoP,OAAA,WACA,IAAAX,EAAA3O,KAAA2O,KACAC,EAAA5O,KAAA4O,KACA/J,EAAA7E,KAAA6E,IAOA,OANA7E,KAAAqP,MAAA,EAAA1I,OAAA9B,CAAA,EACAA,IACA7E,KAAA4O,KAAAL,KAAAI,EAAAJ,KACAvO,KAAA4O,KAAAA,EACA5O,KAAA6E,KAAAA,GAEA7E,IACA,EAMAmF,EAAAjF,UAAAiJ,OAAA,WAIA,IAHA,IAAAwF,EAAA3O,KAAA2O,KAAAJ,KACAzN,EAAAd,KAAAqH,YAAA9C,MAAAvE,KAAA6E,GAAA,EACA9D,EAAA,EACA4N,GACAA,EAAAvR,GAAAuR,EAAA9N,IAAAC,EAAAC,CAAA,EACAA,GAAA4N,EAAA9J,IACA8J,EAAAA,EAAAJ,KAGA,OAAAzN,CACA,EAEAqE,EAAAD,EAAA,SAAAqK,GACAnK,EAAAmK,EACApK,EAAAY,OAAAA,EAAA,EACAX,EAAAF,EAAA,CACA,C,+BC/cAjI,EAAAR,QAAA2I,EAGA,IAAAD,EAAAhI,EAAA,EAAA,EAGAT,IAFA0I,EAAAlF,UAAAkE,OAAA2B,OAAAZ,EAAAjF,SAAA,GAAAmH,YAAAjC,EAEAjI,EAAA,EAAA,GAQA,SAAAiI,IACAD,EAAA3I,KAAAwD,IAAA,CACA,CAuCA,SAAAwP,EAAA3O,EAAAC,EAAAC,GACAF,EAAApD,OAAA,GACAf,EAAAkI,KAAAG,MAAAlE,EAAAC,EAAAC,CAAA,EACAD,EAAA6L,UACA7L,EAAA6L,UAAA9L,EAAAE,CAAA,EAEAD,EAAAiE,MAAAlE,EAAAE,CAAA,CACA,CA5CAqE,EAAAF,EAAA,WAOAE,EAAAb,MAAA7H,EAAAmQ,EAEAzH,EAAAqK,iBAAA/S,EAAAsJ,QAAAtJ,EAAAsJ,OAAA9F,qBAAA+C,YAAA,QAAAvG,EAAAsJ,OAAA9F,UAAAsL,IAAAlP,KACA,SAAAuE,EAAAC,EAAAC,GACAD,EAAA0K,IAAA3K,EAAAE,CAAA,CAEA,EAEA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAA6O,KACA7O,EAAA6O,KAAA5O,EAAAC,EAAA,EAAAF,EAAApD,MAAA,OACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAmC,EAAApD,QACAqD,EAAAC,CAAA,IAAAF,EAAAnC,CAAA,GACA,CACA,EAMA0G,EAAAlF,UAAAiH,MAAA,SAAAhB,GAGA,IAAAtB,GADAsB,EADAzJ,EAAAkN,SAAAzD,CAAA,EACAzJ,EAAAkQ,EAAAzG,EAAA,QAAA,EACAA,GAAA1I,SAAA,EAIA,OAHAuC,KAAA2G,OAAA9B,CAAA,EACAA,GACA7E,KAAAkP,EAAA9J,EAAAqK,iBAAA5K,EAAAsB,CAAA,EACAnG,IACA,EAcAoF,EAAAlF,UAAA/B,OAAA,SAAAgI,GACA,IAAAtB,EAAAnI,EAAAsJ,OAAA2J,WAAAxJ,CAAA,EAIA,OAHAnG,KAAA2G,OAAA9B,CAAA,EACAA,GACA7E,KAAAkP,EAAAM,EAAA3K,EAAAsB,CAAA,EACAnG,IACA,EAUAoF,EAAAF,EAAA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(16);\nprotobuf.BufferWriter = require(17);\nprotobuf.Reader       = require(9);\nprotobuf.BufferReader = require(10);\n\n// Utility\nprotobuf.util         = require(15);\nprotobuf.rpc          = require(12);\nprotobuf.roots        = require(11);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(15);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n\n    if (start === end) { // fix for IE 10/Win8 and others' subarray returning array of size 1\n        var nativeBuffer = util.Buffer;\n        return nativeBuffer\n            ? nativeBuffer.alloc(0)\n            : new this.buf.constructor(0);\n    }\n    return this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(9);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(15);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(13);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(15);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(15);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(3);\n\n// float handling accross browsers\nutil.float = require(4);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(5);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(7);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(6);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(14);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get: function get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value: function value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(15);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(16);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(15);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}