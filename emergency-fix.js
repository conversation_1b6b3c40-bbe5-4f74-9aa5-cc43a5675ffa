const fs = require('fs');

console.log('开始紧急修复知识库数据...');

// 读取原始文件
let fileContent;
try {
    fileContent = fs.readFileSync('knowledge-data-clean.js', 'utf8');
    console.log('成功读取原始数据文件');
} catch (error) {
    console.error('无法读取文件:', error);
    process.exit(1);
}

// 查找所有批次
const batchRegex = /const KNOWLEDGE_BATCH_(\d+) = \[([\s\S]*?)\];/g;
const batches = [];
let match;

while ((match = batchRegex.exec(fileContent)) !== null) {
    batches.push({
        number: parseInt(match[1]),
        content: match[2],
        fullMatch: match[0],
        startIndex: match.index,
        endIndex: match.index + match[0].length
    });
}

console.log(`找到 ${batches.length} 个批次`);

// 修复统计
const stats = {
    total: 0,
    fixed: 0,
    corrupted: 0,
    replaced: 0
};

const fixedBatches = {};

// 处理每个批次
for (const batch of batches) {
    console.log(`处理第 ${batch.number} 批...`);
    
    const items = [];
    let currentItem = '';
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;
    
    // 解析批次中的项目
    for (let i = 0; i < batch.content.length; i++) {
        const char = batch.content[i];
        
        if (escapeNext) {
            currentItem += char;
            escapeNext = false;
            continue;
        }
        
        if (char === '\\') {
            currentItem += char;
            escapeNext = true;
            continue;
        }
        
        if (char === '"' && !escapeNext) {
            inString = !inString;
            currentItem += char;
            continue;
        }
        
        if (!inString) {
            if (char === '{') {
                braceCount++;
            } else if (char === '}') {
                braceCount--;
            }
            
            if (char === ',' && braceCount === 0) {
                const trimmed = currentItem.trim();
                if (trimmed) {
                    items.push(trimmed);
                }
                currentItem = '';
                continue;
            }
        }
        
        currentItem += char;
    }
    
    // 添加最后一个项目
    if (currentItem.trim()) {
        items.push(currentItem.trim());
    }
    
    console.log(`第 ${batch.number} 批解析出 ${items.length} 个项目`);
    stats.total += items.length;
    
    // 修复每个项目
    const fixedItems = [];
    
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        
        try {
            // 尝试解析JSON
            const parsed = JSON.parse(item);
            
            // 检查content字段是否损坏
            if (parsed.content && typeof parsed.content === 'string') {
                const originalLength = parsed.content.length;
                
                // 检查是否包含异常字符或过长内容
                const hasControlChars = /[\u0000-\u001F\u007F-\u009F]/.test(parsed.content);
                const hasUnescapedQuotes = /(?<!\\)"/.test(parsed.content.slice(1, -1)); // 检查未转义的引号
                const tooLong = parsed.content.length > 10000; // 内容过长
                const hasJavaScriptCode = /packet count|transfer size|DocID|STMicroelectronics/.test(parsed.content);
                
                if (hasControlChars || hasUnescapedQuotes || tooLong || hasJavaScriptCode) {
                    console.log(`  项目 ${i + 1}: content字段损坏，生成替代内容`);
                    
                    // 生成安全的替代内容
                    parsed.content = `${parsed.title || '文档'}的相关内容。作者：${parsed.author || '未知'}。这是一部关于${parsed.category || '易学'}的重要文献。`;
                    
                    stats.corrupted++;
                    stats.fixed++;
                }
            }
            
            // 修复其他字段
            if (!parsed.author || parsed.author.trim() === '') {
                if (parsed.filePath) {
                    const match = parsed.filePath.match(/^([^-]+)-/);
                    if (match) {
                        parsed.author = match[1];
                    } else {
                        parsed.author = "未知作者";
                    }
                } else {
                    parsed.author = "未知作者";
                }
                stats.fixed++;
            }
            
            // 检查title和author是否颠倒
            if (parsed.title && parsed.author && parsed.filePath) {
                const expectedPattern = `${parsed.author}-${parsed.title}`;
                if (!parsed.filePath.includes(expectedPattern)) {
                    const swappedPattern = `${parsed.title}-${parsed.author}`;
                    if (parsed.filePath.includes(swappedPattern)) {
                        const temp = parsed.title;
                        parsed.title = parsed.author;
                        parsed.author = temp;
                        console.log(`  项目 ${i + 1}: 交换title和author`);
                        stats.fixed++;
                    }
                }
            }
            
            // 确保所有必需字段存在
            if (!parsed.filePath) parsed.filePath = `unknown-${batch.number}-${i + 1}.txt`;
            if (!parsed.title) parsed.title = `文档${batch.number}-${i + 1}`;
            if (!parsed.dynasty) parsed.dynasty = "";
            if (!parsed.category) parsed.category = "易学";
            if (!parsed.keywords) parsed.keywords = ["易学"];
            if (!parsed.content) parsed.content = "内容待补充";
            
            fixedItems.push(JSON.stringify(parsed, null, 2));
            
        } catch (error) {
            console.log(`  项目 ${i + 1}: JSON解析失败，创建替代项目`);
            
            // 创建完全替代的项目
            const fallbackItem = {
                filePath: `error-batch${batch.number}-item${i + 1}.txt`,
                title: `错误项目${batch.number}-${i + 1}`,
                author: "数据错误",
                dynasty: "",
                category: "易学",
                keywords: ["易学"],
                content: "此项目数据损坏，已生成替代内容。"
            };
            
            fixedItems.push(JSON.stringify(fallbackItem, null, 2));
            stats.replaced++;
            stats.fixed++;
        }
    }
    
    fixedBatches[batch.number] = fixedItems;
}

// 重建文件
let newFileContent = fileContent;

// 按批次替换（从后往前替换避免索引问题）
for (const batch of batches.sort((a, b) => b.startIndex - a.startIndex)) {
    if (fixedBatches[batch.number]) {
        const newBatchContent = `const KNOWLEDGE_BATCH_${batch.number} = [\n  ${fixedBatches[batch.number].join(',\n  ')}\n];`;
        newFileContent = newFileContent.substring(0, batch.startIndex) + 
                        newBatchContent + 
                        newFileContent.substring(batch.endIndex);
    }
}

// 保存修复后的文件
try {
    fs.writeFileSync('knowledge-data-emergency-fixed.js', newFileContent, 'utf8');
    console.log('\n修复后的数据已保存到 knowledge-data-emergency-fixed.js');
    
    // 验证修复后的文件
    console.log('验证修复后的文件语法...');
    require('./knowledge-data-emergency-fixed.js');
    console.log('✓ 验证成功！文件语法正确。');
    
} catch (error) {
    console.error('保存或验证文件时出错:', error);
    process.exit(1);
}

// 输出修复统计
console.log('\n=== 紧急修复统计 ===');
console.log(`总项目数: ${stats.total}`);
console.log(`修复项目数: ${stats.fixed}`);
console.log(`损坏content字段: ${stats.corrupted}`);
console.log(`完全替换项目: ${stats.replaced}`);

console.log('\n紧急修复完成！现在可以安全使用修复后的文件。');
