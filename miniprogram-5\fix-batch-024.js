const fs = require('fs');
const path = require('path');

// 读取原始batch-024.json文件
const originalBatch = JSON.parse(fs.readFileSync('./upload-batches/batch-024.json', 'utf8'));

console.log('原始文件信息:');
console.log('- 文件数量:', originalBatch.data.length);
console.log('- 文件大小:', Math.round(fs.statSync('./upload-batches/batch-024.json').size / 1024), 'KB');

// 将10个文件拆分成两个批次
const batch024a = {
  batchNumber: '024a',
  totalBatches: 45, // 原来44个批次，现在变成45个
  fileCount: 5,
  totalSize: 0,
  sizeInMB: 0,
  files: originalBatch.files.slice(0, 5),
  data: originalBatch.data.slice(0, 5),
  createdAt: new Date().toISOString()
};

const batch024b = {
  batchNumber: '024b', 
  totalBatches: 45,
  fileCount: 5,
  totalSize: 0,
  sizeInMB: 0,
  files: originalBatch.files.slice(5, 10),
  data: originalBatch.data.slice(5, 10),
  createdAt: new Date().toISOString()
};

// 计算文件大小
batch024a.totalSize = batch024a.data.reduce((sum, item) => sum + item.file_size, 0);
batch024a.sizeInMB = Math.round(batch024a.totalSize / (1024 * 1024) * 100) / 100;

batch024b.totalSize = batch024b.data.reduce((sum, item) => sum + item.file_size, 0);
batch024b.sizeInMB = Math.round(batch024b.totalSize / (1024 * 1024) * 100) / 100;

// 保存新的批次文件
fs.writeFileSync('./upload-batches/batch-024a.json', JSON.stringify(batch024a, null, 2));
fs.writeFileSync('./upload-batches/batch-024b.json', JSON.stringify(batch024b, null, 2));

console.log('\n拆分完成:');
console.log('batch-024a.json:');
console.log('- 文件数量:', batch024a.fileCount);
console.log('- 内容大小:', batch024a.sizeInMB, 'MB');
console.log('- JSON文件大小:', Math.round(fs.statSync('./upload-batches/batch-024a.json').size / 1024), 'KB');

console.log('\nbatch-024b.json:');
console.log('- 文件数量:', batch024b.fileCount);
console.log('- 内容大小:', batch024b.sizeInMB, 'MB');
console.log('- JSON文件大小:', Math.round(fs.statSync('./upload-batches/batch-024b.json').size / 1024), 'KB');

// 验证JSON有效性
try {
  JSON.parse(fs.readFileSync('./upload-batches/batch-024a.json', 'utf8'));
  JSON.parse(fs.readFileSync('./upload-batches/batch-024b.json', 'utf8'));
  console.log('\n✅ 所有新文件JSON格式验证通过');
} catch (error) {
  console.error('❌ JSON验证失败:', error.message);
}

// 备份原文件
fs.renameSync('./upload-batches/batch-024.json', './upload-batches/batch-024-backup.json');
console.log('\n📁 原文件已备份为 batch-024-backup.json');
console.log('\n🎯 修复完成！现在可以分别导入 batch-024a.json 和 batch-024b.json');
