{"name": "@protobufjs/aspromise", "description": "Returns a promise from a node-style callback function.", "version": "1.1.2", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js"}}