{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "inquire", "moduleName", "mod", "eval", "e", "isAbsolute", "path", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "common", "commonRe", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "timeType", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "Enum", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "defaultAlreadyEmitted", "resolvedType", "typeDefault", "repeated", "fullName", "isUnsigned", "genValuePartial_toObject", "converter", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "types", "defaults", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "Namespace", "create", "constructor", "className", "comment", "comments", "valuesOptions", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "Boolean", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "proto3_optional", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "setParsedOption", "propName", "opt", "newOpt", "find", "hasOwnProperty", "newValue", "setProperty", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "whichImports", "preferTrailingComment", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "parseNumber", "substring", "parseInt", "parseFloat", "readRanges", "target", "acceptStrings", "parseId", "dummy", "ifBlock", "parseOption", "parseInlineOptions", "acceptNegative", "parse<PERSON><PERSON><PERSON>", "parseType", "parseEnum", "parseService", "service", "parseMethod", "commentText", "method", "parseExtension", "reference", "parseField", "fnIf", "fnElse", "trailingLine", "parseMapField", "valueType", "extensions", "parseGroup", "lcFirst", "ucFirst", "endsWith", "startsWith", "parseEnumValue", "isCustom", "option", "optionValue", "parseOptionValue", "objectResult", "lastValue", "prevValue", "concat", "simpleValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "<PERSON><PERSON><PERSON><PERSON>", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "sisterField", "extendedType", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "lastCommentLine", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "char<PERSON>t", "setComment", "isLeading", "lineEmpty", "leading", "lookback", "commentOffset", "lines", "trim", "text", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "match", "lastIndex", "exec", "repeat", "curr", "isDoc", "isLeadingComment", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "originalThis", "wrapper", "fork", "l<PERSON>im", "typeName", "bake", "o", "safePropBackslashRe", "key", "safePropQuoteRe", "camelCaseRe", "toUpperCase", "decorateEnumIndex", "a", "decorateRoot", "enumerable", "dst", "setProp", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "writable", "configurable", "pool", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyValue", "messageName", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;AAAA,CAAA,SAAAA,IAAA,aAAA,CAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,EAAA,EAAAJ,EAAAE,EAAAA,EAAAE,OAAA,EACAF,EAAAE,OACA,EAEAN,EAAA,EAAA,EAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,YAAA,OAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,UAAA,GAEAZ,CACA,CAAA,EAGA,UAAA,OAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,EAEA,EAAA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,CAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,CAAA,IAAAF,UAAAG,CAAA,IACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,EAAA,CAAA,EACAI,EACAD,EAAAC,CAAA,MACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,CAAA,IAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,CAAA,CACA,CAEA,EACA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,CAAA,CAMA,CALA,MAAAU,GACAJ,IACAA,EAAA,CAAA,EACAG,EAAAC,CAAA,EAEA,CACA,CAAA,CACA,C,yBCrCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,GAAA,CAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,EAAA,EAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,KACA,EAAAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,MAAA,EAAA,EAAAY,CACA,EASA,IAxBA,IAkBAG,EAAAjB,MAAA,EAAA,EAGAkB,EAAAlB,MAAA,GAAA,EAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,CAAA,GASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,CAAA,IACA,OAAAK,GACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,CAAA,IAAAF,EAAA,GAAAW,GACAD,EAAA,CAEA,CACA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,EAEA,CAOA,OANAQ,IACAD,EAAAP,CAAA,IAAAF,EAAAO,GACAE,EAAAP,CAAA,IAAA,GACA,IAAAQ,IACAD,EAAAP,CAAA,IAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EAEA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,CAAA,EAAA,EACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,GACA,MAAA6D,MAAAJ,CAAA,EACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,IAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,CAEA,CACA,CACA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,CAAA,EACA,OAAA/B,EAAAmB,CACA,EAOAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,CAAA,CACA,C,yBChIA,SAAA4B,EAAAC,EAAAC,GAGA,UAAA,OAAAD,IACAC,EAAAD,EACAA,EAAAhE,IAGA,IAAAkE,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,UAAA,OAAAA,EAAA,CACA,IAAAC,EAAAC,EAAA,EAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,CAAA,EACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,CAAA,EACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,CAAA,EACAqD,EAAAvD,MAAAmD,EAAAjD,MAAA,EACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,CAAA,KAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,CAAA,EAAA5C,MAAA,KAAA6C,CAAA,CACA,CACA,OAAAE,SAAAX,CAAA,EAAA,CACA,CAKA,IAFA,IAAAY,EAAA1D,MAAAC,UAAAC,OAAA,CAAA,EACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,UAAA,EAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,CAAA,IACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,IAAAkC,EAAAA,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,CAAA,EACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,CAAA,EACA,IAAA,IAAA,MAAAjC,GAAAiC,CACA,CACA,MAAA,GACA,CAAA,EACAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,0BAAA,EAEA,OADAK,EAAAd,KAAAgB,CAAA,EACAD,CACA,CAEA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,GAAA,GAAA,IAAA,SAAAU,EAAAV,KAAA,MAAA,EAAA,KACA,CAGA,OADAW,EAAAG,SAAAA,EACAH,CACA,EAjFAlD,EAAAR,QAAAsD,GAiGAQ,QAAA,CAAA,C,yBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,EACA,EAhBA7E,EAAAR,QAAAmF,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,IACA,CAAA,EACAA,IACA,EAQAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAjG,GACA6F,KAAAC,EAAA,QAEA,GAAA1E,IAAApB,GACA6F,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EAGA,OAAAmD,IACA,EAQAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,CAAA,GAAA,EACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,CAAA,IAAArB,IAAAiF,CAAA,CACA,CACA,OAAAT,IACA,C,yBC1EA5E,EAAAR,QAAA8F,EAEA,IAAAC,EAAArF,EAAA,CAAA,EAGAsF,EAFAtF,EAAA,CAAA,EAEA,IAAA,EA2BA,SAAAoF,EAAAG,EAAAC,EAAAC,GAOA,OAJAD,EAFA,YAAA,OAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,EAIA,CAAAD,EAAAE,KAAAJ,GAAAA,EAAAK,SACAL,EAAAK,SAAAJ,EAAA,SAAA1E,EAAA+E,GACA,OAAA/E,GAAA,aAAA,OAAAgF,eACAT,EAAAM,IAAAH,EAAAC,EAAAC,CAAA,EACA5E,EACA4E,EAAA5E,CAAA,EACA4E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,MAAA,CAAA,CACA,CAAA,EAGAiC,EAAAM,IAAAH,EAAAC,EAAAC,CAAA,EAbAJ,EAAAD,EAAAV,KAAAa,EAAAC,CAAA,CAcA,CAuBAJ,EAAAM,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAnH,GAKA,GAAA,IAAA6G,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,MAAA,CAAA,EAIA,GAAAT,EAAAM,OAAA,CAEA,GAAA,EAAArE,EADAiE,EAAAQ,UAGA,IAAA,IADAzE,EAAA,GACAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA7F,OAAA,EAAAiB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,CAAA,CAAA,EAEA,OAAAkE,EAAA,KAAA,aAAA,OAAAW,WAAA,IAAAA,WAAA3E,CAAA,EAAAA,CAAA,CACA,CACA,OAAAgE,EAAA,KAAAC,EAAAS,YAAA,CACA,EAEAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,oCAAA,EACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,CAAA,EACAG,EAAAc,KAAA,CACA,C,gCC3BA,SAAAC,EAAAnH,GAsDA,SAAAoH,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,EACA,CAAAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,CAAA,EACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA5F,KAAA8F,MAAAL,EAAA,oBAAA,KAAA,GAIAG,GAAA,GAAA,KAFAG,EAAA/F,KAAAkD,MAAAlD,KAAAmC,IAAAsD,CAAA,EAAAzF,KAAAgG,GAAA,IAEA,GADA,QAAAhG,KAAA8F,MAAAL,EAAAzF,KAAAiG,IAAA,EAAA,CAAAF,CAAA,EAAA,OAAA,KACA,EAVAL,EAAAC,CAAA,CAYA,CAKA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,CAAA,EACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA5F,KAAAiG,IAAA,EAAAF,EAAA,GAAA,GAAA,QAAAM,EACA,CA/EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAEA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAOA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,EACA,CAEA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,EACA,CAzCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,EACA,CAAAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,CAAA,GACArB,MAAAJ,CAAA,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,EAAA,WAAAE,EAAAC,EAAAuB,CAAA,GACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,CAAA,GAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,CAAA,IAMA1B,EAAA,kBADAa,EAAAZ,EAAAzF,KAAAiG,IAAA,EAAA,EADAF,EADA,QADAA,EAAA/F,KAAAkD,MAAAlD,KAAAmC,IAAAsD,CAAA,EAAAzF,KAAAgG,GAAA,GAEA,KACAD,EAAA,KACA,EAAAL,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,CAAA,EAGA,CAKA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,CAAA,EACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,CAAA,EACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA5F,KAAAiG,IAAA,EAAAF,EAAA,IAAA,GAAAM,EAAA,iBACA,CA3GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAEA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAOA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,EACA,CAEA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,EACA,CA+DA,MArNA,aAAA,OAAAY,cAEAjB,EAAA,IAAAiB,aAAA,CAAA,CAAA,EAAA,EACAhB,EAAA,IAAAzB,WAAAwB,EAAAnG,MAAA,EACAyG,EAAA,MAAAL,EAAA,GAmBAvI,EAAAwJ,aAAAZ,EAAAP,EAAAG,EAEAxI,EAAAyJ,aAAAb,EAAAJ,EAAAH,EAmBArI,EAAA0J,YAAAd,EAAAH,EAAAC,EAEA1I,EAAA2J,YAAAf,EAAAF,EAAAD,IAwBAzI,EAAAwJ,aAAApC,EAAAwC,KAAA,KAAAC,CAAA,EACA7J,EAAAyJ,aAAArC,EAAAwC,KAAA,KAAAE,CAAA,EAgBA9J,EAAA0J,YAAA3B,EAAA6B,KAAA,KAAAG,CAAA,EACA/J,EAAA2J,YAAA5B,EAAA6B,KAAA,KAAAI,CAAA,GAKA,aAAA,OAAAC,cAEAtB,EAAA,IAAAsB,aAAA,CAAA,CAAA,EAAA,EACA1B,EAAA,IAAAzB,WAAA6B,EAAAxG,MAAA,EACAyG,EAAA,MAAAL,EAAA,GA2BAvI,EAAAkK,cAAAtB,EAAAO,EAAAC,EAEApJ,EAAAmK,cAAAvB,EAAAQ,EAAAD,EA2BAnJ,EAAAoK,aAAAxB,EAAAS,EAAAC,EAEAtJ,EAAAqK,aAAAzB,EAAAU,EAAAD,IAmCArJ,EAAAkK,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,CAAA,EACA7J,EAAAmK,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,CAAA,EAiBA9J,EAAAoK,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,CAAA,EACA/J,EAAAqK,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,CAAA,GAIAhK,CACA,CAIA,SAAA6J,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CAEA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,CACA,CAEA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,CACA,CAEA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,CACA,CA5UAhH,EAAAR,QAAAmH,EAAAA,CAAA,C,yBCOA,SAAAmD,EAAAC,GACA,IACA,IAAAC,EAAAC,KAAA,SAAA,EAAAF,CAAA,EACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,CAAA,EAAAxJ,QACA,OAAAwJ,CACA,CAAA,MAAAE,IACA,OAAA,IACA,CAfAlK,EAAAR,QAAAsK,C,yBCMA,IAEAK,EAMAC,EAAAD,WAAA,SAAAC,GACA,MAAA,eAAAvH,KAAAuH,CAAA,CACA,EAEAC,EAMAD,EAAAC,UAAA,SAAAD,GAGA,IAAArI,GAFAqI,EAAAA,EAAAlG,QAAA,MAAA,GAAA,EACAA,QAAA,UAAA,GAAA,GACAoG,MAAA,GAAA,EACAC,EAAAJ,EAAAC,CAAA,EACAI,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,MAAA,EAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,OAAA,EAAA1D,EAAA,CAAA,EACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,GAAA,CACA,EASA6H,EAAAvJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,CAAA,GACAR,CAAAA,EAAAQ,CAAA,IAIAD,GADAA,EADAE,EAEAF,EADAL,EAAAK,CAAA,GACAxG,QAAA,iBAAA,EAAA,GAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,CAAA,EAHAA,CAIA,C,yBC/DA3K,EAAAR,QA6BA,SAAAqL,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,CAAA,EACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,CAAA,EACAtK,EAAA,GAEAsG,EAAAzE,EAAA/C,KAAA0L,EAAAxK,EAAAA,GAAAqK,CAAA,EAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAsG,CACA,CACA,C,0BCjCAmE,EAAA1K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAyI,EAAA,EAEA1J,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,CAAA,GACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,CAAA,IACA,EAAAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,CACA,EASAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,CAAA,KACA,IACAI,EAAAP,CAAA,IAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,CAAA,IACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,IAAA,GAAAD,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,KAAA,MACAI,EAAAP,CAAA,IAAA,OAAAK,GAAA,IACAE,EAAAP,CAAA,IAAA,OAAA,KAAAK,IAEAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,IACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EASAyJ,EAAAG,MAAA,SAAAnK,EAAAS,EAAAlB,GAIA,IAHA,IACA6K,EACAC,EAFA3J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACA6J,EAAApK,EAAAyB,WAAAlB,CAAA,GACA,IACAE,EAAAlB,CAAA,IAAA6K,GACAA,EAAA,KACA3J,EAAAlB,CAAA,IAAA6K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAArK,EAAAyB,WAAAlB,EAAA,CAAA,KAEA,EAAAA,EACAE,EAAAlB,CAAA,KAFA6K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA5J,EAAAlB,CAAA,IAAA6K,GAAA,GAAA,GAAA,KAIA3J,EAAAlB,CAAA,IAAA6K,GAAA,GAAA,IAHA3J,EAAAlB,CAAA,IAAA6K,GAAA,EAAA,GAAA,KANA3J,EAAAlB,CAAA,IAAA,GAAA6K,EAAA,KAcA,OAAA7K,EAAAmB,CACA,C,0BCvGA5B,EAAAR,QAAAgM,EAEA,IAAAC,EAAA,QAsBA,SAAAD,EAAAnM,EAAAqM,GACAD,EAAA5I,KAAAxD,CAAA,IACAA,EAAA,mBAAAA,EAAA,SACAqM,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAxM,SAAA,CAAAwM,OAAAD,CAAA,CAAA,CAAA,CAAA,CAAA,GAEAF,EAAAnM,GAAAqM,CACA,CAWAF,EAAA,MAAA,CAUAK,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,CACA,EACA5H,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAIAT,EAAA,WAAA,CAUAU,SAAAC,EAAA,CACAL,OAAA,CACAM,QAAA,CACAJ,KAAA,QACAC,GAAA,CACA,EACAI,MAAA,CACAL,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,YAAA,CAUAc,UAAAH,CACA,CAAA,EAEAX,EAAA,QAAA,CAOAe,MAAA,CACAT,OAAA,EACA,CACA,CAAA,EAEAN,EAAA,SAAA,CASAgB,OAAA,CACAV,OAAA,CACAA,OAAA,CACAW,QAAA,SACAT,KAAA,QACAC,GAAA,CACA,CACA,CACA,EAeAS,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,YAEA,CACA,EACAf,OAAA,CACAgB,UAAA,CACAd,KAAA,YACAC,GAAA,CACA,EACAc,YAAA,CACAf,KAAA,SACAC,GAAA,CACA,EACAe,YAAA,CACAhB,KAAA,SACAC,GAAA,CACA,EACAgB,UAAA,CACAjB,KAAA,OACAC,GAAA,CACA,EACAiB,YAAA,CACAlB,KAAA,SACAC,GAAA,CACA,EACAkB,UAAA,CACAnB,KAAA,YACAC,GAAA,CACA,CACA,CACA,EAEAmB,UAAA,CACAC,OAAA,CACAC,WAAA,CACA,CACA,EASAC,UAAA,CACAzB,OAAA,CACAuB,OAAA,CACAG,KAAA,WACAxB,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,WAAA,CASAiC,YAAA,CACA3B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASAyB,WAAA,CACA5B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA0B,WAAA,CACA7B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA2B,YAAA,CACA9B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASA4B,WAAA,CACA/B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA6B,YAAA,CACAhC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASA8B,UAAA,CACAjC,OAAA,CACAzH,MAAA,CACA2H,KAAA,OACAC,GAAA,CACA,CACA,CACA,EASA+B,YAAA,CACAlC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASAgC,WAAA,CACAnC,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,aAAA,CASA0C,UAAA,CACApC,OAAA,CACAqC,MAAA,CACAX,KAAA,WACAxB,KAAA,SACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAiBAT,EAAA4C,IAAA,SAAAC,GACA,OAAA7C,EAAA6C,IAAA,IACA,C,0BCzYA,IAEAC,EAAApO,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAWA,SAAAqO,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAA,CAAA,EAEA,GAAAH,EAAAI,aACA,GAAAJ,EAAAI,wBAAAP,EAAA,CAAAE,EACA,eAAAG,CAAA,EACA,IAAA,IAAAtB,EAAAoB,EAAAI,aAAAxB,OAAA1J,EAAAD,OAAAC,KAAA0J,CAAA,EAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EAEA4L,EAAA1J,EAAAlC,MAAAgN,EAAAK,aAAAF,IAAAJ,EACA,UAAA,EACA,4CAAAG,EAAAA,EAAAA,CAAA,EACAF,EAAAM,UAAAP,EAEA,OAAA,EACAI,EAAA,CAAA,GAEAJ,EACA,UAAA7K,EAAAlC,EAAA,EACA,WAAA4L,EAAA1J,EAAAlC,GAAA,EACA,SAAAkN,EAAAtB,EAAA1J,EAAAlC,GAAA,EACA,OAAA,EACA+M,EACA,GAAA,CACA,MAAAA,EACA,4BAAAG,CAAA,EACA,sBAAAF,EAAAO,SAAA,mBAAA,EACA,gCAAAL,EAAAD,EAAAC,CAAA,MACA,CACA,IAAAM,EAAA,CAAA,EACA,OAAAR,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,kBAAAG,EAAAA,CAAA,EACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,CAAA,EACA,MACA,IAAA,SACAM,EAAA,CAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,eAAA,EACA,6CAAAG,EAAAA,EAAAM,CAAA,EACA,iCAAAN,CAAA,EACA,uBAAAA,EAAAA,CAAA,EACA,iCAAAA,CAAA,EACA,UAAAA,EAAAA,CAAA,EACA,iCAAAA,CAAA,EACA,+DAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,EAAA,EACA,MACA,IAAA,QAAAT,EACA,4BAAAG,CAAA,EACA,wEAAAA,EAAAA,EAAAA,CAAA,EACA,2BAAAA,CAAA,EACA,UAAAA,EAAAA,CAAA,EACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,CAAA,EACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,CAAA,CAKA,CACA,CACA,OAAAH,CAEA,CAiEA,SAAAU,EAAAV,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAI,aACAJ,EAAAI,wBAAAP,EAAAE,EACA,yFAAAG,EAAAD,EAAAC,EAAAA,EAAAD,EAAAC,EAAAA,CAAA,EACAH,EACA,gCAAAG,EAAAD,EAAAC,CAAA,MACA,CACA,IAAAM,EAAA,CAAA,EACA,OAAAR,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,CAAA,EACA,MACA,IAAA,SACAM,EAAA,CAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,4BAAAG,CAAA,EACA,uCAAAA,EAAAA,EAAAA,CAAA,EACA,MAAA,EACA,4IAAAA,EAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,GAAAN,CAAA,EACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,CAAA,EACA,MACA,QAAAH,EACA,UAAAG,EAAAA,CAAA,CAEA,CACA,CACA,OAAAH,CAEA,CA9FAW,EAAAC,WAAA,SAAAC,GAEA,IAAAvD,EAAAuD,EAAAC,YACAd,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,KAAA,aAAA,EACA,4BAAA,EACA,UAAA,EACA,GAAA,CAAAyM,EAAAtL,OAAA,OAAAgO,EACA,sBAAA,EACAA,EACA,qBAAA,EACA,IAAA,IAAA/M,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,QAAA,EACA8N,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EAGAoP,EAAAe,KAAAhB,EACA,WAAAG,CAAA,EACA,4BAAAA,CAAA,EACA,sBAAAF,EAAAO,SAAA,mBAAA,EACA,SAAAL,CAAA,EACA,oDAAAA,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,SAAA,EACA,GAAA,EACA,GAAA,GAGAF,EAAAM,UAAAP,EACA,WAAAG,CAAA,EACA,0BAAAA,CAAA,EACA,sBAAAF,EAAAO,SAAA,kBAAA,EACA,SAAAL,CAAA,EACA,iCAAAA,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,KAAA,EACA,GAAA,EACA,GAAA,IAIAF,EAAAI,wBAAAP,GAAAE,EACA,iBAAAG,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,CAAA,EACAF,EAAAI,wBAAAP,GAAAE,EACA,GAAA,EAEA,CAAA,OAAAA,EACA,UAAA,CAEA,EAsDAW,EAAAM,SAAA,SAAAJ,GAEA,IAAAvD,EAAAuD,EAAAC,YAAAhN,MAAA,EAAAoN,KAAAjQ,EAAAkQ,iBAAA,EACA,GAAA,CAAA7D,EAAAtL,OACA,OAAAf,EAAAqD,QAAA,EAAA,WAAA,EAUA,IATA,IAAA0L,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAuM,EAAAhQ,KAAA,WAAA,EACA,QAAA,EACA,MAAA,EACA,UAAA,EAEAuQ,EAAA,GACAC,EAAA,GACAC,EAAA,GACArO,EAAA,EACAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EACAqK,EAAArK,GAAAsO,SACAjE,EAAArK,GAAAZ,QAAA,EAAAkO,SAAAa,EACA9D,EAAArK,GAAA+N,IAAAK,EACAC,GAAA3N,KAAA2J,EAAArK,EAAA,EAEA,GAAAmO,EAAApP,OAAA,CAEA,IAFAgO,EACA,2BAAA,EACA/M,EAAA,EAAAA,EAAAmO,EAAApP,OAAA,EAAAiB,EAAA+M,EACA,SAAA/O,EAAA8P,SAAAK,EAAAnO,GAAApC,IAAA,CAAA,EACAmP,EACA,GAAA,CACA,CAEA,GAAAqB,EAAArP,OAAA,CAEA,IAFAgO,EACA,4BAAA,EACA/M,EAAA,EAAAA,EAAAoO,EAAArP,OAAA,EAAAiB,EAAA+M,EACA,SAAA/O,EAAA8P,SAAAM,EAAApO,GAAApC,IAAA,CAAA,EACAmP,EACA,GAAA,CACA,CAEA,GAAAsB,EAAAtP,OAAA,CAEA,IAFAgO,EACA,iBAAA,EACA/M,EAAA,EAAAA,EAAAqO,EAAAtP,OAAA,EAAAiB,EAAA,CACA,IAWAuO,EAXAvB,EAAAqB,EAAArO,GACAkN,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EACAoP,EAAAI,wBAAAP,EAAAE,EACA,6BAAAG,EAAAF,EAAAI,aAAAoB,WAAAxB,EAAAK,aAAAL,EAAAK,WAAA,EACAL,EAAAyB,KAAA1B,EACA,gBAAA,EACA,gCAAAC,EAAAK,YAAAqB,IAAA1B,EAAAK,YAAAsB,KAAA3B,EAAAK,YAAAuB,QAAA,EACA,oEAAA1B,CAAA,EACA,OAAA,EACA,6BAAAA,EAAAF,EAAAK,YAAAzL,SAAA,EAAAoL,EAAAK,YAAAwB,SAAA,CAAA,EACA7B,EAAA8B,OACAP,EAAA,IAAA1P,MAAAwE,UAAAxC,MAAA/C,KAAAkP,EAAAK,WAAA,EAAAvM,KAAA,GAAA,EAAA,IACAiM,EACA,6BAAAG,EAAAvM,OAAAC,aAAArB,MAAAoB,OAAAqM,EAAAK,WAAA,CAAA,EACA,OAAA,EACA,SAAAH,EAAAqB,CAAA,EACA,6CAAArB,EAAAA,CAAA,EACA,GAAA,GACAH,EACA,SAAAG,EAAAF,EAAAK,WAAA,CACA,CAAAN,EACA,GAAA,CACA,CAEA,IADA,IAAAgC,EAAA,CAAA,EACA/O,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GACAf,EAAA2O,EAAAoB,EAAAC,QAAAjC,CAAA,EACAE,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EACAoP,EAAAe,KACAgB,IAAAA,EAAA,CAAA,EAAAhC,EACA,SAAA,GACAA,EACA,0CAAAG,EAAAA,CAAA,EACA,SAAAA,CAAA,EACA,gCAAA,EACAO,EAAAV,EAAAC,EAAA/N,EAAAiO,EAAA,UAAA,EACA,GAAA,GACAF,EAAAM,UAAAP,EACA,uBAAAG,EAAAA,CAAA,EACA,SAAAA,CAAA,EACA,iCAAAA,CAAA,EACAO,EAAAV,EAAAC,EAAA/N,EAAAiO,EAAA,KAAA,EACA,GAAA,IACAH,EACA,uCAAAG,EAAAF,EAAApP,IAAA,EACA6P,EAAAV,EAAAC,EAAA/N,EAAAiO,CAAA,EACAF,EAAAsB,QAAAvB,EACA,cAAA,EACA,SAAA/O,EAAA8P,SAAAd,EAAAsB,OAAA1Q,IAAA,EAAAoP,EAAApP,IAAA,GAEAmP,EACA,GAAA,CACA,CACA,OAAAA,EACA,UAAA,CAEA,C,qCC3SAxO,EAAAR,QAeA,SAAA6P,GAEA,IAAAb,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,4BAAA,EACA,oBAAA,EACA,qDAAAgQ,EAAAC,YAAAqB,OAAA,SAAAlC,GAAA,OAAAA,EAAAe,GAAA,CAAA,EAAAhP,OAAA,WAAA,GAAA,EACA,iBAAA,EACA,kBAAA,EACA6O,EAAAuB,OAAApC,EACA,eAAA,EACA,OAAA,EACAA,EACA,gBAAA,EAGA,IADA,IAAA/M,EAAA,EACAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAAY,EAAAoB,EAAAhP,GAAAZ,QAAA,EACAmL,EAAAyC,EAAAI,wBAAAP,EAAA,QAAAG,EAAAzC,KACA6E,EAAA,IAAApR,EAAA8P,SAAAd,EAAApP,IAAA,EAAAmP,EACA,aAAAC,EAAAxC,EAAA,EAGAwC,EAAAe,KAAAhB,EACA,4BAAAqC,CAAA,EACA,QAAAA,CAAA,EACA,2BAAA,EAEAC,EAAAC,SAAAtC,EAAAhC,WAAA1N,GAAAyP,EACA,OAAAsC,EAAAC,SAAAtC,EAAAhC,QAAA,EACA+B,EACA,QAAA,EAEAsC,EAAAC,SAAA/E,KAAAjN,GAAAyP,EACA,WAAAsC,EAAAC,SAAA/E,EAAA,EACAwC,EACA,YAAA,EAEAA,EACA,kBAAA,EACA,qBAAA,EACA,mBAAA,EACA,0BAAAC,EAAAhC,OAAA,EACA,SAAA,EAEAqE,EAAAE,MAAAhF,KAAAjN,GAAAyP,EACA,uCAAA/M,CAAA,EACA+M,EACA,eAAAxC,CAAA,EAEAwC,EACA,OAAA,EACA,UAAA,EACA,oBAAA,EACA,OAAA,EACA,GAAA,EACA,GAAA,EAEAsC,EAAAZ,KAAAzB,EAAAhC,WAAA1N,GAAAyP,EACA,qDAAAqC,CAAA,EACArC,EACA,cAAAqC,CAAA,GAGApC,EAAAM,UAAAP,EAEA,uBAAAqC,EAAAA,CAAA,EACA,QAAAA,CAAA,EAGAC,EAAAG,OAAAjF,KAAAjN,IAAAyP,EACA,gBAAA,EACA,yBAAA,EACA,iBAAA,EACA,kBAAAqC,EAAA7E,CAAA,EACA,OAAA,EAGA8E,EAAAE,MAAAhF,KAAAjN,GAAAyP,EAAAC,EAAAI,aAAA+B,MACA,+BACA,0CAAAC,EAAApP,CAAA,EACA+M,EACA,kBAAAqC,EAAA7E,CAAA,GAGA8E,EAAAE,MAAAhF,KAAAjN,GAAAyP,EAAAC,EAAAI,aAAA+B,MACA,yBACA,oCAAAC,EAAApP,CAAA,EACA+M,EACA,YAAAqC,EAAA7E,CAAA,EACAwC,EACA,OAAA,EACA,GAAA,CAEA,CASA,IATAA,EACA,UAAA,EACA,iBAAA,EACA,OAAA,EAEA,GAAA,EACA,GAAA,EAGA/M,EAAA,EAAAA,EAAA4N,EAAAoB,EAAAjQ,OAAA,EAAAiB,EAAA,CACA,IAAAyP,EAAA7B,EAAAoB,EAAAhP,GACAyP,EAAAC,UAAA3C,EACA,4BAAA0C,EAAA7R,IAAA,EACA,4CAlHA,qBAkHA6R,EAlHA7R,KAAA,GAkHA,CACA,CAEA,OAAAmP,EACA,UAAA,CAEA,EA7HA,IAAAF,EAAApO,EAAA,EAAA,EACA4Q,EAAA5Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,C,2CCJAF,EAAAR,QA0BA,SAAA6P,GAWA,IATA,IAIAwB,EAJArC,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,QAAA,EACA,mBAAA,EAKAyM,EAAAuD,EAAAC,YAAAhN,MAAA,EAAAoN,KAAAjQ,EAAAkQ,iBAAA,EAEAlO,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,QAAA,EACAH,EAAA2O,EAAAoB,EAAAC,QAAAjC,CAAA,EACAzC,EAAAyC,EAAAI,wBAAAP,EAAA,QAAAG,EAAAzC,KACAoF,EAAAN,EAAAE,MAAAhF,GACA6E,EAAA,IAAApR,EAAA8P,SAAAd,EAAApP,IAAA,EAGAoP,EAAAe,KACAhB,EACA,kDAAAqC,EAAApC,EAAApP,IAAA,EACA,mDAAAwR,CAAA,EACA,4CAAApC,EAAAxC,IAAA,EAAA,KAAA,EAAA,EAAA6E,EAAAO,OAAA5C,EAAAhC,SAAAgC,EAAAhC,OAAA,EACA2E,IAAArS,GAAAyP,EACA,oEAAA9N,EAAAmQ,CAAA,EACArC,EACA,qCAAA,GAAA4C,EAAApF,EAAA6E,CAAA,EACArC,EACA,GAAA,EACA,GAAA,GAGAC,EAAAM,UAAAP,EACA,2BAAAqC,EAAAA,CAAA,EAGApC,EAAAwC,QAAAH,EAAAG,OAAAjF,KAAAjN,GAAAyP,EAEA,uBAAAC,EAAAxC,IAAA,EAAA,KAAA,CAAA,EACA,+BAAA4E,CAAA,EACA,cAAA7E,EAAA6E,CAAA,EACA,YAAA,GAGArC,EAEA,+BAAAqC,CAAA,EACAO,IAAArS,GACAuS,EAAA9C,EAAAC,EAAA/N,EAAAmQ,EAAA,KAAA,EACArC,EACA,0BAAAC,EAAAxC,IAAA,EAAAmF,KAAA,EAAApF,EAAA6E,CAAA,GAEArC,EACA,GAAA,IAIAC,EAAA8C,UAAA/C,EACA,iDAAAqC,EAAApC,EAAApP,IAAA,EAEA+R,IAAArS,GACAuS,EAAA9C,EAAAC,EAAA/N,EAAAmQ,CAAA,EACArC,EACA,uBAAAC,EAAAxC,IAAA,EAAAmF,KAAA,EAAApF,EAAA6E,CAAA,EAGA,CAEA,OAAArC,EACA,UAAA,CAEA,EAhGA,IAAAF,EAAApO,EAAA,EAAA,EACA4Q,EAAA5Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAWA,SAAAoR,EAAA9C,EAAAC,EAAAC,EAAAmC,GACApC,EAAAI,aAAA+B,MACApC,EAAA,+CAAAE,EAAAmC,GAAApC,EAAAxC,IAAA,EAAA,KAAA,GAAAwC,EAAAxC,IAAA,EAAA,KAAA,CAAA,EACAuC,EAAA,oDAAAE,EAAAmC,GAAApC,EAAAxC,IAAA,EAAA,KAAA,CAAA,CACA,C,2CCnBAjM,EAAAR,QAAA8O,EAGA,IAAAkD,EAAAtR,EAAA,EAAA,EAGAuR,KAFAnD,EAAAxJ,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAArD,GAAAsD,UAAA,OAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAcA,SAAAoO,EAAAjP,EAAAgO,EAAA3H,EAAAmM,EAAAC,EAAAC,GAGA,GAFAP,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAEA2H,GAAA,UAAA,OAAAA,EACA,MAAA2E,UAAA,0BAAA,EA0CA,GApCApN,KAAAqL,WAAA,GAMArL,KAAAyI,OAAA3J,OAAAgO,OAAA9M,KAAAqL,UAAA,EAMArL,KAAAiN,QAAAA,EAMAjN,KAAAkN,SAAAA,GAAA,GAMAlN,KAAAmN,cAAAA,EAMAnN,KAAAqN,SAAAlT,GAMAsO,EACA,IAAA,IAAA1J,EAAAD,OAAAC,KAAA0J,CAAA,EAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACA,UAAA,OAAA4L,EAAA1J,EAAAlC,MACAmD,KAAAqL,WAAArL,KAAAyI,OAAA1J,EAAAlC,IAAA4L,EAAA1J,EAAAlC,KAAAkC,EAAAlC,GACA,CAgBA6M,EAAA4D,SAAA,SAAA7S,EAAAqM,GACAyG,EAAA,IAAA7D,EAAAjP,EAAAqM,EAAA2B,OAAA3B,EAAAhG,QAAAgG,EAAAmG,QAAAnG,EAAAoG,QAAA,EAEA,OADAK,EAAAF,SAAAvG,EAAAuG,SACAE,CACA,EAOA7D,EAAAxJ,UAAAsN,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,UAAA7K,KAAAc,QACA,gBAAAd,KAAAmN,cACA,SAAAnN,KAAAyI,OACA,WAAAzI,KAAAqN,UAAArN,KAAAqN,SAAAzR,OAAAoE,KAAAqN,SAAAlT,GACA,UAAAuT,EAAA1N,KAAAiN,QAAA9S,GACA,WAAAuT,EAAA1N,KAAAkN,SAAA/S,GACA,CACA,EAYAuP,EAAAxJ,UAAA0N,IAAA,SAAAnT,EAAA4M,EAAA4F,EAAAnM,GAGA,GAAA,CAAAjG,EAAAgT,SAAApT,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,GAAA,CAAAvS,EAAAiT,UAAAzG,CAAA,EACA,MAAA+F,UAAA,uBAAA,EAEA,GAAApN,KAAAyI,OAAAhO,KAAAN,GACA,MAAA6D,MAAA,mBAAAvD,EAAA,QAAAuF,IAAA,EAEA,GAAAA,KAAA+N,aAAA1G,CAAA,EACA,MAAArJ,MAAA,MAAAqJ,EAAA,mBAAArH,IAAA,EAEA,GAAAA,KAAAgO,eAAAvT,CAAA,EACA,MAAAuD,MAAA,SAAAvD,EAAA,oBAAAuF,IAAA,EAEA,GAAAA,KAAAqL,WAAAhE,KAAAlN,GAAA,CACA,GAAA6F,CAAAA,KAAAc,SAAAd,CAAAA,KAAAc,QAAAmN,YACA,MAAAjQ,MAAA,gBAAAqJ,EAAA,OAAArH,IAAA,EACAA,KAAAyI,OAAAhO,GAAA4M,CACA,MACArH,KAAAqL,WAAArL,KAAAyI,OAAAhO,GAAA4M,GAAA5M,EASA,OAPAqG,IACAd,KAAAmN,gBAAAhT,KACA6F,KAAAmN,cAAA,IACAnN,KAAAmN,cAAA1S,GAAAqG,GAAA,MAGAd,KAAAkN,SAAAzS,GAAAwS,GAAA,KACAjN,IACA,EASA0J,EAAAxJ,UAAAgO,OAAA,SAAAzT,GAEA,GAAA,CAAAI,EAAAgT,SAAApT,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,IAAAlL,EAAAlC,KAAAyI,OAAAhO,GACA,GAAA,MAAAyH,EACA,MAAAlE,MAAA,SAAAvD,EAAA,uBAAAuF,IAAA,EAQA,OANA,OAAAA,KAAAqL,WAAAnJ,GACA,OAAAlC,KAAAyI,OAAAhO,GACA,OAAAuF,KAAAkN,SAAAzS,GACAuF,KAAAmN,eACA,OAAAnN,KAAAmN,cAAA1S,GAEAuF,IACA,EAOA0J,EAAAxJ,UAAA6N,aAAA,SAAA1G,GACA,OAAAwF,EAAAkB,aAAA/N,KAAAqN,SAAAhG,CAAA,CACA,EAOAqC,EAAAxJ,UAAA8N,eAAA,SAAAvT,GACA,OAAAoS,EAAAmB,eAAAhO,KAAAqN,SAAA5S,CAAA,CACA,C,2CCpMAW,EAAAR,QAAAuT,EAGA,IAOAC,EAPAxB,EAAAtR,EAAA,EAAA,EAGAoO,KAFAyE,EAAAjO,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAoB,GAAAnB,UAAA,QAEA1R,EAAA,EAAA,GACA4Q,EAAA5Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAIA+S,EAAA,+BAyCA,SAAAF,EAAA1T,EAAA4M,EAAAD,EAAAwB,EAAA0F,EAAAxN,EAAAmM,GAcA,GAZApS,EAAA0T,SAAA3F,CAAA,GACAqE,EAAAqB,EACAxN,EAAA8H,EACAA,EAAA0F,EAAAnU,IACAU,EAAA0T,SAAAD,CAAA,IACArB,EAAAnM,EACAA,EAAAwN,EACAA,EAAAnU,IAGAyS,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAEA,CAAAjG,EAAAiT,UAAAzG,CAAA,GAAAA,EAAA,EACA,MAAA+F,UAAA,mCAAA,EAEA,GAAA,CAAAvS,EAAAgT,SAAAzG,CAAA,EACA,MAAAgG,UAAA,uBAAA,EAEA,GAAAxE,IAAAzO,IAAA,CAAAkU,EAAApQ,KAAA2K,EAAAA,EAAAnK,SAAA,EAAA+P,YAAA,CAAA,EACA,MAAApB,UAAA,4BAAA,EAEA,GAAAkB,IAAAnU,IAAA,CAAAU,EAAAgT,SAAAS,CAAA,EACA,MAAAlB,UAAA,yBAAA,EASApN,KAAA4I,MAFAA,EADA,oBAAAA,EACA,WAEAA,IAAA,aAAAA,EAAAA,EAAAzO,GAMA6F,KAAAoH,KAAAA,EAMApH,KAAAqH,GAAAA,EAMArH,KAAAsO,OAAAA,GAAAnU,GAMA6F,KAAAuM,SAAA,aAAA3D,EAMA5I,KAAA2M,SAAA,CAAA3M,KAAAuM,SAMAvM,KAAAmK,SAAA,aAAAvB,EAMA5I,KAAA4K,IAAA,CAAA,EAMA5K,KAAAyO,QAAA,KAMAzO,KAAAmL,OAAA,KAMAnL,KAAAkK,YAAA,KAMAlK,KAAA0O,aAAA,KAMA1O,KAAAsL,KAAAzQ,CAAAA,CAAAA,EAAAI,MAAAiR,EAAAZ,KAAAlE,KAAAjN,GAMA6F,KAAA2L,MAAA,UAAAvE,EAMApH,KAAAiK,aAAA,KAMAjK,KAAA2O,eAAA,KAMA3O,KAAA4O,eAAA,KAOA5O,KAAA6O,EAAA,KAMA7O,KAAAiN,QAAAA,CACA,CAjKAkB,EAAAb,SAAA,SAAA7S,EAAAqM,GACA,OAAA,IAAAqH,EAAA1T,EAAAqM,EAAAO,GAAAP,EAAAM,KAAAN,EAAA8B,KAAA9B,EAAAwH,OAAAxH,EAAAhG,QAAAgG,EAAAmG,OAAA,CACA,EAuKAnO,OAAAgQ,eAAAX,EAAAjO,UAAA,SAAA,CACAsJ,IAAA,WAIA,OAFA,OAAAxJ,KAAA6O,IACA7O,KAAA6O,EAAA,CAAA,IAAA7O,KAAA+O,UAAA,QAAA,GACA/O,KAAA6O,CACA,CACA,CAAA,EAKAV,EAAAjO,UAAA8O,UAAA,SAAAvU,EAAAgF,EAAAwP,GAGA,MAFA,WAAAxU,IACAuF,KAAA6O,EAAA,MACAjC,EAAA1M,UAAA8O,UAAArU,KAAAqF,KAAAvF,EAAAgF,EAAAwP,CAAA,CACA,EAuBAd,EAAAjO,UAAAsN,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,OAAA,aAAA7K,KAAA4I,MAAA5I,KAAA4I,MAAAzO,GACA,OAAA6F,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAsO,OACA,UAAAtO,KAAAc,QACA,UAAA4M,EAAA1N,KAAAiN,QAAA9S,GACA,CACA,EAOAgU,EAAAjO,UAAAjE,QAAA,WAEA,IAsCAkG,EAtCA,OAAAnC,KAAAkP,SACAlP,OAEAA,KAAAkK,YAAAgC,EAAAC,SAAAnM,KAAAoH,SAAAjN,IACA6F,KAAAiK,cAAAjK,KAAA4O,gBAAA5O,MAAAmP,OAAAC,iBAAApP,KAAAoH,IAAA,EACApH,KAAAiK,wBAAAmE,EACApO,KAAAkK,YAAA,KAEAlK,KAAAkK,YAAAlK,KAAAiK,aAAAxB,OAAA3J,OAAAC,KAAAiB,KAAAiK,aAAAxB,MAAA,EAAA,KACAzI,KAAAc,SAAAd,KAAAc,QAAAuO,kBAEArP,KAAAkK,YAAA,MAIAlK,KAAAc,SAAA,MAAAd,KAAAc,QAAA,UACAd,KAAAkK,YAAAlK,KAAAc,QAAA,QACAd,KAAAiK,wBAAAP,GAAA,UAAA,OAAA1J,KAAAkK,cACAlK,KAAAkK,YAAAlK,KAAAiK,aAAAxB,OAAAzI,KAAAkK,eAIAlK,KAAAc,UACA,CAAA,IAAAd,KAAAc,QAAAuL,SAAArM,KAAAc,QAAAuL,SAAAlS,IAAA6F,CAAAA,KAAAiK,cAAAjK,KAAAiK,wBAAAP,IACA,OAAA1J,KAAAc,QAAAuL,OACAvN,OAAAC,KAAAiB,KAAAc,OAAA,EAAAlF,SACAoE,KAAAc,QAAA3G,KAIA6F,KAAAsL,MACAtL,KAAAkK,YAAArP,EAAAI,KAAAqU,WAAAtP,KAAAkK,YAAA,MAAAlK,KAAAoH,KAAA,IAAApH,GAAA,EAGAlB,OAAAyQ,QACAzQ,OAAAyQ,OAAAvP,KAAAkK,WAAA,GAEAlK,KAAA2L,OAAA,UAAA,OAAA3L,KAAAkK,cAEArP,EAAAwB,OAAA4B,KAAA+B,KAAAkK,WAAA,EACArP,EAAAwB,OAAAwB,OAAAmC,KAAAkK,YAAA/H,EAAAtH,EAAA2U,UAAA3U,EAAAwB,OAAAT,OAAAoE,KAAAkK,WAAA,CAAA,EAAA,CAAA,EAEArP,EAAAyL,KAAAG,MAAAzG,KAAAkK,YAAA/H,EAAAtH,EAAA2U,UAAA3U,EAAAyL,KAAA1K,OAAAoE,KAAAkK,WAAA,CAAA,EAAA,CAAA,EACAlK,KAAAkK,YAAA/H,GAIAnC,KAAA4K,IACA5K,KAAA0O,aAAA7T,EAAA4U,YACAzP,KAAAmK,SACAnK,KAAA0O,aAAA7T,EAAA6U,WAEA1P,KAAA0O,aAAA1O,KAAAkK,YAGAlK,KAAAmP,kBAAAf,IACApO,KAAAmP,OAAAQ,KAAAzP,UAAAF,KAAAvF,MAAAuF,KAAA0O,cAEA9B,EAAA1M,UAAAjE,QAAAtB,KAAAqF,IAAA,EACA,EAsBAmO,EAAAyB,EAAA,SAAAC,EAAAC,EAAAC,EAAArB,GAUA,MAPA,YAAA,OAAAoB,EACAA,EAAAjV,EAAAmV,aAAAF,CAAA,EAAArV,KAGAqV,GAAA,UAAA,OAAAA,IACAA,EAAAjV,EAAAoV,aAAAH,CAAA,EAAArV,MAEA,SAAAyF,EAAAgQ,GACArV,EAAAmV,aAAA9P,EAAA6M,WAAA,EACAa,IAAA,IAAAO,EAAA+B,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAAzB,CAAA,CAAA,CAAA,CACA,CACA,EAgBAP,EAAAiC,EAAA,SAAAC,GACAjC,EAAAiC,CACA,C,iDCvXA,IAAA9V,EAAAa,EAAAR,QAAAU,EAAA,EAAA,EAEAf,EAAA+V,MAAA,QAoDA/V,EAAAgW,KAjCA,SAAA1P,EAAA2P,EAAAzP,GAMA,OAHAyP,EAFA,YAAA,OAAAA,GACAzP,EAAAyP,EACA,IAAAjW,EAAAkW,MACAD,GACA,IAAAjW,EAAAkW,MACAF,KAAA1P,EAAAE,CAAA,CACA,EA0CAxG,EAAAmW,SANA,SAAA7P,EAAA2P,GAGA,OADAA,EADAA,GACA,IAAAjW,EAAAkW,MACAC,SAAA7P,CAAA,CACA,EAKAtG,EAAAoW,QAAArV,EAAA,EAAA,EACAf,EAAAqW,QAAAtV,EAAA,EAAA,EACAf,EAAAsW,SAAAvV,EAAA,EAAA,EACAf,EAAAgQ,UAAAjP,EAAA,EAAA,EAGAf,EAAAqS,iBAAAtR,EAAA,EAAA,EACAf,EAAAsS,UAAAvR,EAAA,EAAA,EACAf,EAAAkW,KAAAnV,EAAA,EAAA,EACAf,EAAAmP,KAAApO,EAAA,EAAA,EACAf,EAAA6T,KAAA9S,EAAA,EAAA,EACAf,EAAA4T,MAAA7S,EAAA,EAAA,EACAf,EAAAuW,MAAAxV,EAAA,EAAA,EACAf,EAAAwW,SAAAzV,EAAA,EAAA,EACAf,EAAAyW,QAAA1V,EAAA,EAAA,EACAf,EAAA0W,OAAA3V,EAAA,EAAA,EAGAf,EAAA2W,QAAA5V,EAAA,EAAA,EACAf,EAAA4W,SAAA7V,EAAA,EAAA,EAGAf,EAAA2R,MAAA5Q,EAAA,EAAA,EACAf,EAAAM,KAAAS,EAAA,EAAA,EAGAf,EAAAqS,iBAAAwD,EAAA7V,EAAAkW,IAAA,EACAlW,EAAAsS,UAAAuD,EAAA7V,EAAA6T,KAAA7T,EAAAyW,QAAAzW,EAAAmP,IAAA,EACAnP,EAAAkW,KAAAL,EAAA7V,EAAA6T,IAAA,EACA7T,EAAA4T,MAAAiC,EAAA7V,EAAA6T,IAAA,C,2ICtGA,IAAA7T,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAAuV,EAAA,EACA7V,EAAA6W,OAAAhB,EAAA7V,EAAA8W,YAAA,EACA9W,EAAA+W,OAAAlB,EAAA7V,EAAAgX,YAAA,CACA,CAvBAhX,EAAA+V,MAAA,UAGA/V,EAAA6W,OAAA9V,EAAA,EAAA,EACAf,EAAA8W,aAAA/V,EAAA,EAAA,EACAf,EAAA+W,OAAAhW,EAAA,EAAA,EACAf,EAAAgX,aAAAjW,EAAA,EAAA,EAGAf,EAAAM,KAAAS,EAAA,EAAA,EACAf,EAAAiX,IAAAlW,EAAA,EAAA,EACAf,EAAAkX,MAAAnW,EAAA,EAAA,EACAf,EAAAY,UAAAA,EAcAA,EAAA,C,mEClCAZ,EAAAa,EAAAR,QAAAU,EAAA,EAAA,EAEAf,EAAA+V,MAAA,OAGA/V,EAAAmX,SAAApW,EAAA,EAAA,EACAf,EAAAoX,MAAArW,EAAA,EAAA,EACAf,EAAAqM,OAAAtL,EAAA,EAAA,EAGAf,EAAAkW,KAAAL,EAAA7V,EAAA6T,KAAA7T,EAAAoX,MAAApX,EAAAqM,MAAA,C,iDCVAxL,EAAAR,QAAAmW,EAGA,IAAA5C,EAAA7S,EAAA,EAAA,EAGA4Q,KAFA6E,EAAA7Q,UAAApB,OAAAgO,OAAAqB,EAAAjO,SAAA,GAAA6M,YAAAgE,GAAA/D,UAAA,WAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAcA,SAAAyV,EAAAtW,EAAA4M,EAAAQ,EAAAT,EAAAtG,EAAAmM,GAIA,GAHAkB,EAAAxT,KAAAqF,KAAAvF,EAAA4M,EAAAD,EAAAjN,GAAAA,GAAA2G,EAAAmM,CAAA,EAGA,CAAApS,EAAAgT,SAAAhG,CAAA,EACA,MAAAuF,UAAA,0BAAA,EAMApN,KAAA6H,QAAAA,EAMA7H,KAAA4R,gBAAA,KAGA5R,KAAA4K,IAAA,CAAA,CACA,CAuBAmG,EAAAzD,SAAA,SAAA7S,EAAAqM,GACA,OAAA,IAAAiK,EAAAtW,EAAAqM,EAAAO,GAAAP,EAAAe,QAAAf,EAAAM,KAAAN,EAAAhG,QAAAgG,EAAAmG,OAAA,CACA,EAOA8D,EAAA7Q,UAAAsN,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,UAAA7K,KAAA6H,QACA,OAAA7H,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAsO,OACA,UAAAtO,KAAAc,QACA,UAAA4M,EAAA1N,KAAAiN,QAAA9S,GACA,CACA,EAKA4W,EAAA7Q,UAAAjE,QAAA,WACA,GAAA+D,KAAAkP,SACA,OAAAlP,KAGA,GAAAkM,EAAAO,OAAAzM,KAAA6H,WAAA1N,GACA,MAAA6D,MAAA,qBAAAgC,KAAA6H,OAAA,EAEA,OAAAsG,EAAAjO,UAAAjE,QAAAtB,KAAAqF,IAAA,CACA,EAYA+Q,EAAAnB,EAAA,SAAAC,EAAAgC,EAAAC,GAUA,MAPA,YAAA,OAAAA,EACAA,EAAAjX,EAAAmV,aAAA8B,CAAA,EAAArX,KAGAqX,GAAA,UAAA,OAAAA,IACAA,EAAAjX,EAAAoV,aAAA6B,CAAA,EAAArX,MAEA,SAAAyF,EAAAgQ,GACArV,EAAAmV,aAAA9P,EAAA6M,WAAA,EACAa,IAAA,IAAAmD,EAAAb,EAAAL,EAAAgC,EAAAC,CAAA,CAAA,CACA,CACA,C,2CC5HA1W,EAAAR,QAAAsW,EAEA,IAAArW,EAAAS,EAAA,EAAA,EASA,SAAA4V,EAAAa,GAEA,GAAAA,EACA,IAAA,IAAAhT,EAAAD,OAAAC,KAAAgT,CAAA,EAAAlV,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAmD,KAAAjB,EAAAlC,IAAAkV,EAAAhT,EAAAlC,GACA,CAyBAqU,EAAApE,OAAA,SAAAiF,GACA,OAAA/R,KAAAgS,MAAAlF,OAAAiF,CAAA,CACA,EAUAb,EAAApU,OAAA,SAAA2R,EAAAwD,GACA,OAAAjS,KAAAgS,MAAAlV,OAAA2R,EAAAwD,CAAA,CACA,EAUAf,EAAAgB,gBAAA,SAAAzD,EAAAwD,GACA,OAAAjS,KAAAgS,MAAAE,gBAAAzD,EAAAwD,CAAA,CACA,EAWAf,EAAArT,OAAA,SAAAsU,GACA,OAAAnS,KAAAgS,MAAAnU,OAAAsU,CAAA,CACA,EAWAjB,EAAAkB,gBAAA,SAAAD,GACA,OAAAnS,KAAAgS,MAAAI,gBAAAD,CAAA,CACA,EASAjB,EAAAmB,OAAA,SAAA5D,GACA,OAAAzO,KAAAgS,MAAAK,OAAA5D,CAAA,CACA,EASAyC,EAAA1G,WAAA,SAAA8H,GACA,OAAAtS,KAAAgS,MAAAxH,WAAA8H,CAAA,CACA,EAUApB,EAAArG,SAAA,SAAA4D,EAAA3N,GACA,OAAAd,KAAAgS,MAAAnH,SAAA4D,EAAA3N,CAAA,CACA,EAMAoQ,EAAAhR,UAAAsN,OAAA,WACA,OAAAxN,KAAAgS,MAAAnH,SAAA7K,KAAAnF,EAAA4S,aAAA,CACA,C,+BCvIArS,EAAAR,QAAAqW,EAGA,IAAArE,EAAAtR,EAAA,EAAA,EAGAT,KAFAoW,EAAA/Q,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAkE,GAAAjE,UAAA,SAEA1R,EAAA,EAAA,GAiBA,SAAA2V,EAAAxW,EAAA2M,EAAAmL,EAAA3Q,EAAA4Q,EAAAC,EAAA3R,EAAAmM,EAAAyF,GAYA,GATA7X,EAAA0T,SAAAiE,CAAA,GACA1R,EAAA0R,EACAA,EAAAC,EAAAtY,IACAU,EAAA0T,SAAAkE,CAAA,IACA3R,EAAA2R,EACAA,EAAAtY,IAIAiN,IAAAjN,IAAAU,CAAAA,EAAAgT,SAAAzG,CAAA,EACA,MAAAgG,UAAA,uBAAA,EAGA,GAAA,CAAAvS,EAAAgT,SAAA0E,CAAA,EACA,MAAAnF,UAAA,8BAAA,EAGA,GAAA,CAAAvS,EAAAgT,SAAAjM,CAAA,EACA,MAAAwL,UAAA,+BAAA,EAEAR,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAAoH,KAAAA,GAAA,MAMApH,KAAAuS,YAAAA,EAMAvS,KAAAwS,cAAAA,CAAAA,CAAAA,GAAArY,GAMA6F,KAAA4B,aAAAA,EAMA5B,KAAAyS,eAAAA,CAAAA,CAAAA,GAAAtY,GAMA6F,KAAA2S,oBAAA,KAMA3S,KAAA4S,qBAAA,KAMA5S,KAAAiN,QAAAA,EAKAjN,KAAA0S,cAAAA,CACA,CAsBAzB,EAAA3D,SAAA,SAAA7S,EAAAqM,GACA,OAAA,IAAAmK,EAAAxW,EAAAqM,EAAAM,KAAAN,EAAAyL,YAAAzL,EAAAlF,aAAAkF,EAAA0L,cAAA1L,EAAA2L,eAAA3L,EAAAhG,QAAAgG,EAAAmG,QAAAnG,EAAA4L,aAAA,CACA,EAOAzB,EAAA/Q,UAAAsN,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,OAAA,QAAA7K,KAAAoH,MAAApH,KAAAoH,MAAAjN,GACA,cAAA6F,KAAAuS,YACA,gBAAAvS,KAAAwS,cACA,eAAAxS,KAAA4B,aACA,iBAAA5B,KAAAyS,eACA,UAAAzS,KAAAc,QACA,UAAA4M,EAAA1N,KAAAiN,QAAA9S,GACA,gBAAA6F,KAAA0S,cACA,CACA,EAKAzB,EAAA/Q,UAAAjE,QAAA,WAGA,OAAA+D,KAAAkP,SACAlP,MAEAA,KAAA2S,oBAAA3S,KAAAmP,OAAA0D,WAAA7S,KAAAuS,WAAA,EACAvS,KAAA4S,qBAAA5S,KAAAmP,OAAA0D,WAAA7S,KAAA4B,YAAA,EAEAgL,EAAA1M,UAAAjE,QAAAtB,KAAAqF,IAAA,EACA,C,qCC9JA5E,EAAAR,QAAAiS,EAGA,IAOAuB,EACA4C,EACAtH,EATAkD,EAAAtR,EAAA,EAAA,EAGA6S,KAFAtB,EAAA3M,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAF,GAAAG,UAAA,YAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EACAwV,EAAAxV,EAAA,EAAA,EAoCA,SAAAwX,EAAAC,EAAAtF,GACA,GAAAsF,CAAAA,GAAAA,CAAAA,EAAAnX,OACA,OAAAzB,GAEA,IADA,IAAA6Y,EAAA,GACAnW,EAAA,EAAAA,EAAAkW,EAAAnX,OAAA,EAAAiB,EACAmW,EAAAD,EAAAlW,GAAApC,MAAAsY,EAAAlW,GAAA2Q,OAAAC,CAAA,EACA,OAAAuF,CACA,CA2CA,SAAAnG,EAAApS,EAAAqG,GACA8L,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAA+G,OAAA5M,GAOA6F,KAAAiT,EAAA,IACA,CAEA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,CACA,CAjFAtG,EAAAS,SAAA,SAAA7S,EAAAqM,GACA,OAAA,IAAA+F,EAAApS,EAAAqM,EAAAhG,OAAA,EAAAsS,QAAAtM,EAAAC,MAAA,CACA,EAkBA8F,EAAAiG,YAAAA,EAQAjG,EAAAkB,aAAA,SAAAV,EAAAhG,GACA,GAAAgG,EACA,IAAA,IAAAxQ,EAAA,EAAAA,EAAAwQ,EAAAzR,OAAA,EAAAiB,EACA,GAAA,UAAA,OAAAwQ,EAAAxQ,IAAAwQ,EAAAxQ,GAAA,IAAAwK,GAAAgG,EAAAxQ,GAAA,GAAAwK,EACA,MAAA,CAAA,EACA,MAAA,CAAA,CACA,EAQAwF,EAAAmB,eAAA,SAAAX,EAAA5S,GACA,GAAA4S,EACA,IAAA,IAAAxQ,EAAA,EAAAA,EAAAwQ,EAAAzR,OAAA,EAAAiB,EACA,GAAAwQ,EAAAxQ,KAAApC,EACA,MAAA,CAAA,EACA,MAAA,CAAA,CACA,EAyCAqE,OAAAgQ,eAAAjC,EAAA3M,UAAA,cAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAiT,IAAAjT,KAAAiT,EAAApY,EAAAwY,QAAArT,KAAA+G,MAAA,EACA,CACA,CAAA,EA0BA8F,EAAA3M,UAAAsN,OAAA,SAAAC,GACA,OAAA5S,EAAAgQ,SAAA,CACA,UAAA7K,KAAAc,QACA,SAAAgS,EAAA9S,KAAAsT,YAAA7F,CAAA,EACA,CACA,EAOAZ,EAAA3M,UAAAkT,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAxM,EAAAyM,EAAA1U,OAAAC,KAAAwU,CAAA,EAAA1W,EAAA,EAAAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EACAkK,EAAAwM,EAAAC,EAAA3W,IAJAmD,KAKA4N,KACA7G,EAAAG,SAAA/M,GACAiU,EACArH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAA0M,UAAAtZ,GACA6W,EACAjK,EAAAM,KAAAlN,GACAgU,EACAtB,GAPAS,SAOAkG,EAAA3W,GAAAkK,CAAA,CACA,EAGA,OAAA/G,IACA,EAOA6M,EAAA3M,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,IACA,EASAoS,EAAA3M,UAAAwT,QAAA,SAAAjZ,GACA,GAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,aAAAiP,EACA,OAAA1J,KAAA+G,OAAAtM,GAAAgO,OACA,MAAAzK,MAAA,iBAAAvD,CAAA,CACA,EASAoS,EAAA3M,UAAA0N,IAAA,SAAA0E,GAEA,GAAA,EAAAA,aAAAnE,GAAAmE,EAAAhE,SAAAnU,IAAAmY,aAAAlE,GAAAkE,aAAAxB,GAAAwB,aAAA5I,GAAA4I,aAAAtB,GAAAsB,aAAAzF,GACA,MAAAO,UAAA,sCAAA,EAEA,GAAApN,KAAA+G,OAEA,CACA,IAAA4M,EAAA3T,KAAAwJ,IAAA8I,EAAA7X,IAAA,EACA,GAAAkZ,EAAA,CACA,GAAAA,EAAAA,aAAA9G,GAAAyF,aAAAzF,IAAA8G,aAAAvF,GAAAuF,aAAA3C,EAWA,MAAAhT,MAAA,mBAAAsU,EAAA7X,KAAA,QAAAuF,IAAA,EARA,IADA,IAAA+G,EAAA4M,EAAAL,YACAzW,EAAA,EAAAA,EAAAkK,EAAAnL,OAAA,EAAAiB,EACAyV,EAAA1E,IAAA7G,EAAAlK,EAAA,EACAmD,KAAAkO,OAAAyF,CAAA,EACA3T,KAAA+G,SACA/G,KAAA+G,OAAA,IACAuL,EAAAsB,WAAAD,EAAA7S,QAAA,CAAA,CAAA,CAIA,CACA,MAjBAd,KAAA+G,OAAA,GAoBA,OAFA/G,KAAA+G,OAAAuL,EAAA7X,MAAA6X,GACAuB,MAAA7T,IAAA,EACAkT,EAAAlT,IAAA,CACA,EASA6M,EAAA3M,UAAAgO,OAAA,SAAAoE,GAEA,GAAA,EAAAA,aAAA1F,GACA,MAAAQ,UAAA,mCAAA,EACA,GAAAkF,EAAAnD,SAAAnP,KACA,MAAAhC,MAAAsU,EAAA,uBAAAtS,IAAA,EAOA,OALA,OAAAA,KAAA+G,OAAAuL,EAAA7X,MACAqE,OAAAC,KAAAiB,KAAA+G,MAAA,EAAAnL,SACAoE,KAAA+G,OAAA5M,IAEAmY,EAAAwB,SAAA9T,IAAA,EACAkT,EAAAlT,IAAA,CACA,EAQA6M,EAAA3M,UAAAnF,OAAA,SAAAyK,EAAAsB,GAEA,GAAAjM,EAAAgT,SAAArI,CAAA,EACAA,EAAAA,EAAAE,MAAA,GAAA,OACA,GAAA,CAAAhK,MAAAqY,QAAAvO,CAAA,EACA,MAAA4H,UAAA,cAAA,EACA,GAAA5H,GAAAA,EAAA5J,QAAA,KAAA4J,EAAA,GACA,MAAAxH,MAAA,uBAAA,EAGA,IADA,IAAAgW,EAAAhU,KACA,EAAAwF,EAAA5J,QAAA,CACA,IAAAqY,EAAAzO,EAAAK,MAAA,EACA,GAAAmO,EAAAjN,QAAAiN,EAAAjN,OAAAkN,IAEA,GAAA,GADAD,EAAAA,EAAAjN,OAAAkN,cACApH,GACA,MAAA7O,MAAA,2CAAA,CAAA,MAEAgW,EAAApG,IAAAoG,EAAA,IAAAnH,EAAAoH,CAAA,CAAA,CACA,CAGA,OAFAnN,GACAkN,EAAAZ,QAAAtM,CAAA,EACAkN,CACA,EAMAnH,EAAA3M,UAAAgU,WAAA,WAEA,IADA,IAAAnN,EAAA/G,KAAAsT,YAAAzW,EAAA,EACAA,EAAAkK,EAAAnL,QACAmL,EAAAlK,aAAAgQ,EACA9F,EAAAlK,CAAA,IAAAqX,WAAA,EAEAnN,EAAAlK,CAAA,IAAAZ,QAAA,EACA,OAAA+D,KAAA/D,QAAA,CACA,EASA4Q,EAAA3M,UAAAiU,OAAA,SAAA3O,EAAA4O,EAAAC,GASA,GANA,WAAA,OAAAD,GACAC,EAAAD,EACAA,EAAAja,IACAia,GAAA,CAAA1Y,MAAAqY,QAAAK,CAAA,IACAA,EAAA,CAAAA,IAEAvZ,EAAAgT,SAAArI,CAAA,GAAAA,EAAA5J,OAAA,CACA,GAAA,MAAA4J,EACA,OAAAxF,KAAAwQ,KACAhL,EAAAA,EAAAE,MAAA,GAAA,CACA,MAAA,GAAA,CAAAF,EAAA5J,OACA,OAAAoE,KAGA,GAAA,KAAAwF,EAAA,GACA,OAAAxF,KAAAwQ,KAAA2D,OAAA3O,EAAA9H,MAAA,CAAA,EAAA0W,CAAA,EAGA,IAAAE,EAAAtU,KAAAwJ,IAAAhE,EAAA,EAAA,EACA,GAAA8O,GACA,GAAA,IAAA9O,EAAA5J,QACA,GAAA,CAAAwY,GAAAA,CAAAA,EAAAtI,QAAAwI,EAAAvH,WAAA,EACA,OAAAuH,CAAA,MACA,GAAAA,aAAAzH,IAAAyH,EAAAA,EAAAH,OAAA3O,EAAA9H,MAAA,CAAA,EAAA0W,EAAA,CAAA,CAAA,GACA,OAAAE,CAAA,MAIA,IAAA,IAAAzX,EAAA,EAAAA,EAAAmD,KAAAsT,YAAA1X,OAAA,EAAAiB,EACA,GAAAmD,KAAAiT,EAAApW,aAAAgQ,IAAAyH,EAAAtU,KAAAiT,EAAApW,GAAAsX,OAAA3O,EAAA4O,EAAA,CAAA,CAAA,GACA,OAAAE,EAGA,OAAA,OAAAtU,KAAAmP,QAAAkF,EACA,KACArU,KAAAmP,OAAAgF,OAAA3O,EAAA4O,CAAA,CACA,EAoBAvH,EAAA3M,UAAA2S,WAAA,SAAArN,GACA,IAAA8O,EAAAtU,KAAAmU,OAAA3O,EAAA,CAAA4I,EAAA,EACA,GAAAkG,EAEA,OAAAA,EADA,MAAAtW,MAAA,iBAAAwH,CAAA,CAEA,EASAqH,EAAA3M,UAAAqU,WAAA,SAAA/O,GACA,IAAA8O,EAAAtU,KAAAmU,OAAA3O,EAAA,CAAAkE,EAAA,EACA,GAAA4K,EAEA,OAAAA,EADA,MAAAtW,MAAA,iBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EASA6M,EAAA3M,UAAAkP,iBAAA,SAAA5J,GACA,IAAA8O,EAAAtU,KAAAmU,OAAA3O,EAAA,CAAA4I,EAAA1E,EAAA,EACA,GAAA4K,EAEA,OAAAA,EADA,MAAAtW,MAAA,yBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EASA6M,EAAA3M,UAAAsU,cAAA,SAAAhP,GACA,IAAA8O,EAAAtU,KAAAmU,OAAA3O,EAAA,CAAAwL,EAAA,EACA,GAAAsD,EAEA,OAAAA,EADA,MAAAtW,MAAA,oBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EAGA6M,EAAAuD,EAAA,SAAAC,EAAAoE,EAAAC,GACAtG,EAAAiC,EACAW,EAAAyD,EACA/K,EAAAgL,CACA,C,kDC/aAtZ,EAAAR,QAAAgS,GAEAI,UAAA,mBAEA,IAEAyD,EAFA5V,EAAAS,EAAA,EAAA,EAYA,SAAAsR,EAAAnS,EAAAqG,GAEA,GAAA,CAAAjG,EAAAgT,SAAApT,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,GAAAtM,GAAA,CAAAjG,EAAA0T,SAAAzN,CAAA,EACA,MAAAsM,UAAA,2BAAA,EAMApN,KAAAc,QAAAA,EAMAd,KAAA0S,cAAA,KAMA1S,KAAAvF,KAAAA,EAMAuF,KAAAmP,OAAA,KAMAnP,KAAAkP,SAAA,CAAA,EAMAlP,KAAAiN,QAAA,KAMAjN,KAAAa,SAAA,IACA,CAEA/B,OAAA6V,iBAAA/H,EAAA1M,UAAA,CAQAsQ,KAAA,CACAhH,IAAA,WAEA,IADA,IAAAwK,EAAAhU,KACA,OAAAgU,EAAA7E,QACA6E,EAAAA,EAAA7E,OACA,OAAA6E,CACA,CACA,EAQA5J,SAAA,CACAZ,IAAA,WAGA,IAFA,IAAAhE,EAAA,CAAAxF,KAAAvF,MACAuZ,EAAAhU,KAAAmP,OACA6E,GACAxO,EAAAoP,QAAAZ,EAAAvZ,IAAA,EACAuZ,EAAAA,EAAA7E,OAEA,OAAA3J,EAAA7H,KAAA,GAAA,CACA,CACA,CACA,CAAA,EAOAiP,EAAA1M,UAAAsN,OAAA,WACA,MAAAxP,MAAA,CACA,EAOA4O,EAAA1M,UAAA2T,MAAA,SAAA1E,GACAnP,KAAAmP,QAAAnP,KAAAmP,SAAAA,GACAnP,KAAAmP,OAAAjB,OAAAlO,IAAA,EACAA,KAAAmP,OAAAA,EACAnP,KAAAkP,SAAA,CAAA,EACAsB,EAAArB,EAAAqB,KACAA,aAAAC,GACAD,EAAAqE,EAAA7U,IAAA,CACA,EAOA4M,EAAA1M,UAAA4T,SAAA,SAAA3E,GACAqB,EAAArB,EAAAqB,KACAA,aAAAC,GACAD,EAAAsE,EAAA9U,IAAA,EACAA,KAAAmP,OAAA,KACAnP,KAAAkP,SAAA,CAAA,CACA,EAMAtC,EAAA1M,UAAAjE,QAAA,WAKA,OAJA+D,KAAAkP,UAEAlP,KAAAwQ,gBAAAC,IACAzQ,KAAAkP,SAAA,CAAA,GACAlP,IACA,EAOA4M,EAAA1M,UAAA6O,UAAA,SAAAtU,GACA,OAAAuF,KAAAc,QACAd,KAAAc,QAAArG,GACAN,EACA,EASAyS,EAAA1M,UAAA8O,UAAA,SAAAvU,EAAAgF,EAAAwP,GAGA,OAFAA,GAAAjP,KAAAc,SAAAd,KAAAc,QAAArG,KAAAN,MACA6F,KAAAc,UAAAd,KAAAc,QAAA,KAAArG,GAAAgF,GACAO,IACA,EASA4M,EAAA1M,UAAA6U,gBAAA,SAAAta,EAAAgF,EAAAuV,GACAhV,KAAA0S,gBACA1S,KAAA0S,cAAA,IAEA,IAIAuC,EAeAC,EAnBAxC,EAAA1S,KAAA0S,cAuBA,OAtBAsC,GAGAC,EAAAvC,EAAAyC,KAAA,SAAAF,GACA,OAAAnW,OAAAoB,UAAAkV,eAAAza,KAAAsa,EAAAxa,CAAA,CACA,CAAA,IAGA4a,EAAAJ,EAAAxa,GACAI,EAAAya,YAAAD,EAAAL,EAAAvV,CAAA,KAGAwV,EAAA,IACAxa,GAAAI,EAAAya,YAAA,GAAAN,EAAAvV,CAAA,EACAiT,EAAAnV,KAAA0X,CAAA,KAIAC,EAAA,IACAza,GAAAgF,EACAiT,EAAAnV,KAAA2X,CAAA,GAEAlV,IACA,EAQA4M,EAAA1M,UAAA0T,WAAA,SAAA9S,EAAAmO,GACA,GAAAnO,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,CAAA,EAAAjE,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAmD,KAAAgP,UAAAjQ,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAAoS,CAAA,EACA,OAAAjP,IACA,EAMA4M,EAAA1M,UAAAzB,SAAA,WACA,IAAAuO,EAAAhN,KAAA+M,YAAAC,UACA5C,EAAApK,KAAAoK,SACA,OAAAA,EAAAxO,OACAoR,EAAA,IAAA5C,EACA4C,CACA,EAGAJ,EAAAwD,EAAA,SAAAmF,GACA9E,EAAA8E,CACA,C,+BCjPAna,EAAAR,QAAAkW,EAGA,IAAAlE,EAAAtR,EAAA,EAAA,EAGA6S,KAFA2C,EAAA5Q,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAA+D,GAAA9D,UAAA,QAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAYA,SAAAwV,EAAArW,EAAA+a,EAAA1U,EAAAmM,GAQA,GAPAvR,MAAAqY,QAAAyB,CAAA,IACA1U,EAAA0U,EACAA,EAAArb,IAEAyS,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAGA0U,IAAArb,IAAAuB,CAAAA,MAAAqY,QAAAyB,CAAA,EACA,MAAApI,UAAA,6BAAA,EAMApN,KAAAiI,MAAAuN,GAAA,GAOAxV,KAAA0K,YAAA,GAMA1K,KAAAiN,QAAAA,CACA,CAyCA,SAAAwI,EAAAxN,GACA,GAAAA,EAAAkH,OACA,IAAA,IAAAtS,EAAA,EAAAA,EAAAoL,EAAAyC,YAAA9O,OAAA,EAAAiB,EACAoL,EAAAyC,YAAA7N,GAAAsS,QACAlH,EAAAkH,OAAAvB,IAAA3F,EAAAyC,YAAA7N,EAAA,CACA,CA9BAiU,EAAAxD,SAAA,SAAA7S,EAAAqM,GACA,OAAA,IAAAgK,EAAArW,EAAAqM,EAAAmB,MAAAnB,EAAAhG,QAAAgG,EAAAmG,OAAA,CACA,EAOA6D,EAAA5Q,UAAAsN,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,UAAA7K,KAAAc,QACA,QAAAd,KAAAiI,MACA,UAAAyF,EAAA1N,KAAAiN,QAAA9S,GACA,CACA,EAqBA2W,EAAA5Q,UAAA0N,IAAA,SAAA/D,GAGA,GAAAA,aAAAsE,EASA,OANAtE,EAAAsF,QAAAtF,EAAAsF,SAAAnP,KAAAmP,QACAtF,EAAAsF,OAAAjB,OAAArE,CAAA,EACA7J,KAAAiI,MAAA1K,KAAAsM,EAAApP,IAAA,EACAuF,KAAA0K,YAAAnN,KAAAsM,CAAA,EAEA4L,EADA5L,EAAAsB,OAAAnL,IACA,EACAA,KARA,MAAAoN,UAAA,uBAAA,CASA,EAOA0D,EAAA5Q,UAAAgO,OAAA,SAAArE,GAGA,GAAA,EAAAA,aAAAsE,GACA,MAAAf,UAAA,uBAAA,EAEA,IAAAtR,EAAAkE,KAAA0K,YAAAoB,QAAAjC,CAAA,EAGA,GAAA/N,EAAA,EACA,MAAAkC,MAAA6L,EAAA,uBAAA7J,IAAA,EAUA,OARAA,KAAA0K,YAAAnK,OAAAzE,EAAA,CAAA,EAIA,CAAA,GAHAA,EAAAkE,KAAAiI,MAAA6D,QAAAjC,EAAApP,IAAA,IAIAuF,KAAAiI,MAAA1H,OAAAzE,EAAA,CAAA,EAEA+N,EAAAsB,OAAA,KACAnL,IACA,EAKA8Q,EAAA5Q,UAAA2T,MAAA,SAAA1E,GACAvC,EAAA1M,UAAA2T,MAAAlZ,KAAAqF,KAAAmP,CAAA,EAGA,IAFA,IAEAtS,EAAA,EAAAA,EAAAmD,KAAAiI,MAAArM,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAAsF,EAAA3F,IAAAxJ,KAAAiI,MAAApL,EAAA,EACAgN,GAAA,CAAAA,EAAAsB,SACAtB,EAAAsB,OALAnL,MAMA0K,YAAAnN,KAAAsM,CAAA,CAEA,CAEA4L,EAAAzV,IAAA,CACA,EAKA8Q,EAAA5Q,UAAA4T,SAAA,SAAA3E,GACA,IAAA,IAAAtF,EAAAhN,EAAA,EAAAA,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,GACAgN,EAAA7J,KAAA0K,YAAA7N,IAAAsS,QACAtF,EAAAsF,OAAAjB,OAAArE,CAAA,EACA+C,EAAA1M,UAAA4T,SAAAnZ,KAAAqF,KAAAmP,CAAA,CACA,EAkBA2B,EAAAlB,EAAA,WAGA,IAFA,IAAA4F,EAAA9Z,MAAAC,UAAAC,MAAA,EACAE,EAAA,EACAA,EAAAH,UAAAC,QACA4Z,EAAA1Z,GAAAH,UAAAG,CAAA,IACA,OAAA,SAAAoE,EAAAwV,GACA7a,EAAAmV,aAAA9P,EAAA6M,WAAA,EACAa,IAAA,IAAAkD,EAAA4E,EAAAF,CAAA,CAAA,EACA1W,OAAAgQ,eAAA5O,EAAAwV,EAAA,CACAlM,IAAA3O,EAAA8a,YAAAH,CAAA,EACAI,IAAA/a,EAAAgb,YAAAL,CAAA,CACA,CAAA,CACA,CACA,C,4CCzMApa,EAAAR,QAAA+W,IAEA9Q,SAAA,KACA8Q,GAAAxF,SAAA,CAAA2J,SAAA,CAAA,CAAA,EAEA,IAAApE,EAAApW,EAAA,EAAA,EACAmV,EAAAnV,EAAA,EAAA,EACA8S,EAAA9S,EAAA,EAAA,EACA6S,EAAA7S,EAAA,EAAA,EACAyV,EAAAzV,EAAA,EAAA,EACAwV,EAAAxV,EAAA,EAAA,EACAoO,EAAApO,EAAA,EAAA,EACA0V,EAAA1V,EAAA,EAAA,EACA2V,EAAA3V,EAAA,EAAA,EACA4Q,EAAA5Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAEAya,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,GAAA,kCAmCA,SAAA7E,GAAAnT,EAAAgS,EAAA1P,GAEA0P,aAAAC,IACA3P,EAAA0P,EACAA,EAAA,IAAAC,GAKA,IASAgG,EACAC,EACAC,EACAC,EAmuBAC,EAzkBAA,EACAC,EAvKAC,GAFAjW,EADAA,GACA6Q,GAAAxF,UAEA4K,uBAAA,CAAA,EACAC,EAAAtF,EAAAlT,EAAAsC,EAAAmW,sBAAA,CAAA,CAAA,EACAC,EAAAF,EAAAE,KACA3Z,EAAAyZ,EAAAzZ,KACA4Z,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,EAAA,CAAA,EAKAC,EAAA,CAAA,EAEAvD,EAAAxD,EAEAgH,EAAA1W,EAAAgV,SAAA,SAAArb,GAAA,OAAAA,CAAA,EAAAI,EAAA4c,UAGA,SAAAC,EAAAb,EAAApc,EAAAkd,GACA,IAAA9W,EAAA8Q,GAAA9Q,SAGA,OAFA8W,IACAhG,GAAA9Q,SAAA,MACA7C,MAAA,YAAAvD,GAAA,SAAA,KAAAoc,EAAA,OAAAhW,EAAAA,EAAA,KAAA,IAAA,QAAAmW,EAAAY,KAAA,GAAA,CACA,CAEA,SAAAC,IACA,IACAhB,EADApO,EAAA,GAEA,GAEA,GAAA,OAAAoO,EAAAK,EAAA,IAAA,MAAAL,EACA,MAAAa,EAAAb,CAAA,CAAA,OAEApO,EAAAlL,KAAA2Z,EAAA,CAAA,EACAE,EAAAP,CAAA,EAEA,OADAA,EAAAM,EAAA,IACA,MAAAN,GACA,OAAApO,EAAA9K,KAAA,EAAA,CACA,CAEA,SAAAma,EAAAC,GACA,IAAAlB,EAAAK,EAAA,EACA,OAAAL,GACA,IAAA,IACA,IAAA,IAEA,OADAtZ,EAAAsZ,CAAA,EACAgB,EAAA,EACA,IAAA,OAAA,IAAA,OACA,MAAA,CAAA,EACA,IAAA,QAAA,IAAA,QACA,MAAA,CAAA,CACA,CACA,IACAG,IAwCAnB,EAxCAA,EAwCAc,EAxCA,CAAA,EAyCAtV,EAAA,EAKA,OAJA,MAAAwU,EAAA,IAAAA,MACAxU,EAAA,CAAA,EACAwU,EAAAA,EAAAoB,UAAA,CAAA,GAEApB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAxU,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,CACA,CACA,GAAAgT,EAAA9X,KAAA4Y,CAAA,EACA,OAAAxU,EAAA6V,SAAArB,EAAA,EAAA,EACA,GAAAZ,EAAAhY,KAAA4Y,CAAA,EACA,OAAAxU,EAAA6V,SAAArB,EAAA,EAAA,EACA,GAAAV,EAAAlY,KAAA4Y,CAAA,EACA,OAAAxU,EAAA6V,SAAArB,EAAA,CAAA,EAGA,GAAAR,EAAApY,KAAA4Y,CAAA,EACA,OAAAxU,EAAA8V,WAAAtB,CAAA,EAGA,MAAAa,EAAAb,EAAA,SAAAc,CAAA,CAzDA,CARA,MAAArS,GAGA,GAAAyS,GAAAxB,EAAAtY,KAAA4Y,CAAA,EACA,OAAAA,EAGA,MAAAa,EAAAb,EAAA,OAAA,CACA,CACA,CAEA,SAAAuB,EAAAC,EAAAC,GAEA,IADA,IAAAtb,EAEAsb,CAAAA,GAAA,OAAAzB,EAAAM,EAAA,IAAA,MAAAN,EAGAwB,EAAA9a,KAAA,CAAAP,EAAAub,EAAArB,EAAA,CAAA,EAAAE,EAAA,KAAA,CAAA,CAAA,EAAAmB,EAAArB,EAAA,CAAA,EAAAla,EAAA,EAFAqb,EAAA9a,KAAAsa,EAAA,CAAA,EAGAT,EAAA,IAAA,CAAA,CAAA,IACA,IAAAoB,EAAA,CAAA1X,QAAA3G,GACA6U,UAAA,SAAAvU,EAAAgF,GACAO,KAAAc,UAAA3G,KAAA6F,KAAAc,QAAA,IACAd,KAAAc,QAAArG,GAAAgF,CACA,CAJA,EAKAgZ,EACAD,EACA,SAAA3B,GAEA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,CAAA,EAHA6B,EAAAF,EAAA3B,CAAA,EACAO,EAAA,GAAA,CAGA,EACA,WACAuB,EAAAH,CAAA,CACA,CAAA,CACA,CA+BA,SAAAD,EAAA1B,EAAA+B,GACA,OAAA/B,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,CACA,CAGA,GAAA+B,GAAA,MAAA/B,EAAA,IAAAA,IAAA,CAGA,GAAAb,EAAA/X,KAAA4Y,CAAA,EACA,OAAAqB,SAAArB,EAAA,EAAA,EACA,GAAAX,EAAAjY,KAAA4Y,CAAA,EACA,OAAAqB,SAAArB,EAAA,EAAA,EAGA,GAAAT,EAAAnY,KAAA4Y,CAAA,EACA,OAAAqB,SAAArB,EAAA,CAAA,CATA,CAYA,MAAAa,EAAAb,EAAA,IAAA,CACA,CAsDA,SAAAgC,EAAA1J,EAAA0H,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA6B,EAAAvJ,EAAA0H,CAAA,EACAO,EAAA,GAAA,EACA,EAEA,IAAA,UAEA,OADA0B,EAAA3J,CAAA,EACA,EAEA,IAAA,OAEA,OADA4J,EAAA5J,CAAA,EACA,EAEA,IAAA,UACA6J,IAkbAC,EANA9J,EA5aAA,EA4aA0H,EA5aAA,EA+aA,GAAAP,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EA9aA,OAkbAuB,EADAQ,EAAA,IAAAjI,EAAA6F,CAAA,EACA,SAAAA,GACA,GAAAgC,CAAAA,EAAAI,EAAApC,CAAA,EAAA,CAIA,GAAA,QAAAA,EAGA,MAAAa,EAAAb,CAAA,EAFAqC,IAOA/J,EAPA8J,EAUAE,EAAA9B,EAAA,EAEAjQ,EAAAyP,EAGA,GAAA,CAAAP,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,MAAA,EAEA,IACAtE,EAAAC,EACAC,EAFAhY,EAAAoc,EASA,GALAO,EAAA,GAAA,EACAA,EAAA,SAAA,CAAA,CAAA,IACA5E,EAAA,CAAA,GAGA,CAAA+D,EAAAtY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,CAAA,EAQA,GANAtE,EAAAsE,EACAO,EAAA,GAAA,EAAAA,EAAA,SAAA,EAAAA,EAAA,GAAA,EACAA,EAAA,SAAA,CAAA,CAAA,IACA3E,EAAA,CAAA,GAGA,CAAA8D,EAAAtY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,CAAA,EAEAjV,EAAAiV,EACAO,EAAA,GAAA,EAEA,IAAAgC,EAAA,IAAAnI,EAAAxW,EAAA2M,EAAAmL,EAAA3Q,EAAA4Q,EAAAC,CAAA,EACA2G,EAAAnM,QAAAkM,EACAV,EAAAW,EAAA,SAAAvC,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,CAAA,EAHA6B,EAAAU,EAAAvC,CAAA,EACAO,EAAA,GAAA,CAIA,CAAA,EACAjI,EAAAvB,IAAAwL,CAAA,CA1DA,CAOA,CAAA,EACAjK,EAAAvB,IAAAqL,CAAA,EA5bA,EA+aA,MAAAvB,EAAAb,EAAA,cAAA,EA7aA,IAAA,SACAwC,IAofAC,EANAnK,EA9eAA,EA8eA0H,EA9eAA,EAifA,GAAAN,EAAAtY,KAAA4Y,EAAAK,EAAA,CAAA,EAhfA,OAmfAoC,EAAAzC,EACA4B,EAAA,KAAA,SAAA5B,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACA0C,EAAApK,EAAA0H,EAAAyC,CAAA,EACA,MAEA,IAAA,WAGAC,EAAApK,EADAoI,EACA,kBAEA,WAFA+B,CAAA,EAIA,MAEA,QAEA,GAAA,CAAA/B,GAAA,CAAAhB,EAAAtY,KAAA4Y,CAAA,EACA,MAAAa,EAAAb,CAAA,EACAtZ,EAAAsZ,CAAA,EACA0C,EAAApK,EAAA,WAAAmK,CAAA,CAEA,CACA,CAAA,EA7gBA,EAifA,MAAA5B,EAAAb,EAAA,WAAA,CAhfA,CAEA,CAEA,SAAA4B,EAAAzF,EAAAwG,EAAAC,GACA,IAQA5C,EARA6C,EAAA1C,EAAAY,KAOA,GANA5E,IACA,UAAA,OAAAA,EAAA/F,UACA+F,EAAA/F,QAAAoK,EAAA,GAEArE,EAAAnS,SAAA8Q,GAAA9Q,UAEAuW,EAAA,IAAA,CAAA,CAAA,EAAA,CAEA,KAAA,OAAAP,EAAAK,EAAA,IACAsC,EAAA3C,CAAA,EACAO,EAAA,IAAA,CAAA,CAAA,CACA,MACAqC,GACAA,EAAA,EACArC,EAAA,GAAA,EACApE,IAAA,UAAA,OAAAA,EAAA/F,SAAA8J,KACA/D,EAAA/F,QAAAoK,EAAAqC,CAAA,GAAA1G,EAAA/F,QAEA,CAEA,SAAA6L,EAAA3J,EAAA0H,GAGA,GAAA,CAAAP,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,WAAA,EAEA,IAAAzP,EAAA,IAAAgH,EAAAyI,CAAA,EACA4B,EAAArR,EAAA,SAAAyP,GACA,GAAAgC,CAAAA,EAAAzR,EAAAyP,CAAA,EAGA,OAAAA,GAEA,IAAA,MACA8C,IA8JAxK,EA9JA/H,EAgKAS,GADAuP,EAAA,GAAA,EACAF,EAAA,GAGA,GAAAhL,EAAAO,OAAA5E,KAAA1N,GACA,MAAAud,EAAA7P,EAAA,MAAA,EAEAuP,EAAA,GAAA,EACA,IAAAwC,EAAA1C,EAAA,EAGA,GAAA,CAAAX,EAAAtY,KAAA2b,CAAA,EACA,MAAAlC,EAAAkC,EAAA,MAAA,EAEAxC,EAAA,GAAA,EACA,IAAA3c,EAAAyc,EAAA,EAGA,GAAA,CAAAZ,EAAArY,KAAAxD,CAAA,EACA,MAAAid,EAAAjd,EAAA,MAAA,EAEA2c,EAAA,GAAA,EACA,IAAAvN,EAAA,IAAAkH,EAAAyG,EAAA/c,CAAA,EAAA8d,EAAArB,EAAA,CAAA,EAAArP,EAAA+R,CAAA,EACAnB,EAAA5O,EAAA,SAAAgN,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,CAAA,EAHA6B,EAAA7O,EAAAgN,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAuB,EAAA9O,CAAA,CACA,CAAA,EACAsF,EAAAvB,IAAA/D,CAAA,EAjMA,MAEA,IAAA,WACA,IAAA,WACA0P,EAAAnS,EAAAyP,CAAA,EACA,MAEA,IAAA,WAGA0C,EAAAnS,EADAmQ,EACA,kBAEA,UAFA,EAIA,MAEA,IAAA,QAoLApI,EAnLA/H,EAmLAyP,EAnLAA,EAsLA,GAAA,CAAAP,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,MAAA,EAEA,IAAA5O,EAAA,IAAA6I,EAAA0G,EAAAX,CAAA,CAAA,EACA4B,EAAAxQ,EAAA,SAAA4O,GACA,WAAAA,GACA6B,EAAAzQ,EAAA4O,CAAA,EACAO,EAAA,GAAA,IAEA7Z,EAAAsZ,CAAA,EACA0C,EAAAtR,EAAA,UAAA,EAEA,CAAA,EACAkH,EAAAvB,IAAA3F,CAAA,EAlMA,MAEA,IAAA,aACAmQ,EAAAhR,EAAAyS,aAAAzS,EAAAyS,WAAA,GAAA,EACA,MAEA,IAAA,WACAzB,EAAAhR,EAAAiG,WAAAjG,EAAAiG,SAAA,IAAA,CAAA,CAAA,EACA,MAEA,QAEA,GAAA,CAAAkK,GAAA,CAAAhB,EAAAtY,KAAA4Y,CAAA,EACA,MAAAa,EAAAb,CAAA,EAEAtZ,EAAAsZ,CAAA,EACA0C,EAAAnS,EAAA,UAAA,CAEA,CACA,CAAA,EACA+H,EAAAvB,IAAAxG,CAAA,CACA,CAEA,SAAAmS,EAAApK,EAAAvG,EAAA0F,GACA,IAAAlH,EAAA8P,EAAA,EACA,GAAA,UAAA9P,EAAA,CACA0S,IAsEA1S,EAEAyC,EAdAsF,EA1DAA,EA0DAvG,EA1DAA,EA2DAnO,EAAAyc,EAAA,EAGA,GAAAZ,EAAArY,KAAAxD,CAAA,EA7DA,OAgEAyV,EAAArV,EAAAkf,QAAAtf,CAAA,EACAA,IAAAyV,IACAzV,EAAAI,EAAAmf,QAAAvf,CAAA,GACA2c,EAAA,GAAA,EACA/P,EAAAkR,EAAArB,EAAA,CAAA,GACA9P,EAAA,IAAAgH,EAAA3T,CAAA,GACAuR,MAAA,CAAA,GAEAnC,EADA,IAAAsE,EAAA+B,EAAA7I,EAAA5M,EAAAmO,CAAA,GACA/H,SAAA8Q,GAAA9Q,SACA4X,EAAArR,EAAA,SAAAyP,GACA,OAAAA,GAEA,IAAA,SACA6B,EAAAtR,EAAAyP,CAAA,EACAO,EAAA,GAAA,EACA,MAEA,IAAA,WACA,IAAA,WACAmC,EAAAnS,EAAAyP,CAAA,EACA,MAEA,IAAA,WAGA0C,EAAAnS,EADAmQ,EACA,kBAEA,UAFA,EAIA,MAEA,IAAA,UACAuB,EAAA1R,CAAA,EACA,MAEA,IAAA,OACA2R,EAAA3R,CAAA,EACA,MAGA,QACA,MAAAsQ,EAAAb,CAAA,CACA,CACA,CAAA,EAnCAhN,KAoCAsF,EAAAvB,IAAAxG,CAAA,EACAwG,IAAA/D,CAAA,EA/CA,MAAA6N,EAAAjd,EAAA,MAAA,CA7DA,CAQA,KAAA2M,EAAA6S,SAAA,GAAA,GAAA9C,EAAA,EAAA+C,WAAA,GAAA,GACA9S,GAAA8P,EAAA,EAIA,GAAA,CAAAX,EAAAtY,KAAAmJ,CAAA,EACA,MAAAsQ,EAAAtQ,EAAA,MAAA,EAEA,IAAA3M,EAAAyc,EAAA,EAGA,GAAA,CAAAZ,EAAArY,KAAAxD,CAAA,EACA,MAAAid,EAAAjd,EAAA,MAAA,EAEAA,EAAA+c,EAAA/c,CAAA,EACA2c,EAAA,GAAA,EAEA,IAAAvN,EAAA,IAAAsE,EAAA1T,EAAA8d,EAAArB,EAAA,CAAA,EAAA9P,EAAAwB,EAAA0F,CAAA,EACAmK,EAAA5O,EAAA,SAAAgN,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,CAAA,EAHA6B,EAAA7O,EAAAgN,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAuB,EAAA9O,CAAA,CACA,CAAA,EAEA,oBAAAjB,GAEAX,EAAA,IAAA6I,EAAA,IAAArW,CAAA,EACAoP,EAAAmF,UAAA,kBAAA,CAAA,CAAA,EACA/G,EAAA2F,IAAA/D,CAAA,EACAsF,EAAAvB,IAAA3F,CAAA,GAEAkH,EAAAvB,IAAA/D,CAAA,EAMA0N,GAAA1N,CAAAA,EAAAM,UAAA+B,EAAAG,OAAAjF,KAAAjN,IAAA+R,EAAAE,MAAAhF,KAAAjN,IACA0P,EAAAmF,UAAA,SAAA,CAAA,EAAA,CAAA,CAAA,CACA,CAmHA,SAAA+J,EAAA5J,EAAA0H,GAGA,GAAA,CAAAP,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,MAAA,EAEA,IAAAtJ,EAAA,IAAA7D,EAAAmN,CAAA,EACA4B,EAAAlL,EAAA,SAAAsJ,GACA,OAAAA,GACA,IAAA,SACA6B,EAAAnL,EAAAsJ,CAAA,EACAO,EAAA,GAAA,EACA,MAEA,IAAA,WACAgB,EAAA7K,EAAAF,WAAAE,EAAAF,SAAA,IAAA,CAAA,CAAA,EACA,MAEA,QACA8M,IAMAhL,EANA5B,EAMAsJ,EANAA,EASA,GAAA,CAAAP,EAAArY,KAAA4Y,CAAA,EACA,MAAAa,EAAAb,EAAA,MAAA,EAEAO,EAAA,GAAA,EACA,IAAA3X,EAAA8Y,EAAArB,EAAA,EAAA,CAAA,CAAA,EACAsB,EAAA,CACA1X,QAAA3G,GAEA6U,UAAA,SAAAvU,EAAAgF,GACAO,KAAAc,UAAA3G,KACA6F,KAAAc,QAAA,IACAd,KAAAc,QAAArG,GAAAgF,CACA,CALA,EAhBA0a,OAsBA1B,EAAAD,EAAA,SAAA3B,GAGA,GAAA,WAAAA,EAIA,MAAAa,EAAAb,CAAA,EAHA6B,EAAAF,EAAA3B,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAuB,EAAAH,CAAA,CACA,CAAA,EAXAC,KAYAtJ,EAAAvB,IAAAiJ,EAAApX,EAAA+Y,EAAAvL,QAAAuL,EAAA1X,OAAA,CAjCA,CACA,CAAA,EACAqO,EAAAvB,IAAAL,CAAA,CACA,CAiCA,SAAAmL,EAAAvJ,EAAA0H,GACA,IAAAuD,EAAAhD,EAAA,IAAA,CAAA,CAAA,EAGA,GAAA,CAAAb,EAAAtY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,MAAA,EAEA,IAEA7B,EAFAva,EAAAoc,EACAwD,EAAA5f,EAeA6f,GAZAF,IACAhD,EAAA,GAAA,EAEAiD,EADA5f,EAAA,IAAAA,EAAA,IAEAoc,EAAAM,EAAA,EACAX,GAAAvY,KAAA4Y,CAAA,IACA7B,EAAA6B,EAAAnZ,MAAA,CAAA,EACAjD,GAAAoc,EACAK,EAAA,IAGAE,EAAA,GAAA,EAKA,SAAAmD,EAAApL,EAAA1U,GAEA,GAAA2c,EAAA,IAAA,CAAA,CAAA,EAAA,CAGA,IAFA,IAAAoD,EAAA,GAEA,CAAApD,EAAA,IAAA,CAAA,CAAA,GAAA,CAEA,GAAA,CAAAd,EAAArY,KAAA4Y,EAAAK,EAAA,CAAA,EACA,MAAAQ,EAAAb,EAAA,MAAA,EAEA,GAAA,OAAAA,EACA,MAAAa,EAAAb,EAAA,cAAA,EAGA,IAAApX,EAYAgb,EAXAzF,EAAA6B,EAIA,GAFAO,EAAA,IAAA,CAAA,CAAA,EAEA,MAAAD,EAAA,EACA1X,EAAA8a,EAAApL,EAAA1U,EAAA,IAAAoc,CAAA,OACA,GAAA,MAAAM,EAAA,GAMA,GAFA1X,EAAA,GAEA2X,EAAA,IAAA,CAAA,CAAA,EAAA,CACA,KACAqD,EAAA3C,EAAA,CAAA,CAAA,EACArY,EAAAlC,KAAAkd,CAAA,EACArD,EAAA,IAAA,CAAA,CAAA,IACAA,EAAA,GAAA,EACA,KAAA,IAAAqD,GACAzL,EAAAG,EAAA1U,EAAA,IAAAoc,EAAA4D,CAAA,CAEA,CAAA,MAEAhb,EAAAqY,EAAA,CAAA,CAAA,EACA9I,EAAAG,EAAA1U,EAAA,IAAAoc,EAAApX,CAAA,EAGA,IAAAib,EAAAF,EAAAxF,GAEA0F,IACAjb,EAAA,GAAAkb,OAAAD,CAAA,EAAAC,OAAAlb,CAAA,GAEA+a,EAAAxF,GAAAvV,EAGA2X,EAAA,IAAA,CAAA,CAAA,EACAA,EAAA,IAAA,CAAA,CAAA,CACA,CAEA,OAAAoD,CACA,CAEA,IAAAI,EAAA9C,EAAA,CAAA,CAAA,EACA9I,EAAAG,EAAA1U,EAAAmgB,CAAA,EACA,OAAAA,CAEA,EAjEAzL,EAAA1U,CAAA,GAwEAA,EAvEA4f,EAuEA5a,EAvEA6a,EAuEAtF,EAvEAA,GAuEA7F,EAvEAA,GAwEA4F,iBACA5F,EAAA4F,gBAAAta,EAAAgF,EAAAuV,CAAA,CAxEA,CAiEA,SAAAhG,EAAAG,EAAA1U,EAAAgF,GACA0P,EAAAH,WACAG,EAAAH,UAAAvU,EAAAgF,CAAA,CACA,CAOA,SAAAkZ,EAAAxJ,GACA,GAAAiI,EAAA,IAAA,CAAA,CAAA,EAAA,CACA,KACAsB,EAAAvJ,EAAA,QAAA,EACAiI,EAAA,IAAA,CAAA,CAAA,IACAA,EAAA,GAAA,CACA,CAEA,CA4GA,KAAA,QAAAP,EAAAK,EAAA,IACA,OAAAL,GAEA,IAAA,UAGA,GAAA,CAAAS,EACA,MAAAI,EAAAb,CAAA,EA/lBA,GAAAJ,IAAAtc,GACA,MAAAud,EAAA,SAAA,EAKA,GAHAjB,EAAAS,EAAA,EAGA,CAAAX,EAAAtY,KAAAwY,CAAA,EACA,MAAAiB,EAAAjB,EAAA,MAAA,EAEAzC,EAAAA,EAAAjZ,OAAA0b,CAAA,EACAW,EAAA,GAAA,EAwlBA,MAEA,IAAA,SAGA,GAAA,CAAAE,EACA,MAAAI,EAAAb,CAAA,EAxlBA,OADAC,EADAD,EAAAA,KAAAA,EAAAM,EAAA,GAGA,IAAA,OACAL,EAAAH,EAAAA,GAAA,GACAO,EAAA,EACA,MACA,IAAA,SACAA,EAAA,EAEA,QACAJ,EAAAJ,EAAAA,GAAA,EAEA,CACAG,EAAAgB,EAAA,EACAT,EAAA,GAAA,EACAN,EAAAvZ,KAAAsZ,CAAA,EA6kBA,MAEA,IAAA,SAGA,GAAA,CAAAS,EACA,MAAAI,EAAAb,CAAA,EA1kBA,GALAO,EAAA,GAAA,EACAR,EAAAiB,EAAA,EAIA,EAHAN,EAAA,WAAAX,IAGA,WAAAA,EACA,MAAAc,EAAAd,EAAA,QAAA,EAIApG,EAAAxB,UAAA,SAAA4H,CAAA,EAEAQ,EAAA,GAAA,EAskBA,MAEA,IAAA,SAEAsB,EAAA1E,EAAA6C,CAAA,EACAO,EAAA,GAAA,EACA,MAEA,QAGA,GAAAyB,EAAA7E,EAAA6C,CAAA,EAAA,CACAS,EAAA,CAAA,EACA,QACA,CAGA,MAAAI,EAAAb,CAAA,CACA,CAIA,OADAlF,GAAA9Q,SAAA,KACA,CACAga,QAAApE,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACApG,KAAAA,CACA,CACA,C,2FC/2BApV,EAAAR,QAAA0W,EAEA,IAEAC,EAFA1W,EAAAS,EAAA,EAAA,EAIAwf,EAAAjgB,EAAAigB,SACAxU,EAAAzL,EAAAyL,KAGA,SAAAyU,EAAA5I,EAAA6I,GACA,OAAAC,WAAA,uBAAA9I,EAAA/P,IAAA,OAAA4Y,GAAA,GAAA,MAAA7I,EAAA5L,GAAA,CACA,CAQA,SAAA+K,EAAAvU,GAMAiD,KAAAmC,IAAApF,EAMAiD,KAAAoC,IAAA,EAMApC,KAAAuG,IAAAxJ,EAAAnB,MACA,CAeA,SAAAkR,IACA,OAAAjS,EAAAqgB,OACA,SAAAne,GACA,OAAAuU,EAAAxE,OAAA,SAAA/P,GACA,OAAAlC,EAAAqgB,OAAAC,SAAApe,CAAA,EACA,IAAAwU,EAAAxU,CAAA,EAEAqe,EAAAre,CAAA,CACA,GAAAA,CAAA,CACA,EAEAqe,CACA,CAzBA,IA4CA3b,EA5CA2b,EAAA,aAAA,OAAA1Z,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAhG,MAAAqY,QAAAhX,CAAA,EACA,OAAA,IAAAuU,EAAAvU,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAEA,SAAAjB,GACA,GAAArB,MAAAqY,QAAAhX,CAAA,EACA,OAAA,IAAAuU,EAAAvU,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAqEA,SAAAqd,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,CAAA,EACAje,EAAA,EACA,GAAAmD,EAAA,EAAAA,KAAAuG,IAAAvG,KAAAoC,KAaA,CACA,KAAAvF,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAmD,KAAAoC,KAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,IAAA,EAGA,GADAsb,EAAAzX,IAAAyX,EAAAzX,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAkZ,CACA,CAGA,OADAA,EAAAzX,IAAAyX,EAAAzX,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,GAAA,MAAA,EAAAvF,KAAA,EACAye,CACA,CAzBA,KAAAze,EAAA,EAAA,EAAAA,EAGA,GADAye,EAAAzX,IAAAyX,EAAAzX,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAkZ,EAKA,GAFAA,EAAAzX,IAAAyX,EAAAzX,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EACAkZ,EAAAxX,IAAAwX,EAAAxX,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,KAAA,EACApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAkZ,EAgBA,GAfAze,EAAA,EAeA,EAAAmD,KAAAuG,IAAAvG,KAAAoC,KACA,KAAAvF,EAAA,EAAA,EAAAA,EAGA,GADAye,EAAAxX,IAAAwX,EAAAxX,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,EAAA,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAkZ,CACA,MAEA,KAAAze,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAmD,KAAAoC,KAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,IAAA,EAGA,GADAsb,EAAAxX,IAAAwX,EAAAxX,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,EAAA,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAkZ,CACA,CAGA,MAAAtd,MAAA,yBAAA,CACA,CAiCA,SAAAud,EAAApZ,EAAAlF,GACA,OAAAkF,EAAAlF,EAAA,GACAkF,EAAAlF,EAAA,IAAA,EACAkF,EAAAlF,EAAA,IAAA,GACAkF,EAAAlF,EAAA,IAAA,MAAA,CACA,CA8BA,SAAAue,IAGA,GAAAxb,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,KAAA,CAAA,EAEA,OAAA,IAAA8a,EAAAS,EAAAvb,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,EAAAmZ,EAAAvb,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CAAA,CACA,CA5KAkP,EAAAxE,OAAAA,EAAA,EAEAwE,EAAApR,UAAAub,EAAA5gB,EAAAa,MAAAwE,UAAAwb,UAAA7gB,EAAAa,MAAAwE,UAAAxC,MAOA4T,EAAApR,UAAAyb,QACAlc,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,QAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,KAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,GAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,KAGA,GAAApC,KAAAoC,KAAA,GAAApC,KAAAuG,SAIA,OAAA9G,EAFA,MADAO,KAAAoC,IAAApC,KAAAuG,IACAwU,EAAA/a,KAAA,EAAA,CAGA,GAOAsR,EAAApR,UAAA0b,MAAA,WACA,OAAA,EAAA5b,KAAA2b,OAAA,CACA,EAMArK,EAAApR,UAAA2b,OAAA,WACA,IAAApc,EAAAO,KAAA2b,OAAA,EACA,OAAAlc,IAAA,EAAA,EAAA,EAAAA,GAAA,CACA,EAoFA6R,EAAApR,UAAA4b,KAAA,WACA,OAAA,IAAA9b,KAAA2b,OAAA,CACA,EAaArK,EAAApR,UAAA6b,QAAA,WAGA,GAAA/b,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,KAAA,CAAA,EAEA,OAAAub,EAAAvb,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CACA,EAMAkP,EAAApR,UAAA8b,SAAA,WAGA,GAAAhc,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,KAAA,CAAA,EAEA,OAAA,EAAAub,EAAAvb,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CACA,EAkCAkP,EAAApR,UAAA+b,MAAA,WAGA,GAAAjc,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,KAAA,CAAA,EAEA,IAAAP,EAAA5E,EAAAohB,MAAA3X,YAAAtE,KAAAmC,IAAAnC,KAAAoC,GAAA,EAEA,OADApC,KAAAoC,KAAA,EACA3C,CACA,EAOA6R,EAAApR,UAAAgc,OAAA,WAGA,GAAAlc,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,KAAA,CAAA,EAEA,IAAAP,EAAA5E,EAAAohB,MAAAjX,aAAAhF,KAAAmC,IAAAnC,KAAAoC,GAAA,EAEA,OADApC,KAAAoC,KAAA,EACA3C,CACA,EAMA6R,EAAApR,UAAAyL,MAAA,WACA,IAAA/P,EAAAoE,KAAA2b,OAAA,EACA3e,EAAAgD,KAAAoC,IACAnF,EAAA+C,KAAAoC,IAAAxG,EAGA,GAAAqB,EAAA+C,KAAAuG,IACA,MAAAwU,EAAA/a,KAAApE,CAAA,EAGA,OADAoE,KAAAoC,KAAAxG,EACAF,MAAAqY,QAAA/T,KAAAmC,GAAA,EACAnC,KAAAmC,IAAAzE,MAAAV,EAAAC,CAAA,EAEAD,IAAAC,GACAkf,EAAAthB,EAAAqgB,QAEAiB,EAAAlW,MAAA,CAAA,EACA,IAAAjG,KAAAmC,IAAA4K,YAAA,CAAA,EAEA/M,KAAAyb,EAAA9gB,KAAAqF,KAAAmC,IAAAnF,EAAAC,CAAA,CACA,EAMAqU,EAAApR,UAAA5D,OAAA,WACA,IAAAqP,EAAA3L,KAAA2L,MAAA,EACA,OAAArF,EAAAE,KAAAmF,EAAA,EAAAA,EAAA/P,MAAA,CACA,EAOA0V,EAAApR,UAAAkX,KAAA,SAAAxb,GACA,GAAA,UAAA,OAAAA,EAAA,CAEA,GAAAoE,KAAAoC,IAAAxG,EAAAoE,KAAAuG,IACA,MAAAwU,EAAA/a,KAAApE,CAAA,EACAoE,KAAAoC,KAAAxG,CACA,MACA,GAEA,GAAAoE,KAAAoC,KAAApC,KAAAuG,IACA,MAAAwU,EAAA/a,IAAA,CAAA,OACA,IAAAA,KAAAmC,IAAAnC,KAAAoC,GAAA,KAEA,OAAApC,IACA,EAOAsR,EAAApR,UAAAkc,SAAA,SAAA5P,GACA,OAAAA,GACA,KAAA,EACAxM,KAAAoX,KAAA,EACA,MACA,KAAA,EACApX,KAAAoX,KAAA,CAAA,EACA,MACA,KAAA,EACApX,KAAAoX,KAAApX,KAAA2b,OAAA,CAAA,EACA,MACA,KAAA,EACA,KAAA,IAAAnP,EAAA,EAAAxM,KAAA2b,OAAA,IACA3b,KAAAoc,SAAA5P,CAAA,EAEA,MACA,KAAA,EACAxM,KAAAoX,KAAA,CAAA,EACA,MAGA,QACA,MAAApZ,MAAA,qBAAAwO,EAAA,cAAAxM,KAAAoC,GAAA,CACA,CACA,OAAApC,IACA,EAEAsR,EAAAlB,EAAA,SAAAiM,GACA9K,EAAA8K,EACA/K,EAAAxE,OAAAA,EAAA,EACAyE,EAAAnB,EAAA,EAEA,IAAA7U,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAAyhB,MAAAhL,EAAApR,UAAA,CAEAqc,MAAA,WACA,OAAAlB,EAAA1gB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEAihB,OAAA,WACA,OAAAnB,EAAA1gB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEAkhB,OAAA,WACA,OAAApB,EAAA1gB,KAAAqF,IAAA,EAAA0c,SAAA,EAAAnhB,GAAA,CAAA,CAAA,CACA,EAEAohB,QAAA,WACA,OAAAnB,EAAA7gB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEAqhB,SAAA,WACA,OAAApB,EAAA7gB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,CAEA,CAAA,CACA,C,+BC9ZAH,EAAAR,QAAA2W,EAGA,IAAAD,EAAAhW,EAAA,EAAA,EAGAT,IAFA0W,EAAArR,UAAApB,OAAAgO,OAAAwE,EAAApR,SAAA,GAAA6M,YAAAwE,EAEAjW,EAAA,EAAA,GASA,SAAAiW,EAAAxU,GACAuU,EAAA3W,KAAAqF,KAAAjD,CAAA,CAOA,CAEAwU,EAAAnB,EAAA,WAEAvV,EAAAqgB,SACA3J,EAAArR,UAAAub,EAAA5gB,EAAAqgB,OAAAhb,UAAAxC,MACA,EAMA6T,EAAArR,UAAA5D,OAAA,WACA,IAAAiK,EAAAvG,KAAA2b,OAAA,EACA,OAAA3b,KAAAmC,IAAA0a,UACA7c,KAAAmC,IAAA0a,UAAA7c,KAAAoC,IAAApC,KAAAoC,IAAA3F,KAAAqgB,IAAA9c,KAAAoC,IAAAmE,EAAAvG,KAAAuG,GAAA,CAAA,EACAvG,KAAAmC,IAAA1D,SAAA,QAAAuB,KAAAoC,IAAApC,KAAAoC,IAAA3F,KAAAqgB,IAAA9c,KAAAoC,IAAAmE,EAAAvG,KAAAuG,GAAA,CAAA,CACA,EASAgL,EAAAnB,EAAA,C,qCCjDAhV,EAAAR,QAAA6V,EAGA,IAQArC,EACAuD,EACA/K,EAVAiG,EAAAvR,EAAA,EAAA,EAGA6S,KAFAsC,EAAAvQ,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAA0D,GAAAzD,UAAA,OAEA1R,EAAA,EAAA,GACAoO,EAAApO,EAAA,EAAA,EACAwV,EAAAxV,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAaA,SAAAmV,EAAA3P,GACA+L,EAAAlS,KAAAqF,KAAA,GAAAc,CAAA,EAMAd,KAAA+c,SAAA,GAMA/c,KAAAgd,MAAA,EACA,CAsCA,SAAAC,KA9BAxM,EAAAnD,SAAA,SAAAxG,EAAA0J,GAKA,OAHAA,EADAA,GACA,IAAAC,EACA3J,EAAAhG,SACA0P,EAAAoD,WAAA9M,EAAAhG,OAAA,EACA0P,EAAA4C,QAAAtM,EAAAC,MAAA,CACA,EAUA0J,EAAAvQ,UAAAgd,YAAAriB,EAAA2K,KAAAvJ,QAUAwU,EAAAvQ,UAAAQ,MAAA7F,EAAA6F,MAaA+P,EAAAvQ,UAAAqQ,KAAA,SAAAA,EAAA1P,EAAAC,EAAAC,GACA,YAAA,OAAAD,IACAC,EAAAD,EACAA,EAAA3G,IAEA,IAAAgjB,EAAAnd,KACA,GAAA,CAAAe,EACA,OAAAlG,EAAA8F,UAAA4P,EAAA4M,EAAAtc,EAAAC,CAAA,EAEA,IAAAsc,EAAArc,IAAAkc,EAGA,SAAAI,EAAAlhB,EAAAqU,GAEA,GAAAzP,EAAA,CAEA,GAAAqc,EACA,MAAAjhB,EACA,IAAAmhB,EAAAvc,EACAA,EAAA,KACAuc,EAAAnhB,EAAAqU,CAAA,CALA,CAMA,CAGA,SAAA+M,EAAA1c,GACA,IAAA2c,EAAA3c,EAAA4c,YAAA,kBAAA,EACA,GAAA,CAAA,EAAAD,EAAA,CACAE,EAAA7c,EAAAoX,UAAAuF,CAAA,EACA,GAAAE,KAAA9W,EAAA,OAAA8W,CACA,CACA,OAAA,IACA,CAGA,SAAAC,EAAA9c,EAAArC,GACA,IAGA,GAFA3D,EAAAgT,SAAArP,CAAA,GAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAA+R,MAAAnT,CAAA,GACA3D,EAAAgT,SAAArP,CAAA,EAEA,CACAmT,EAAA9Q,SAAAA,EACA,IACAqO,EADA0O,EAAAjM,EAAAnT,EAAA2e,EAAArc,CAAA,EAEAjE,EAAA,EACA,GAAA+gB,EAAAlH,QACA,KAAA7Z,EAAA+gB,EAAAlH,QAAA9a,OAAA,EAAAiB,GACAqS,EAAAqO,EAAAK,EAAAlH,QAAA7Z,EAAA,GAAAsgB,EAAAD,YAAArc,EAAA+c,EAAAlH,QAAA7Z,EAAA,IACA6D,EAAAwO,CAAA,EACA,GAAA0O,EAAAjH,YACA,IAAA9Z,EAAA,EAAAA,EAAA+gB,EAAAjH,YAAA/a,OAAA,EAAAiB,GACAqS,EAAAqO,EAAAK,EAAAjH,YAAA9Z,EAAA,GAAAsgB,EAAAD,YAAArc,EAAA+c,EAAAjH,YAAA9Z,EAAA,IACA6D,EAAAwO,EAAA,CAAA,CAAA,CACA,MAdAiO,EAAAvJ,WAAApV,EAAAsC,OAAA,EAAAsS,QAAA5U,EAAAuI,MAAA,CAiBA,CAFA,MAAA5K,GACAkhB,EAAAlhB,CAAA,CACA,CACAihB,GAAAS,GACAR,EAAA,KAAAF,CAAA,CACA,CAGA,SAAAzc,EAAAG,EAAAid,GAIA,GAHAjd,EAAA0c,EAAA1c,CAAA,GAAAA,EAGAsc,CAAAA,CAAAA,EAAAH,MAAAlR,QAAAjL,CAAA,EAKA,GAHAsc,EAAAH,MAAAzf,KAAAsD,CAAA,EAGAA,KAAA+F,EACAwW,EACAO,EAAA9c,EAAA+F,EAAA/F,EAAA,GAEA,EAAAgd,EACAE,WAAA,WACA,EAAAF,EACAF,EAAA9c,EAAA+F,EAAA/F,EAAA,CACA,CAAA,QAMA,GAAAuc,EAAA,CACA,IAAA5e,EACA,IACAA,EAAA3D,EAAA+F,GAAAod,aAAAnd,CAAA,EAAApC,SAAA,MAAA,CAKA,CAJA,MAAAtC,GAGA,OAFA,KAAA2hB,GACAT,EAAAlhB,CAAA,EAEA,CACAwhB,EAAA9c,EAAArC,CAAA,CACA,KACA,EAAAqf,EACAV,EAAAzc,MAAAG,EAAA,SAAA1E,EAAAqC,GACA,EAAAqf,EAEA9c,IAEA5E,EAEA2hB,EAEAD,GACAR,EAAA,KAAAF,CAAA,EAFAE,EAAAlhB,CAAA,EAKAwhB,EAAA9c,EAAArC,CAAA,EACA,CAAA,CAEA,CACA,IAAAqf,EAAA,EAIAhjB,EAAAgT,SAAAhN,CAAA,IACAA,EAAA,CAAAA,IACA,IAAA,IAAAqO,EAAArS,EAAA,EAAAA,EAAAgE,EAAAjF,OAAA,EAAAiB,GACAqS,EAAAiO,EAAAD,YAAA,GAAArc,EAAAhE,EAAA,IACA6D,EAAAwO,CAAA,EAEA,OAAAkO,EACAD,GACAU,GACAR,EAAA,KAAAF,CAAA,EACAhjB,GACA,EA+BAsW,EAAAvQ,UAAAwQ,SAAA,SAAA7P,EAAAC,GACA,GAAAjG,EAAAojB,OAEA,OAAAje,KAAAuQ,KAAA1P,EAAAC,EAAAmc,CAAA,EADA,MAAAjf,MAAA,eAAA,CAEA,EAKAyS,EAAAvQ,UAAAgU,WAAA,WACA,GAAAlU,KAAA+c,SAAAnhB,OACA,MAAAoC,MAAA,4BAAAgC,KAAA+c,SAAAnS,IAAA,SAAAf,GACA,MAAA,WAAAA,EAAAyE,OAAA,QAAAzE,EAAAsF,OAAA/E,QACA,CAAA,EAAAzM,KAAA,IAAA,CAAA,EACA,OAAAkP,EAAA3M,UAAAgU,WAAAvZ,KAAAqF,IAAA,CACA,EAGA,IAAAke,EAAA,SAUA,SAAAC,EAAA3N,EAAA3G,GACA,IAEAuU,EAFAC,EAAAxU,EAAAsF,OAAAgF,OAAAtK,EAAAyE,MAAA,EACA,GAAA+P,EASA,OARAD,EAAA,IAAAjQ,EAAAtE,EAAAO,SAAAP,EAAAxC,GAAAwC,EAAAzC,KAAAyC,EAAAjB,KAAAzO,GAAA0P,EAAA/I,OAAA,EAEAud,EAAA7U,IAAA4U,EAAA3jB,IAAA,KAGA2jB,EAAAxP,eAAA/E,GACA8E,eAAAyP,EACAC,EAAAzQ,IAAAwQ,CAAA,GACA,CAGA,CAQA3N,EAAAvQ,UAAA2U,EAAA,SAAAvC,GACA,GAAAA,aAAAnE,EAEAmE,EAAAhE,SAAAnU,IAAAmY,EAAA3D,gBACAwP,EAAAne,EAAAsS,CAAA,GACAtS,KAAA+c,SAAAxf,KAAA+U,CAAA,OAEA,GAAAA,aAAA5I,EAEAwU,EAAAjgB,KAAAqU,EAAA7X,IAAA,IACA6X,EAAAnD,OAAAmD,EAAA7X,MAAA6X,EAAA7J,aAEA,GAAA,EAAA6J,aAAAxB,GAAA,CAEA,GAAAwB,aAAAlE,EACA,IAAA,IAAAvR,EAAA,EAAAA,EAAAmD,KAAA+c,SAAAnhB,QACAuiB,EAAAne,EAAAA,KAAA+c,SAAAlgB,EAAA,EACAmD,KAAA+c,SAAAxc,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAiV,EAAAgB,YAAA1X,OAAA,EAAAyB,EACA2C,KAAA6U,EAAAvC,EAAAW,EAAA5V,EAAA,EACA6gB,EAAAjgB,KAAAqU,EAAA7X,IAAA,IACA6X,EAAAnD,OAAAmD,EAAA7X,MAAA6X,EACA,CAKA,EAQA7B,EAAAvQ,UAAA4U,EAAA,SAAAxC,GAGA,IAKAxW,EAPA,GAAAwW,aAAAnE,EAEAmE,EAAAhE,SAAAnU,KACAmY,EAAA3D,gBACA2D,EAAA3D,eAAAQ,OAAAjB,OAAAoE,EAAA3D,cAAA,EACA2D,EAAA3D,eAAA,MAIA,CAAA,GAFA7S,EAAAkE,KAAA+c,SAAAjR,QAAAwG,CAAA,IAGAtS,KAAA+c,SAAAxc,OAAAzE,EAAA,CAAA,QAIA,GAAAwW,aAAA5I,EAEAwU,EAAAjgB,KAAAqU,EAAA7X,IAAA,GACA,OAAA6X,EAAAnD,OAAAmD,EAAA7X,WAEA,GAAA6X,aAAAzF,EAAA,CAEA,IAAA,IAAAhQ,EAAA,EAAAA,EAAAyV,EAAAgB,YAAA1X,OAAA,EAAAiB,EACAmD,KAAA8U,EAAAxC,EAAAW,EAAApW,EAAA,EAEAqhB,EAAAjgB,KAAAqU,EAAA7X,IAAA,GACA,OAAA6X,EAAAnD,OAAAmD,EAAA7X,KAEA,CACA,EAGAgW,EAAAL,EAAA,SAAAC,EAAAiO,EAAAC,GACAnQ,EAAAiC,EACAsB,EAAA2M,EACA1X,EAAA2X,CACA,C,uDC9WAnjB,EAAAR,QAAA,E,0BCKAA,EA6BAoW,QAAA1V,EAAA,EAAA,C,+BClCAF,EAAAR,QAAAoW,EAEA,IAAAnW,EAAAS,EAAA,EAAA,EAsCA,SAAA0V,EAAAwN,EAAAC,EAAAC,GAEA,GAAA,YAAA,OAAAF,EACA,MAAApR,UAAA,4BAAA,EAEAvS,EAAAkF,aAAApF,KAAAqF,IAAA,EAMAA,KAAAwe,QAAAA,EAMAxe,KAAAye,iBAAA9Q,CAAAA,CAAA8Q,EAMAze,KAAA0e,kBAAA/Q,CAAAA,CAAA+Q,CACA,GA3DA1N,EAAA9Q,UAAApB,OAAAgO,OAAAjS,EAAAkF,aAAAG,SAAA,GAAA6M,YAAAiE,GAwEA9Q,UAAAye,QAAA,SAAAA,EAAAvF,EAAAwF,EAAAC,EAAAC,EAAA/d,GAEA,GAAA,CAAA+d,EACA,MAAA1R,UAAA,2BAAA,EAEA,IAAA+P,EAAAnd,KACA,GAAA,CAAAe,EACA,OAAAlG,EAAA8F,UAAAge,EAAAxB,EAAA/D,EAAAwF,EAAAC,EAAAC,CAAA,EAEA,GAAA,CAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAAhd,EAAA/C,MAAA,eAAA,CAAA,CAAA,EAAA,CAAA,EACA7D,GAGA,IACA,OAAAgjB,EAAAqB,QACApF,EACAwF,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,CAAA,EAAAzB,OAAA,EACA,SAAAlhB,EAAAqF,GAEA,GAAArF,EAEA,OADAghB,EAAA3c,KAAA,QAAArE,EAAAid,CAAA,EACArY,EAAA5E,CAAA,EAGA,GAAA,OAAAqF,EAEA,OADA2b,EAAAlgB,IAAA,CAAA,CAAA,EACA9C,GAGA,GAAA,EAAAqH,aAAAqd,GACA,IACArd,EAAAqd,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAAld,CAAA,CAIA,CAHA,MAAArF,GAEA,OADAghB,EAAA3c,KAAA,QAAArE,EAAAid,CAAA,EACArY,EAAA5E,CAAA,CACA,CAIA,OADAghB,EAAA3c,KAAA,OAAAgB,EAAA4X,CAAA,EACArY,EAAA,KAAAS,CAAA,CACA,CACA,CAKA,CAJA,MAAArF,GAGA,OAFAghB,EAAA3c,KAAA,QAAArE,EAAAid,CAAA,EACA2E,WAAA,WAAAhd,EAAA5E,CAAA,CAAA,EAAA,CAAA,EACAhC,EACA,CACA,EAOA6W,EAAA9Q,UAAAjD,IAAA,SAAA8hB,GAOA,OANA/e,KAAAwe,UACAO,GACA/e,KAAAwe,QAAA,KAAA,KAAA,IAAA,EACAxe,KAAAwe,QAAA,KACAxe,KAAAQ,KAAA,KAAA,EAAAH,IAAA,GAEAL,IACA,C,+BC5IA5E,EAAAR,QAAAoW,EAGA,IAAAnE,EAAAvR,EAAA,EAAA,EAGA2V,KAFAD,EAAA9Q,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAAiE,GAAAhE,UAAA,UAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EACAkW,EAAAlW,EAAA,EAAA,EAWA,SAAA0V,EAAAvW,EAAAqG,GACA+L,EAAAlS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAAyT,QAAA,GAOAzT,KAAAgf,EAAA,IACA,CAwDA,SAAA9L,EAAA+F,GAEA,OADAA,EAAA+F,EAAA,KACA/F,CACA,CA3CAjI,EAAA1D,SAAA,SAAA7S,EAAAqM,GACA,IAAAmS,EAAA,IAAAjI,EAAAvW,EAAAqM,EAAAhG,OAAA,EAEA,GAAAgG,EAAA2M,QACA,IAAA,IAAAD,EAAA1U,OAAAC,KAAA+H,EAAA2M,OAAA,EAAA5W,EAAA,EAAAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EACAoc,EAAArL,IAAAqD,EAAA3D,SAAAkG,EAAA3W,GAAAiK,EAAA2M,QAAAD,EAAA3W,GAAA,CAAA,EAIA,OAHAiK,EAAAC,QACAkS,EAAA7F,QAAAtM,EAAAC,MAAA,EACAkS,EAAAhM,QAAAnG,EAAAmG,QACAgM,CACA,EAOAjI,EAAA9Q,UAAAsN,OAAA,SAAAC,GACA,IAAAwR,EAAApS,EAAA3M,UAAAsN,OAAA7S,KAAAqF,KAAAyN,CAAA,EACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,UAAAoU,GAAAA,EAAAne,SAAA3G,GACA,UAAA0S,EAAAiG,YAAA9S,KAAAkf,aAAAzR,CAAA,GAAA,GACA,SAAAwR,GAAAA,EAAAlY,QAAA5M,GACA,UAAAuT,EAAA1N,KAAAiN,QAAA9S,GACA,CACA,EAQA2E,OAAAgQ,eAAAkC,EAAA9Q,UAAA,eAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAgf,IAAAhf,KAAAgf,EAAAnkB,EAAAwY,QAAArT,KAAAyT,OAAA,EACA,CACA,CAAA,EAUAzC,EAAA9Q,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAyT,QAAAhZ,IACAoS,EAAA3M,UAAAsJ,IAAA7O,KAAAqF,KAAAvF,CAAA,CACA,EAKAuW,EAAA9Q,UAAAgU,WAAA,WAEA,IADA,IAAAT,EAAAzT,KAAAkf,aACAriB,EAAA,EAAAA,EAAA4W,EAAA7X,OAAA,EAAAiB,EACA4W,EAAA5W,GAAAZ,QAAA,EACA,OAAA4Q,EAAA3M,UAAAjE,QAAAtB,KAAAqF,IAAA,CACA,EAKAgR,EAAA9Q,UAAA0N,IAAA,SAAA0E,GAGA,GAAAtS,KAAAwJ,IAAA8I,EAAA7X,IAAA,EACA,MAAAuD,MAAA,mBAAAsU,EAAA7X,KAAA,QAAAuF,IAAA,EAEA,OAAAsS,aAAArB,EAGAiC,GAFAlT,KAAAyT,QAAAnB,EAAA7X,MAAA6X,GACAnD,OAAAnP,IACA,EAEA6M,EAAA3M,UAAA0N,IAAAjT,KAAAqF,KAAAsS,CAAA,CACA,EAKAtB,EAAA9Q,UAAAgO,OAAA,SAAAoE,GACA,GAAAA,aAAArB,EAAA,CAGA,GAAAjR,KAAAyT,QAAAnB,EAAA7X,QAAA6X,EACA,MAAAtU,MAAAsU,EAAA,uBAAAtS,IAAA,EAIA,OAFA,OAAAA,KAAAyT,QAAAnB,EAAA7X,MACA6X,EAAAnD,OAAA,KACA+D,EAAAlT,IAAA,CACA,CACA,OAAA6M,EAAA3M,UAAAgO,OAAAvT,KAAAqF,KAAAsS,CAAA,CACA,EASAtB,EAAA9Q,UAAA4M,OAAA,SAAA0R,EAAAC,EAAAC,GAEA,IADA,IACAtF,EADA+F,EAAA,IAAA3N,EAAAR,QAAAwN,EAAAC,EAAAC,CAAA,EACA7hB,EAAA,EAAAA,EAAAmD,KAAAkf,aAAAtjB,OAAA,EAAAiB,EAAA,CACA,IAAAuiB,EAAAvkB,EAAAkf,SAAAX,EAAApZ,KAAAgf,EAAAniB,IAAAZ,QAAA,EAAAxB,IAAA,EAAA6E,QAAA,WAAA,EAAA,EACA6f,EAAAC,GAAAvkB,EAAAqD,QAAA,CAAA,IAAA,KAAArD,EAAAwkB,WAAAD,CAAA,EAAAA,EAAA,IAAAA,CAAA,EAAA,gCAAA,EAAA,CACAE,EAAAlG,EACAmG,EAAAnG,EAAAzG,oBAAAhD,KACA6P,EAAApG,EAAAxG,qBAAAjD,IACA,CAAA,CACA,CACA,OAAAwP,CACA,C,iDCrKA/jB,EAAAR,QAAA8W,EAEA,IAAA+N,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACA3jB,EAAA,KACAU,EAAA,IACA,EASA,SAAAkjB,EAAAC,GACA,OAAAA,EAAA/gB,QAAA0gB,EAAA,SAAAzgB,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAAygB,EAAAzgB,IAAA,EACA,CACA,CAAA,CACA,CA6DA,SAAAkS,EAAAlT,EAAAyY,GAEAzY,EAAAA,EAAAC,SAAA,EAEA,IAAA5C,EAAA,EACAD,EAAA4C,EAAA5C,OACAgc,EAAA,EACA0I,EAAA,EACApT,EAAA,GAEAqT,EAAA,GAEAC,EAAA,KASA,SAAA9I,EAAA+I,GACA,OAAAziB,MAAA,WAAAyiB,EAAA,UAAA7I,EAAA,GAAA,CACA,CAyBA,SAAA8I,EAAAte,GACA,OAAA5D,EAAAA,EAAA4D,IAAA5D,EACA,CAUA,SAAAmiB,EAAA3jB,EAAAC,EAAA2jB,GACA,IAYA9iB,EAZAmP,EAAA,CACA7F,KAAA5I,EAAAA,EAAAxB,CAAA,KAAAwB,GACAqiB,UAAA,CAAA,EACAC,QAAAF,CACA,EAGAG,EADA9J,EACA,EAEA,EAEA+J,EAAAhkB,EAAA+jB,EAEA,GACA,GAAA,EAAAC,EAAA,GACA,OAAAljB,EAAAU,EAAAA,EAAAwiB,IAAAxiB,IAAA,CACAyO,EAAA4T,UAAA,CAAA,EACA,KACA,CAAA,OACA,MAAA/iB,GAAA,OAAAA,GAIA,IAHA,IAAAmjB,EAAAziB,EACAyZ,UAAAjb,EAAAC,CAAA,EACAyI,MAAAoa,CAAA,EACAjjB,EAAA,EAAAA,EAAAokB,EAAArlB,OAAA,EAAAiB,EACAokB,EAAApkB,GAAAokB,EAAApkB,GACAyC,QAAA2X,EAAA4I,EAAAD,EAAA,EAAA,EACAsB,KAAA,EACAjU,EAAAkU,KAAAF,EACAtjB,KAAA,IAAA,EACAujB,KAAA,EAEAhU,EAAA0K,GAAA3K,EACAqT,EAAA1I,CACA,CAEA,SAAAwJ,EAAAC,GACA,IAAAC,EAAAC,EAAAF,CAAA,EAGAG,EAAAhjB,EAAAyZ,UAAAoJ,EAAAC,CAAA,EAEA,MADA,WAAArjB,KAAAujB,CAAA,CAEA,CAEA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAA1lB,GAAA,OAAA8kB,EAAAY,CAAA,GACAA,CAAA,GAEA,OAAAA,CACA,CAOA,SAAApK,IACA,GAAA,EAAAqJ,EAAA3kB,OACA,OAAA2kB,EAAA1a,MAAA,EACA,GAAA2a,EAAA,CA3FA,IAAAkB,EAAA,MAAAlB,EAAAb,EAAAD,EAEAiC,GADAD,EAAAE,UAAA/lB,EAAA,EACA6lB,EAAAG,KAAArjB,CAAA,GACA,GAAAmjB,EAKA,OAHA9lB,EAAA6lB,EAAAE,UACArkB,EAAAijB,CAAA,EACAA,EAAA,KACAJ,EAAAuB,EAAA,EAAA,EAJA,MAAAjK,EAAA,QAAA,CAwFA,CACA,IAAAoK,EACAnO,EACAoO,EACA/kB,EACAglB,EACAC,EAAA,IAAApmB,EACA,EAAA,CACA,GAAAA,IAAAD,EACA,OAAA,KAEA,IADAkmB,EAAA,CAAA,EACA/B,EAAA9hB,KAAA8jB,EAAArB,EAAA7kB,CAAA,CAAA,GAKA,GAJA,OAAAkmB,IACAE,EAAA,CAAA,EACA,EAAArK,GAEA,EAAA/b,IAAAD,EACA,OAAA,KAGA,GAAA,MAAA8kB,EAAA7kB,CAAA,EAAA,CACA,GAAA,EAAAA,IAAAD,EACA,MAAA8b,EAAA,SAAA,EAEA,GAAA,MAAAgJ,EAAA7kB,CAAA,EACA,GAAAob,EAAA,CAsBA,GADA+K,EAAA,CAAA,EACAZ,GAFApkB,EAAAnB,GAEA,CAAA,EAEA,IADAmmB,EAAA,CAAA,GAEAnmB,EAAA0lB,EAAA1lB,CAAA,KACAD,IAGAC,CAAA,GACAomB,GAIAb,EAAAvlB,CAAA,UAEAA,EAAAY,KAAAqgB,IAAAlhB,EAAA2lB,EAAA1lB,CAAA,EAAA,CAAA,EAEAmmB,IACArB,EAAA3jB,EAAAnB,EAAAomB,CAAA,EACAA,EAAA,CAAA,GAEArK,CAAA,EAEA,KA5CA,CAIA,IAFAoK,EAAA,MAAAtB,EAAA1jB,EAAAnB,EAAA,CAAA,EAEA,OAAA6kB,EAAA,EAAA7kB,CAAA,GACA,GAAAA,IAAAD,EACA,OAAA,KAGA,EAAAC,EACAmmB,IACArB,EAAA3jB,EAAAnB,EAAA,EAAAomB,CAAA,EAGAA,EAAA,CAAA,GAEA,EAAArK,CA4BA,KA7CA,CA8CA,GAAA,OAAAmK,EAAArB,EAAA7kB,CAAA,GAqBA,MAAA,IAnBAmB,EAAAnB,EAAA,EACAmmB,EAAA/K,GAAA,MAAAyJ,EAAA1jB,CAAA,EACA,GAIA,GAHA,OAAA+kB,GACA,EAAAnK,EAEA,EAAA/b,IAAAD,EACA,MAAA8b,EAAA,SAAA,CACA,OACA/D,EAAAoO,EACAA,EAAArB,EAAA7kB,CAAA,EACA,MAAA8X,GAAA,MAAAoO,GACA,EAAAlmB,EACAmmB,IACArB,EAAA3jB,EAAAnB,EAAA,EAAAomB,CAAA,EACAA,EAAA,CAAA,EAKA,CAxBAH,EAAA,CAAA,CAyBA,CACA,OAAAA,GAIA,IAAA7kB,EAAApB,EAGA,GAFA4jB,EAAAmC,UAAA,EAEA,CADAnC,EAAAxhB,KAAAyiB,EAAAzjB,CAAA,EAAA,CAAA,EAEA,KAAAA,EAAArB,GAAA,CAAA6jB,EAAAxhB,KAAAyiB,EAAAzjB,CAAA,CAAA,GACA,EAAAA,EACA4Z,EAAArY,EAAAyZ,UAAApc,EAAAA,EAAAoB,CAAA,EAGA,MAFA,KAAA4Z,GAAA,KAAAA,IACA2J,EAAA3J,GACAA,CACA,CAQA,SAAAtZ,EAAAsZ,GACA0J,EAAAhjB,KAAAsZ,CAAA,CACA,CAOA,SAAAM,IACA,GAAA,CAAAoJ,EAAA3kB,OAAA,CACA,IAAAib,EAAAK,EAAA,EACA,GAAA,OAAAL,EACA,OAAA,KACAtZ,EAAAsZ,CAAA,CACA,CACA,OAAA0J,EAAA,EACA,CAmDA,OAAAzhB,OAAAgQ,eAAA,CACAoI,KAAAA,EACAC,KAAAA,EACA5Z,KAAAA,EACA6Z,KA7CA,SAAA8K,EAAAvV,GACA,IAAAwV,EAAAhL,EAAA,EAEA,GADAgL,IAAAD,EAGA,OADAhL,EAAA,EACA,CAAA,EAEA,GAAAvK,EAEA,MAAA,CAAA,EADA,MAAA+K,EAAA,UAAAyK,EAAA,OAAAD,EAAA,YAAA,CAEA,EAoCA7K,KA5BA,SAAAqC,GACA,IACAzM,EADAmV,EAAA,KAmBA,OAjBA1I,IAAAvf,IACA8S,EAAAC,EAAA0K,EAAA,GACA,OAAA1K,EAAA0K,EAAA,GACA3K,IAAAgK,GAAA,MAAAhK,EAAA7F,MAAA6F,EAAA4T,aACAuB,EAAAnV,EAAA6T,QAAA7T,EAAAkU,KAAA,QAIAb,EAAA5G,GACAvC,EAAA,EAEAlK,EAAAC,EAAAwM,GACA,OAAAxM,EAAAwM,GACAzM,CAAAA,GAAAA,EAAA4T,WAAA5J,CAAAA,GAAA,MAAAhK,EAAA7F,OACAgb,EAAAnV,EAAA6T,QAAA,KAAA7T,EAAAkU,OAGAiB,CACA,CAQA,EAAA,OAAA,CACA5Y,IAAA,WAAA,OAAAoO,CAAA,CACA,CAAA,CAEA,CAxXAlG,EAAA0O,SAAAA,C,0BCtCAhlB,EAAAR,QAAAwT,EAGA,IAAAvB,EAAAvR,EAAA,EAAA,EAGAoO,KAFA0E,EAAAlO,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAAqB,GAAApB,UAAA,OAEA1R,EAAA,EAAA,GACAwV,EAAAxV,EAAA,EAAA,EACA6S,EAAA7S,EAAA,EAAA,EACAyV,EAAAzV,EAAA,EAAA,EACA0V,EAAA1V,EAAA,EAAA,EACA4V,EAAA5V,EAAA,EAAA,EACAgW,EAAAhW,EAAA,EAAA,EACA8V,EAAA9V,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EACAqV,EAAArV,EAAA,EAAA,EACAsV,EAAAtV,EAAA,EAAA,EACAuV,EAAAvV,EAAA,EAAA,EACAiP,EAAAjP,EAAA,EAAA,EACA6V,EAAA7V,EAAA,EAAA,EAUA,SAAA8S,EAAA3T,EAAAqG,GACA+L,EAAAlS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAAkH,OAAA,GAMAlH,KAAA+H,OAAA5N,GAMA6F,KAAA6Z,WAAA1f,GAMA6F,KAAAqN,SAAAlT,GAMA6F,KAAAgM,MAAA7R,GAOA6F,KAAAqiB,EAAA,KAOAriB,KAAA6L,EAAA,KAOA7L,KAAAsiB,EAAA,KAOAtiB,KAAAuiB,EAAA,IACA,CAyHA,SAAArP,EAAA9L,GAKA,OAJAA,EAAAib,EAAAjb,EAAAyE,EAAAzE,EAAAkb,EAAA,KACA,OAAAlb,EAAAtK,OACA,OAAAsK,EAAAvJ,OACA,OAAAuJ,EAAAiL,OACAjL,CACA,CA7HAtI,OAAA6V,iBAAAvG,EAAAlO,UAAA,CAQAsiB,WAAA,CACAhZ,IAAA,WAGA,GAAAxJ,CAAAA,KAAAqiB,EAAA,CAGAriB,KAAAqiB,EAAA,GACA,IAAA,IAAA7O,EAAA1U,OAAAC,KAAAiB,KAAAkH,MAAA,EAAArK,EAAA,EAAAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA7J,KAAAkH,OAAAsM,EAAA3W,IACAwK,EAAAwC,EAAAxC,GAGA,GAAArH,KAAAqiB,EAAAhb,GACA,MAAArJ,MAAA,gBAAAqJ,EAAA,OAAArH,IAAA,EAEAA,KAAAqiB,EAAAhb,GAAAwC,CACA,CAZA,CAaA,OAAA7J,KAAAqiB,CACA,CACA,EAQA3X,YAAA,CACAlB,IAAA,WACA,OAAAxJ,KAAA6L,IAAA7L,KAAA6L,EAAAhR,EAAAwY,QAAArT,KAAAkH,MAAA,EACA,CACA,EAQAub,YAAA,CACAjZ,IAAA,WACA,OAAAxJ,KAAAsiB,IAAAtiB,KAAAsiB,EAAAznB,EAAAwY,QAAArT,KAAA+H,MAAA,EACA,CACA,EAQA4H,KAAA,CACAnG,IAAA,WACA,OAAAxJ,KAAAuiB,IAAAviB,KAAA2P,KAAAvB,EAAAsU,oBAAA1iB,IAAA,EAAA,EACA,EACA4V,IAAA,SAAAjG,GAmBA,IAhBA,IAAAzP,EAAAyP,EAAAzP,UAeArD,GAdAqD,aAAAgR,KACAvB,EAAAzP,UAAA,IAAAgR,GAAAnE,YAAA4C,EACA9U,EAAAyhB,MAAA3M,EAAAzP,UAAAA,CAAA,GAIAyP,EAAAqC,MAAArC,EAAAzP,UAAA8R,MAAAhS,KAGAnF,EAAAyhB,MAAA3M,EAAAuB,EAAA,CAAA,CAAA,EAEAlR,KAAAuiB,EAAA5S,EAGA,GACA9S,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,EACAmD,KAAA6L,EAAAhP,GAAAZ,QAAA,EAIA,IADA,IAAA0mB,EAAA,GACA9lB,EAAA,EAAAA,EAAAmD,KAAAyiB,YAAA7mB,OAAA,EAAAiB,EACA8lB,EAAA3iB,KAAAsiB,EAAAzlB,GAAAZ,QAAA,EAAAxB,MAAA,CACA+O,IAAA3O,EAAA8a,YAAA3V,KAAAsiB,EAAAzlB,GAAAoL,KAAA,EACA2N,IAAA/a,EAAAgb,YAAA7V,KAAAsiB,EAAAzlB,GAAAoL,KAAA,CACA,EACApL,GACAiC,OAAA6V,iBAAAhF,EAAAzP,UAAAyiB,CAAA,CACA,CACA,CACA,CAAA,EAOAvU,EAAAsU,oBAAA,SAAAjY,GAIA,IAFA,IAEAZ,EAFAD,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,IAAA,EAEAoC,EAAA,EAAAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,GACAgN,EAAAY,EAAAoB,EAAAhP,IAAA+N,IAAAhB,EACA,YAAA/O,EAAA8P,SAAAd,EAAApP,IAAA,CAAA,EACAoP,EAAAM,UAAAP,EACA,YAAA/O,EAAA8P,SAAAd,EAAApP,IAAA,CAAA,EACA,OAAAmP,EACA,uEAAA,EACA,sBAAA,CAEA,EA2BAwE,EAAAd,SAAA,SAAA7S,EAAAqM,GAMA,IALA,IAAAM,EAAA,IAAAgH,EAAA3T,EAAAqM,EAAAhG,OAAA,EAGA0S,GAFApM,EAAAyS,WAAA/S,EAAA+S,WACAzS,EAAAiG,SAAAvG,EAAAuG,SACAvO,OAAAC,KAAA+H,EAAAI,MAAA,GACArK,EAAA,EACAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EACAuK,EAAAwG,KACA,KAAA,IAAA9G,EAAAI,OAAAsM,EAAA3W,IAAAgL,QACAkJ,EACA5C,GADAb,SACAkG,EAAA3W,GAAAiK,EAAAI,OAAAsM,EAAA3W,GAAA,CACA,EACA,GAAAiK,EAAAiB,OACA,IAAAyL,EAAA1U,OAAAC,KAAA+H,EAAAiB,MAAA,EAAAlL,EAAA,EAAAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EACAuK,EAAAwG,IAAAkD,EAAAxD,SAAAkG,EAAA3W,GAAAiK,EAAAiB,OAAAyL,EAAA3W,GAAA,CAAA,EACA,GAAAiK,EAAAC,OACA,IAAAyM,EAAA1U,OAAAC,KAAA+H,EAAAC,MAAA,EAAAlK,EAAA,EAAAA,EAAA2W,EAAA5X,OAAA,EAAAiB,EAAA,CACA,IAAAkK,EAAAD,EAAAC,OAAAyM,EAAA3W,IACAuK,EAAAwG,KACA7G,EAAAM,KAAAlN,GACAgU,EACApH,EAAAG,SAAA/M,GACAiU,EACArH,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAA0M,UAAAtZ,GACA6W,EACAnE,GAPAS,SAOAkG,EAAA3W,GAAAkK,CAAA,CACA,CACA,CASA,OARAD,EAAA+S,YAAA/S,EAAA+S,WAAAje,SACAwL,EAAAyS,WAAA/S,EAAA+S,YACA/S,EAAAuG,UAAAvG,EAAAuG,SAAAzR,SACAwL,EAAAiG,SAAAvG,EAAAuG,UACAvG,EAAAkF,QACA5E,EAAA4E,MAAA,CAAA,GACAlF,EAAAmG,UACA7F,EAAA6F,QAAAnG,EAAAmG,SACA7F,CACA,EAOAgH,EAAAlO,UAAAsN,OAAA,SAAAC,GACA,IAAAwR,EAAApS,EAAA3M,UAAAsN,OAAA7S,KAAAqF,KAAAyN,CAAA,EACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAA7S,EAAAgQ,SAAA,CACA,UAAAoU,GAAAA,EAAAne,SAAA3G,GACA,SAAA0S,EAAAiG,YAAA9S,KAAAyiB,YAAAhV,CAAA,EACA,SAAAZ,EAAAiG,YAAA9S,KAAA0K,YAAAqB,OAAA,SAAAiH,GAAA,MAAA,CAAAA,EAAApE,cAAA,CAAA,EAAAnB,CAAA,GAAA,GACA,aAAAzN,KAAA6Z,YAAA7Z,KAAA6Z,WAAAje,OAAAoE,KAAA6Z,WAAA1f,GACA,WAAA6F,KAAAqN,UAAArN,KAAAqN,SAAAzR,OAAAoE,KAAAqN,SAAAlT,GACA,QAAA6F,KAAAgM,OAAA7R,GACA,SAAA8kB,GAAAA,EAAAlY,QAAA5M,GACA,UAAAuT,EAAA1N,KAAAiN,QAAA9S,GACA,CACA,EAKAiU,EAAAlO,UAAAgU,WAAA,WAEA,IADA,IAAAhN,EAAAlH,KAAA0K,YAAA7N,EAAA,EACAA,EAAAqK,EAAAtL,QACAsL,EAAArK,CAAA,IAAAZ,QAAA,EAEA,IADA,IAAA8L,EAAA/H,KAAAyiB,YAAA5lB,EAAA,EACAA,EAAAkL,EAAAnM,QACAmM,EAAAlL,CAAA,IAAAZ,QAAA,EACA,OAAA4Q,EAAA3M,UAAAgU,WAAAvZ,KAAAqF,IAAA,CACA,EAKAoO,EAAAlO,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAkH,OAAAzM,IACAuF,KAAA+H,QAAA/H,KAAA+H,OAAAtN,IACAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,IACA,EASA2T,EAAAlO,UAAA0N,IAAA,SAAA0E,GAEA,GAAAtS,KAAAwJ,IAAA8I,EAAA7X,IAAA,EACA,MAAAuD,MAAA,mBAAAsU,EAAA7X,KAAA,QAAAuF,IAAA,EAEA,GAAAsS,aAAAnE,GAAAmE,EAAAhE,SAAAnU,GAAA,CAMA,IAAA6F,KAAAqiB,GAAAriB,KAAAwiB,YAAAlQ,EAAAjL,IACA,MAAArJ,MAAA,gBAAAsU,EAAAjL,GAAA,OAAArH,IAAA,EACA,GAAAA,KAAA+N,aAAAuE,EAAAjL,EAAA,EACA,MAAArJ,MAAA,MAAAsU,EAAAjL,GAAA,mBAAArH,IAAA,EACA,GAAAA,KAAAgO,eAAAsE,EAAA7X,IAAA,EACA,MAAAuD,MAAA,SAAAsU,EAAA7X,KAAA,oBAAAuF,IAAA,EAOA,OALAsS,EAAAnD,QACAmD,EAAAnD,OAAAjB,OAAAoE,CAAA,GACAtS,KAAAkH,OAAAoL,EAAA7X,MAAA6X,GACA7D,QAAAzO,KACAsS,EAAAuB,MAAA7T,IAAA,EACAkT,EAAAlT,IAAA,CACA,CACA,OAAAsS,aAAAxB,GACA9Q,KAAA+H,SACA/H,KAAA+H,OAAA,KACA/H,KAAA+H,OAAAuK,EAAA7X,MAAA6X,GACAuB,MAAA7T,IAAA,EACAkT,EAAAlT,IAAA,GAEA6M,EAAA3M,UAAA0N,IAAAjT,KAAAqF,KAAAsS,CAAA,CACA,EASAlE,EAAAlO,UAAAgO,OAAA,SAAAoE,GACA,GAAAA,aAAAnE,GAAAmE,EAAAhE,SAAAnU,GAAA,CAIA,GAAA6F,KAAAkH,QAAAlH,KAAAkH,OAAAoL,EAAA7X,QAAA6X,EAMA,OAHA,OAAAtS,KAAAkH,OAAAoL,EAAA7X,MACA6X,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA9T,IAAA,EACAkT,EAAAlT,IAAA,EALA,MAAAhC,MAAAsU,EAAA,uBAAAtS,IAAA,CAMA,CACA,GAAAsS,aAAAxB,EAAA,CAGA,GAAA9Q,KAAA+H,QAAA/H,KAAA+H,OAAAuK,EAAA7X,QAAA6X,EAMA,OAHA,OAAAtS,KAAA+H,OAAAuK,EAAA7X,MACA6X,EAAAnD,OAAA,KACAmD,EAAAwB,SAAA9T,IAAA,EACAkT,EAAAlT,IAAA,EALA,MAAAhC,MAAAsU,EAAA,uBAAAtS,IAAA,CAMA,CACA,OAAA6M,EAAA3M,UAAAgO,OAAAvT,KAAAqF,KAAAsS,CAAA,CACA,EAOAlE,EAAAlO,UAAA6N,aAAA,SAAA1G,GACA,OAAAwF,EAAAkB,aAAA/N,KAAAqN,SAAAhG,CAAA,CACA,EAOA+G,EAAAlO,UAAA8N,eAAA,SAAAvT,GACA,OAAAoS,EAAAmB,eAAAhO,KAAAqN,SAAA5S,CAAA,CACA,EAOA2T,EAAAlO,UAAA4M,OAAA,SAAAiF,GACA,OAAA,IAAA/R,KAAA2P,KAAAoC,CAAA,CACA,EAMA3D,EAAAlO,UAAA0iB,MAAA,WAMA,IAFA,IAAAxY,EAAApK,KAAAoK,SACA8B,EAAA,GACArP,EAAA,EAAAA,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,EACAqP,EAAA3O,KAAAyC,KAAA6L,EAAAhP,GAAAZ,QAAA,EAAAgO,YAAA,EAGAjK,KAAAlD,OAAA6T,EAAA3Q,IAAA,EAAA,CACAoR,OAAAA,EACAlF,MAAAA,EACArR,KAAAA,CACA,CAAA,EACAmF,KAAAnC,OAAA+S,EAAA5Q,IAAA,EAAA,CACAsR,OAAAA,EACApF,MAAAA,EACArR,KAAAA,CACA,CAAA,EACAmF,KAAAqS,OAAAxB,EAAA7Q,IAAA,EAAA,CACAkM,MAAAA,EACArR,KAAAA,CACA,CAAA,EACAmF,KAAAwK,WAAAD,EAAAC,WAAAxK,IAAA,EAAA,CACAkM,MAAAA,EACArR,KAAAA,CACA,CAAA,EACAmF,KAAA6K,SAAAN,EAAAM,SAAA7K,IAAA,EAAA,CACAkM,MAAAA,EACArR,KAAAA,CACA,CAAA,EAGA,IAEAgoB,EAFAC,EAAA3R,EAAA/G,GAaA,OAZA0Y,KACAD,EAAA/jB,OAAAgO,OAAA9M,IAAA,GAEAwK,WAAAxK,KAAAwK,WACAxK,KAAAwK,WAAAsY,EAAAtY,WAAAhG,KAAAqe,CAAA,EAGAA,EAAAhY,SAAA7K,KAAA6K,SACA7K,KAAA6K,SAAAiY,EAAAjY,SAAArG,KAAAqe,CAAA,GAIA7iB,IACA,EAQAoO,EAAAlO,UAAApD,OAAA,SAAA2R,EAAAwD,GACA,OAAAjS,KAAA4iB,MAAA,EAAA9lB,OAAA2R,EAAAwD,CAAA,CACA,EAQA7D,EAAAlO,UAAAgS,gBAAA,SAAAzD,EAAAwD,GACA,OAAAjS,KAAAlD,OAAA2R,EAAAwD,GAAAA,EAAA1L,IAAA0L,EAAA8Q,KAAA,EAAA9Q,CAAA,EAAA+Q,OAAA,CACA,EAUA5U,EAAAlO,UAAArC,OAAA,SAAAsU,EAAAvW,GACA,OAAAoE,KAAA4iB,MAAA,EAAA/kB,OAAAsU,EAAAvW,CAAA,CACA,EASAwS,EAAAlO,UAAAkS,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAAxE,OAAAqF,CAAA,GACAnS,KAAAnC,OAAAsU,EAAAA,EAAAwJ,OAAA,CAAA,CACA,EAOAvN,EAAAlO,UAAAmS,OAAA,SAAA5D,GACA,OAAAzO,KAAA4iB,MAAA,EAAAvQ,OAAA5D,CAAA,CACA,EAOAL,EAAAlO,UAAAsK,WAAA,SAAA8H,GACA,OAAAtS,KAAA4iB,MAAA,EAAApY,WAAA8H,CAAA,CACA,EA2BAlE,EAAAlO,UAAA2K,SAAA,SAAA4D,EAAA3N,GACA,OAAAd,KAAA4iB,MAAA,EAAA/X,SAAA4D,EAAA3N,CAAA,CACA,EAiBAsN,EAAAwB,EAAA,SAAAqT,GACA,OAAA,SAAA5K,GACAxd,EAAAmV,aAAAqI,EAAA4K,CAAA,CACA,CACA,C,mHCtkBA,IAEApoB,EAAAS,EAAA,EAAA,EAEAkkB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAA0D,EAAAza,EAAA5M,GACA,IAAAgB,EAAA,EAAAsmB,EAAA,GAEA,IADAtnB,GAAA,EACAgB,EAAA4L,EAAA7M,QAAAunB,EAAA3D,EAAA3iB,EAAAhB,IAAA4M,EAAA5L,CAAA,IACA,OAAAsmB,CACA,CAsBAjX,EAAAE,MAAA8W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAuBAhX,EAAAC,SAAA+W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CAAA,EACA,GACAroB,EAAA6U,WACA,KACA,EAYAxD,EAAAZ,KAAA4X,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,CAAA,EAmBAhX,EAAAO,OAAAyW,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,CAAA,EAoBAhX,EAAAG,OAAA6W,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,C,+BC7LA,IAIA9U,EACA1E,EALA7O,EAAAO,EAAAR,QAAAU,EAAA,EAAA,EAEAmW,EAAAnW,EAAA,EAAA,EAiDA8nB,GA5CAvoB,EAAAqD,QAAA5C,EAAA,CAAA,EACAT,EAAA6F,MAAApF,EAAA,CAAA,EACAT,EAAA2K,KAAAlK,EAAA,CAAA,EAMAT,EAAA+F,GAAA/F,EAAAqK,QAAA,IAAA,EAOArK,EAAAwY,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAvT,EAAAD,OAAAC,KAAAuT,CAAA,EACAS,EAAArX,MAAAqD,EAAAnD,MAAA,EACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACAmX,EAAAjX,GAAAwW,EAAAvT,EAAAjD,CAAA,KACA,OAAAiX,CACA,CACA,MAAA,EACA,EAOAlY,EAAAgQ,SAAA,SAAAkI,GAGA,IAFA,IAAAT,EAAA,GACAxW,EAAA,EACAA,EAAAiX,EAAAnX,QAAA,CACA,IAAAynB,EAAAtQ,EAAAjX,CAAA,IACAoG,EAAA6Q,EAAAjX,CAAA,IACAoG,IAAA/H,KACAmY,EAAA+Q,GAAAnhB,EACA,CACA,OAAAoQ,CACA,EAEA,OACAgR,EAAA,KA+BAC,GAxBA1oB,EAAAwkB,WAAA,SAAA5kB,GACA,MAAA,uTAAAwD,KAAAxD,CAAA,CACA,EAOAI,EAAA8P,SAAA,SAAAZ,GACA,MAAA,CAAA,YAAA9L,KAAA8L,CAAA,GAAAlP,EAAAwkB,WAAAtV,CAAA,EACA,KAAAA,EAAAzK,QAAA8jB,EAAA,MAAA,EAAA9jB,QAAAgkB,EAAA,KAAA,EAAA,KACA,IAAAvZ,CACA,EAOAlP,EAAAmf,QAAA,SAAAqG,GACA,OAAAA,EAAA,IAAAA,IAAAmD,YAAA,EAAAnD,EAAApI,UAAA,CAAA,CACA,EAEA,aAuDAwL,GAhDA5oB,EAAA4c,UAAA,SAAA4I,GACA,OAAAA,EAAApI,UAAA,EAAA,CAAA,EACAoI,EAAApI,UAAA,CAAA,EACA3Y,QAAAikB,EAAA,SAAAhkB,EAAAC,GAAA,OAAAA,EAAAgkB,YAAA,CAAA,CAAA,CACA,EAQA3oB,EAAAkQ,kBAAA,SAAA2Y,EAAApmB,GACA,OAAAomB,EAAArc,GAAA/J,EAAA+J,EACA,EAUAxM,EAAAmV,aAAA,SAAAL,EAAAsT,GAGA,OAAAtT,EAAAqC,OACAiR,GAAAtT,EAAAqC,MAAAvX,OAAAwoB,IACApoB,EAAA8oB,aAAAzV,OAAAyB,EAAAqC,KAAA,EACArC,EAAAqC,MAAAvX,KAAAwoB,EACApoB,EAAA8oB,aAAA/V,IAAA+B,EAAAqC,KAAA,GAEArC,EAAAqC,QAOA5K,EAAA,IAFAgH,EADAA,GACA9S,EAAA,EAAA,GAEA2nB,GAAAtT,EAAAlV,IAAA,EACAI,EAAA8oB,aAAA/V,IAAAxG,CAAA,EACAA,EAAAuI,KAAAA,EACA7Q,OAAAgQ,eAAAa,EAAA,QAAA,CAAAlQ,MAAA2H,EAAAwc,WAAA,CAAA,CAAA,CAAA,EACA9kB,OAAAgQ,eAAAa,EAAAzP,UAAA,QAAA,CAAAT,MAAA2H,EAAAwc,WAAA,CAAA,CAAA,CAAA,EACAxc,EACA,EAEA,GAOAvM,EAAAoV,aAAA,SAAAqC,GAGA,IAOA/E,EAPA,OAAA+E,EAAAN,QAOAzE,EAAA,IAFA7D,EADAA,GACApO,EAAA,EAAA,GAEA,OAAAmoB,CAAA,GAAAnR,CAAA,EACAzX,EAAA8oB,aAAA/V,IAAAL,CAAA,EACAzO,OAAAgQ,eAAAwD,EAAA,QAAA,CAAA7S,MAAA8N,EAAAqW,WAAA,CAAA,CAAA,CAAA,EACArW,EACA,EAUA1S,EAAAya,YAAA,SAAAuO,EAAAre,EAAA/F,GAiBA,GAAA,UAAA,OAAAokB,EACA,MAAAzW,UAAA,uBAAA,EACA,GAAA5H,EAIA,OAtBA,SAAAse,EAAAD,EAAAre,EAAA/F,GACA,IAAAwU,EAAAzO,EAAAK,MAAA,EAYA,MAXA,cAAAoO,GAAA,cAAAA,IAGA,EAAAzO,EAAA5J,OACAioB,EAAA5P,GAAA6P,EAAAD,EAAA5P,IAAA,GAAAzO,EAAA/F,CAAA,IAEAib,EAAAmJ,EAAA5P,MAEAxU,EAAA,GAAAkb,OAAAD,CAAA,EAAAC,OAAAlb,CAAA,GACAokB,EAAA5P,GAAAxU,IAEAokB,CACA,EAQAA,EADAre,EAAAA,EAAAE,MAAA,GAAA,EACAjG,CAAA,EAHA,MAAA2N,UAAA,wBAAA,CAIA,EAQAtO,OAAAgQ,eAAAjU,EAAA,eAAA,CACA2O,IAAA,WACA,OAAAiI,EAAA,YAAAA,EAAA,UAAA,IAAAnW,EAAA,EAAA,GACA,CACA,CAAA,C,mEClNAF,EAAAR,QAAAkgB,EAEA,IAAAjgB,EAAAS,EAAA,EAAA,EAUA,SAAAwf,EAAAjX,EAAAC,GASA9D,KAAA6D,GAAAA,IAAA,EAMA7D,KAAA8D,GAAAA,IAAA,CACA,CAOA,IAAAigB,EAAAjJ,EAAAiJ,KAAA,IAAAjJ,EAAA,EAAA,CAAA,EAoFA/c,GAlFAgmB,EAAArY,SAAA,WAAA,OAAA,CAAA,EACAqY,EAAAC,SAAAD,EAAArH,SAAA,WAAA,OAAA1c,IAAA,EACA+jB,EAAAnoB,OAAA,WAAA,OAAA,CAAA,EAOAkf,EAAAmJ,SAAA,mBAOAnJ,EAAAxL,WAAA,SAAA7P,GACA,IAEA4C,EAGAwB,EALA,OAAA,IAAApE,EACAskB,GAIAlgB,GADApE,GAFA4C,EAAA5C,EAAA,GAEA,CAAAA,EACAA,KAAA,EACAqE,GAAArE,EAAAoE,GAAA,aAAA,EACAxB,IACAyB,EAAA,CAAAA,IAAA,EACAD,EAAA,CAAAA,IAAA,EACA,WAAA,EAAAA,IACAA,EAAA,EACA,WAAA,EAAAC,IACAA,EAAA,KAGA,IAAAgX,EAAAjX,EAAAC,CAAA,EACA,EAOAgX,EAAAoJ,KAAA,SAAAzkB,GACA,GAAA,UAAA,OAAAA,EACA,OAAAqb,EAAAxL,WAAA7P,CAAA,EACA,GAAA5E,EAAAgT,SAAApO,CAAA,EAAA,CAEA,GAAA5E,CAAAA,EAAAI,KAGA,OAAA6f,EAAAxL,WAAA4I,SAAAzY,EAAA,EAAA,CAAA,EAFAA,EAAA5E,EAAAI,KAAAkpB,WAAA1kB,CAAA,CAGA,CACA,OAAAA,EAAA8L,KAAA9L,EAAA+L,KAAA,IAAAsP,EAAArb,EAAA8L,MAAA,EAAA9L,EAAA+L,OAAA,CAAA,EAAAuY,CACA,EAOAjJ,EAAA5a,UAAAwL,SAAA,SAAAD,GACA,IAEA3H,EAFA,MAAA,CAAA2H,GAAAzL,KAAA8D,KAAA,IACAD,EAAA,EAAA,CAAA7D,KAAA6D,KAAA,EACAC,EAAA,CAAA9D,KAAA8D,KAAA,EAGA,EAAAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA9D,KAAA6D,GAAA,WAAA7D,KAAA8D,EACA,EAOAgX,EAAA5a,UAAAkkB,OAAA,SAAA3Y,GACA,OAAA5Q,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAA+E,KAAA6D,GAAA,EAAA7D,KAAA8D,GAAA6J,CAAAA,CAAAlC,CAAA,EAEA,CAAAF,IAAA,EAAAvL,KAAA6D,GAAA2H,KAAA,EAAAxL,KAAA8D,GAAA2H,SAAAkC,CAAAA,CAAAlC,CAAA,CACA,EAEAjO,OAAA0C,UAAAnC,YAOA+c,EAAAuJ,SAAA,SAAAC,GACA,MAjFAxJ,qBAiFAwJ,EACAP,EACA,IAAAjJ,GACA/c,EAAApD,KAAA2pB,EAAA,CAAA,EACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,EACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,GACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,MAAA,GAEAvmB,EAAApD,KAAA2pB,EAAA,CAAA,EACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,EACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,GACAvmB,EAAApD,KAAA2pB,EAAA,CAAA,GAAA,MAAA,CACA,CACA,EAMAxJ,EAAA5a,UAAAqkB,OAAA,WACA,OAAA/mB,OAAAC,aACA,IAAAuC,KAAA6D,GACA7D,KAAA6D,KAAA,EAAA,IACA7D,KAAA6D,KAAA,GAAA,IACA7D,KAAA6D,KAAA,GACA,IAAA7D,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,EACA,CACA,EAMAgX,EAAA5a,UAAA8jB,SAAA,WACA,IAAAQ,EAAAxkB,KAAA8D,IAAA,GAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,IAAA,EAAA9D,KAAA6D,KAAA,IAAA2gB,KAAA,EACAxkB,KAAA6D,IAAA7D,KAAA6D,IAAA,EAAA2gB,KAAA,EACAxkB,IACA,EAMA8a,EAAA5a,UAAAwc,SAAA,WACA,IAAA8H,EAAA,EAAA,EAAAxkB,KAAA6D,IAGA,OAFA7D,KAAA6D,KAAA7D,KAAA6D,KAAA,EAAA7D,KAAA8D,IAAA,IAAA0gB,KAAA,EACAxkB,KAAA8D,IAAA9D,KAAA8D,KAAA,EAAA0gB,KAAA,EACAxkB,IACA,EAMA8a,EAAA5a,UAAAtE,OAAA,WACA,IAAA6oB,EAAAzkB,KAAA6D,GACA6gB,GAAA1kB,KAAA6D,KAAA,GAAA7D,KAAA8D,IAAA,KAAA,EACA6gB,EAAA3kB,KAAA8D,KAAA,GACA,OAAA,GAAA6gB,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,EACA,C,+BCtMA,IAAA9pB,EAAAD,EA2OA,SAAA0hB,EAAAuH,EAAAe,EAAA3V,GACA,IAAA,IAAAlQ,EAAAD,OAAAC,KAAA6lB,CAAA,EAAA/nB,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAgnB,EAAA9kB,EAAAlC,MAAA1C,IAAA8U,IACA4U,EAAA9kB,EAAAlC,IAAA+nB,EAAA7lB,EAAAlC,KACA,OAAAgnB,CACA,CAmBA,SAAAgB,EAAApqB,GAEA,SAAAqqB,EAAArW,EAAAsD,GAEA,GAAA,EAAA/R,gBAAA8kB,GACA,OAAA,IAAAA,EAAArW,EAAAsD,CAAA,EAKAjT,OAAAgQ,eAAA9O,KAAA,UAAA,CAAAwJ,IAAA,WAAA,OAAAiF,CAAA,CAAA,CAAA,EAGAzQ,MAAA+mB,kBACA/mB,MAAA+mB,kBAAA/kB,KAAA8kB,CAAA,EAEAhmB,OAAAgQ,eAAA9O,KAAA,QAAA,CAAAP,MAAAzB,MAAA,EAAAuiB,OAAA,EAAA,CAAA,EAEAxO,GACAuK,EAAAtc,KAAA+R,CAAA,CACA,CA2BA,OAzBA+S,EAAA5kB,UAAApB,OAAAgO,OAAA9O,MAAAkC,UAAA,CACA6M,YAAA,CACAtN,MAAAqlB,EACAE,SAAA,CAAA,EACApB,WAAA,CAAA,EACAqB,aAAA,CAAA,CACA,EACAxqB,KAAA,CACA+O,IAAA,WAAA,OAAA/O,CAAA,EACAmb,IAAAzb,GACAypB,WAAA,CAAA,EAKAqB,aAAA,CAAA,CACA,EACAxmB,SAAA,CACAgB,MAAA,WAAA,OAAAO,KAAAvF,KAAA,KAAAuF,KAAAyO,OAAA,EACAuW,SAAA,CAAA,EACApB,WAAA,CAAA,EACAqB,aAAA,CAAA,CACA,CACA,CAAA,EAEAH,CACA,CAhTAjqB,EAAA8F,UAAArF,EAAA,CAAA,EAGAT,EAAAwB,OAAAf,EAAA,CAAA,EAGAT,EAAAkF,aAAAzE,EAAA,CAAA,EAGAT,EAAAohB,MAAA3gB,EAAA,CAAA,EAGAT,EAAAqK,QAAA5J,EAAA,CAAA,EAGAT,EAAAyL,KAAAhL,EAAA,EAAA,EAGAT,EAAAqqB,KAAA5pB,EAAA,CAAA,EAGAT,EAAAigB,SAAAxf,EAAA,EAAA,EAOAT,EAAAojB,OAAAtQ,CAAAA,EAAA,aAAA,OAAA7S,QACAA,QACAA,OAAA6iB,SACA7iB,OAAA6iB,QAAAwH,UACArqB,OAAA6iB,QAAAwH,SAAAC,MAOAvqB,EAAAC,OAAAD,EAAAojB,QAAAnjB,QACA,aAAA,OAAAuqB,QAAAA,QACA,aAAA,OAAAlI,MAAAA,MACAnd,KAQAnF,EAAA6U,WAAA5Q,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,EAAA,EAAA,GAOA1U,EAAA4U,YAAA3Q,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,EAAA,EAAA,GAQA1U,EAAAiT,UAAApO,OAAAoO,WAAA,SAAArO,GACA,MAAA,UAAA,OAAAA,GAAA6lB,SAAA7lB,CAAA,GAAAhD,KAAAkD,MAAAF,CAAA,IAAAA,CACA,EAOA5E,EAAAgT,SAAA,SAAApO,GACA,MAAA,UAAA,OAAAA,GAAAA,aAAAjC,MACA,EAOA3C,EAAA0T,SAAA,SAAA9O,GACA,OAAAA,GAAA,UAAA,OAAAA,CACA,EAUA5E,EAAA0qB,MAQA1qB,EAAA2qB,MAAA,SAAAxS,EAAAjJ,GACA,IAAAtK,EAAAuT,EAAAjJ,GACA,OAAA,MAAAtK,GAAAuT,EAAAoC,eAAArL,CAAA,IACA,UAAA,OAAAtK,GAAA,GAAA/D,MAAAqY,QAAAtU,CAAA,EAAAA,EAAAX,OAAAC,KAAAU,CAAA,GAAA7D,OAEA,EAaAf,EAAAqgB,OAAA,WACA,IACA,IAAAA,EAAArgB,EAAAqK,QAAA,QAAA,EAAAgW,OAEA,OAAAA,EAAAhb,UAAAulB,UAAAvK,EAAA,IAIA,CAHA,MAAA5V,GAEA,OAAA,IACA,CACA,EAAA,EAGAzK,EAAA6qB,EAAA,KAGA7qB,EAAA8qB,EAAA,KAOA9qB,EAAA2U,UAAA,SAAAoW,GAEA,MAAA,UAAA,OAAAA,EACA/qB,EAAAqgB,OACArgB,EAAA8qB,EAAAC,CAAA,EACA,IAAA/qB,EAAAa,MAAAkqB,CAAA,EACA/qB,EAAAqgB,OACArgB,EAAA6qB,EAAAE,CAAA,EACA,aAAA,OAAAlkB,WACAkkB,EACA,IAAAlkB,WAAAkkB,CAAA,CACA,EAMA/qB,EAAAa,MAAA,aAAA,OAAAgG,WAAAA,WAAAhG,MAeAb,EAAAI,KAAAJ,EAAAC,OAAA+qB,SAAAhrB,EAAAC,OAAA+qB,QAAA5qB,MACAJ,EAAAC,OAAAG,MACAJ,EAAAqK,QAAA,MAAA,EAOArK,EAAAirB,OAAA,mBAOAjrB,EAAAkrB,QAAA,wBAOAlrB,EAAAmrB,QAAA,6CAOAnrB,EAAAorB,WAAA,SAAAxmB,GACA,OAAAA,EACA5E,EAAAigB,SAAAoJ,KAAAzkB,CAAA,EAAA8kB,OAAA,EACA1pB,EAAAigB,SAAAmJ,QACA,EAQAppB,EAAAqrB,aAAA,SAAA5B,EAAA7Y,GACA6P,EAAAzgB,EAAAigB,SAAAuJ,SAAAC,CAAA,EACA,OAAAzpB,EAAAI,KACAJ,EAAAI,KAAAkrB,SAAA7K,EAAAzX,GAAAyX,EAAAxX,GAAA2H,CAAA,EACA6P,EAAA5P,SAAAiC,CAAAA,CAAAlC,CAAA,CACA,EAiBA5Q,EAAAyhB,MAAAA,EAOAzhB,EAAAkf,QAAA,SAAAsG,GACA,OAAAA,EAAA,IAAAA,IAAA7R,YAAA,EAAA6R,EAAApI,UAAA,CAAA,CACA,EA0DApd,EAAAgqB,SAAAA,EAmBAhqB,EAAAurB,cAAAvB,EAAA,eAAA,EAoBAhqB,EAAA8a,YAAA,SAAAH,GAEA,IADA,IAAA6Q,EAAA,GACAxpB,EAAA,EAAAA,EAAA2Y,EAAA5Z,OAAA,EAAAiB,EACAwpB,EAAA7Q,EAAA3Y,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,IAAA,EAAAnD,EAAAkC,EAAAnD,OAAA,EAAA,CAAA,EAAAiB,EAAA,EAAAA,EACA,GAAA,IAAAwpB,EAAAtnB,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA1C,IAAA,OAAA6F,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,EACA,CACA,EAeAhC,EAAAgb,YAAA,SAAAL,GAQA,OAAA,SAAA/a,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAA2Y,EAAA5Z,OAAA,EAAAiB,EACA2Y,EAAA3Y,KAAApC,GACA,OAAAuF,KAAAwV,EAAA3Y,GACA,CACA,EAkBAhC,EAAA4S,cAAA,CACA6Y,MAAA9oB,OACA+oB,MAAA/oB,OACAmO,MAAAnO,OACAsJ,KAAA,CAAA,CACA,EAGAjM,EAAAuV,EAAA,WACA,IAAA8K,EAAArgB,EAAAqgB,OAEAA,GAMArgB,EAAA6qB,EAAAxK,EAAAgJ,OAAAxiB,WAAAwiB,MAAAhJ,EAAAgJ,MAEA,SAAAzkB,EAAA+mB,GACA,OAAA,IAAAtL,EAAAzb,EAAA+mB,CAAA,CACA,EACA3rB,EAAA8qB,EAAAzK,EAAAuL,aAEA,SAAAvgB,GACA,OAAA,IAAAgV,EAAAhV,CAAA,CACA,GAdArL,EAAA6qB,EAAA7qB,EAAA8qB,EAAA,IAeA,C,6DCpbAvqB,EAAAR,QAwHA,SAAA6P,GAGA,IAAAb,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,mCAAA,EACA,WAAA,iBAAA,EACAsN,EAAA0C,EAAAgY,YACAiE,EAAA,GACA3e,EAAAnM,QAAAgO,EACA,UAAA,EAEA,IAAA,IAAA/M,EAAA,EAAAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,EAAA,CACA,IA2BA8pB,EA3BA9c,EAAAY,EAAAoB,EAAAhP,GAAAZ,QAAA,EACAgQ,EAAA,IAAApR,EAAA8P,SAAAd,EAAApP,IAAA,EAEAoP,EAAA8C,UAAA/C,EACA,sCAAAqC,EAAApC,EAAApP,IAAA,EAGAoP,EAAAe,KAAAhB,EACA,yBAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,QAAA,CAAA,EACA,wBAAAoC,CAAA,EACA,8BAAA,EAxDA,SAAArC,EAAAC,EAAAoC,GAEA,OAAApC,EAAAhC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA+B,EACA,6BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,aAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,kBAAA,CAAA,EACA,MACA,IAAA,OAAAD,EACA,4BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,aAAA,CAAA,CAEA,CAGA,EA+BAD,EAAAC,EAAA,MAAA,EACAgd,EAAAjd,EAAAC,EAAAhN,EAAAoP,EAAA,QAAA,EACA,GAAA,GAGApC,EAAAM,UAAAP,EACA,yBAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,OAAA,CAAA,EACA,gCAAAoC,CAAA,EACA4a,EAAAjd,EAAAC,EAAAhN,EAAAoP,EAAA,KAAA,EACA,GAAA,IAIApC,EAAAsB,SACAwb,EAAA9rB,EAAA8P,SAAAd,EAAAsB,OAAA1Q,IAAA,EACA,IAAAisB,EAAA7c,EAAAsB,OAAA1Q,OAAAmP,EACA,cAAA+c,CAAA,EACA,WAAA9c,EAAAsB,OAAA1Q,KAAA,mBAAA,EACAisB,EAAA7c,EAAAsB,OAAA1Q,MAAA,EACAmP,EACA,QAAA+c,CAAA,GAEAE,EAAAjd,EAAAC,EAAAhN,EAAAoP,CAAA,GAEApC,EAAA8C,UAAA/C,EACA,GAAA,CACA,CACA,OAAAA,EACA,aAAA,CAEA,EA7KA,IAAAF,EAAApO,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAEA,SAAAsrB,EAAA/c,EAAAqY,GACA,OAAArY,EAAApP,KAAA,KAAAynB,GAAArY,EAAAM,UAAA,UAAA+X,EAAA,KAAArY,EAAAe,KAAA,WAAAsX,EAAA,MAAArY,EAAAhC,QAAA,IAAA,IAAA,WACA,CAWA,SAAAgf,EAAAjd,EAAAC,EAAAC,EAAAmC,GAEA,GAAApC,EAAAI,aACA,GAAAJ,EAAAI,wBAAAP,EAAA,CAAAE,EACA,cAAAqC,CAAA,EACA,UAAA,EACA,WAAA2a,EAAA/c,EAAA,YAAA,CAAA,EACA,IAAA,IAAA9K,EAAAD,OAAAC,KAAA8K,EAAAI,aAAAxB,MAAA,EAAApL,EAAA,EAAAA,EAAA0B,EAAAnD,OAAA,EAAAyB,EAAAuM,EACA,WAAAC,EAAAI,aAAAxB,OAAA1J,EAAA1B,GAAA,EACAuM,EACA,OAAA,EACA,GAAA,CACA,MACAA,EACA,GAAA,EACA,8BAAAE,EAAAmC,CAAA,EACA,OAAA,EACA,aAAApC,EAAApP,KAAA,GAAA,EACA,GAAA,OAGA,OAAAoP,EAAAzC,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAwC,EACA,0BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,SAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAqC,EAAAA,EAAAA,EAAAA,CAAA,EACA,WAAA2a,EAAA/c,EAAA,cAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,QAAA,CAAA,EACA,MACA,IAAA,OAAAD,EACA,4BAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,SAAA,CAAA,EACA,MACA,IAAA,SAAAD,EACA,yBAAAqC,CAAA,EACA,WAAA2a,EAAA/c,EAAA,QAAA,CAAA,EACA,MACA,IAAA,QAAAD,EACA,4DAAAqC,EAAAA,EAAAA,CAAA,EACA,WAAA2a,EAAA/c,EAAA,QAAA,CAAA,CAEA,CAEA,OAAAD,CAEA,C,qCCvEA,IAEAsH,EAAA5V,EAAA,EAAA,EA6BA6V,EAAA,wBAAA,CAEA3G,WAAA,SAAA8H,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAKAnL,EALA1M,EAAA6X,EAAA,SAAA2F,UAAA,EAAA3F,EAAA,SAAAmL,YAAA,GAAA,CAAA,EACArW,EAAApH,KAAAmU,OAAA1Z,CAAA,EAEA,GAAA2M,EAQA,MAHAD,EAHAA,EAAA,MAAAmL,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAA5U,MAAA,CAAA,EAAA4U,EAAA,UAEAxG,QAAA,GAAA,IACA3E,EAAA,IAAAA,GAEAnH,KAAA8M,OAAA,CACA3F,SAAAA,EACA1H,MAAA2H,EAAAtK,OAAAsK,EAAAoD,WAAA8H,CAAA,CAAA,EAAA+K,OAAA,CACA,CAAA,CAEA,CAEA,OAAArd,KAAAwK,WAAA8H,CAAA,CACA,EAEAzH,SAAA,SAAA4D,EAAA3N,GAGA,IAkBAwR,EACAwU,EAlBAlhB,EAAA,GACAnL,EAAA,GAeA,OAZAqG,GAAAA,EAAAgG,MAAA2H,EAAAtH,UAAAsH,EAAAhP,QAEAhF,EAAAgU,EAAAtH,SAAA8Q,UAAA,EAAAxJ,EAAAtH,SAAAsW,YAAA,GAAA,CAAA,EAEA7X,EAAA6I,EAAAtH,SAAA8Q,UAAA,EAAA,EAAAxJ,EAAAtH,SAAAsW,YAAA,GAAA,CAAA,GACArW,EAAApH,KAAAmU,OAAA1Z,CAAA,KAGAgU,EAAArH,EAAAvJ,OAAA4Q,EAAAhP,KAAA,IAIA,EAAAgP,aAAAzO,KAAA2P,OAAAlB,aAAAyC,GACAoB,EAAA7D,EAAAuD,MAAAnH,SAAA4D,EAAA3N,CAAA,EACAgmB,EAAA,MAAArY,EAAAuD,MAAA5H,SAAA,GACAqE,EAAAuD,MAAA5H,SAAA1M,MAAA,CAAA,EAAA+Q,EAAAuD,MAAA5H,SAMAkI,EAAA,SADA7X,GAFAmL,EADA,KAAAA,EAtBA,uBAyBAA,GAAAkhB,EAEAxU,GAGAtS,KAAA6K,SAAA4D,EAAA3N,CAAA,CACA,CACA,C,+BCpGA1F,EAAAR,QAAAwW,EAEA,IAEAC,EAFAxW,EAAAS,EAAA,EAAA,EAIAwf,EAAAjgB,EAAAigB,SACAze,EAAAxB,EAAAwB,OACAiK,EAAAzL,EAAAyL,KAWA,SAAAygB,EAAAxrB,EAAAgL,EAAArE,GAMAlC,KAAAzE,GAAAA,EAMAyE,KAAAuG,IAAAA,EAMAvG,KAAAkX,KAAA/c,GAMA6F,KAAAkC,IAAAA,CACA,CAGA,SAAA8kB,KAUA,SAAAC,EAAAhV,GAMAjS,KAAAsX,KAAArF,EAAAqF,KAMAtX,KAAAknB,KAAAjV,EAAAiV,KAMAlnB,KAAAuG,IAAA0L,EAAA1L,IAMAvG,KAAAkX,KAAAjF,EAAAkV,MACA,CAOA,SAAA/V,IAMApR,KAAAuG,IAAA,EAMAvG,KAAAsX,KAAA,IAAAyP,EAAAC,EAAA,EAAA,CAAA,EAMAhnB,KAAAknB,KAAAlnB,KAAAsX,KAMAtX,KAAAmnB,OAAA,IAOA,CAEA,SAAAra,IACA,OAAAjS,EAAAqgB,OACA,WACA,OAAA9J,EAAAtE,OAAA,WACA,OAAA,IAAAuE,CACA,GAAA,CACA,EAEA,WACA,OAAA,IAAAD,CACA,CACA,CAqCA,SAAAgW,EAAAllB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,CACA,CAmBA,SAAAmlB,EAAA9gB,EAAArE,GACAlC,KAAAuG,IAAAA,EACAvG,KAAAkX,KAAA/c,GACA6F,KAAAkC,IAAAA,CACA,CA6CA,SAAAolB,EAAAplB,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,CAAA,IAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,CAAA,IAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,CAAA,IAAAF,EAAA2B,EACA,CA0CA,SAAA0jB,EAAArlB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CA9JAkP,EAAAtE,OAAAA,EAAA,EAOAsE,EAAAnL,MAAA,SAAAC,GACA,OAAA,IAAArL,EAAAa,MAAAwK,CAAA,CACA,EAIArL,EAAAa,QAAAA,QACA0V,EAAAnL,MAAApL,EAAAqqB,KAAA9T,EAAAnL,MAAApL,EAAAa,MAAAwE,UAAAwb,QAAA,GAUAtK,EAAAlR,UAAAsnB,EAAA,SAAAjsB,EAAAgL,EAAArE,GAGA,OAFAlC,KAAAknB,KAAAlnB,KAAAknB,KAAAhQ,KAAA,IAAA6P,EAAAxrB,EAAAgL,EAAArE,CAAA,EACAlC,KAAAuG,KAAAA,EACAvG,IACA,GA6BAqnB,EAAAnnB,UAAApB,OAAAgO,OAAAia,EAAA7mB,SAAA,GACA3E,GAxBA,SAAA2G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,CAAA,IAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,CACA,EAyBAkP,EAAAlR,UAAAyb,OAAA,SAAAlc,GAWA,OARAO,KAAAuG,MAAAvG,KAAAknB,KAAAlnB,KAAAknB,KAAAhQ,KAAA,IAAAmQ,GACA5nB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,CAAA,GAAA8G,IACAvG,IACA,EAQAoR,EAAAlR,UAAA0b,MAAA,SAAAnc,GACA,OAAAA,EAAA,EACAO,KAAAwnB,EAAAF,EAAA,GAAAxM,EAAAxL,WAAA7P,CAAA,CAAA,EACAO,KAAA2b,OAAAlc,CAAA,CACA,EAOA2R,EAAAlR,UAAA2b,OAAA,SAAApc,GACA,OAAAO,KAAA2b,QAAAlc,GAAA,EAAAA,GAAA,MAAA,CAAA,CACA,EAiCA2R,EAAAlR,UAAAqc,MAZAnL,EAAAlR,UAAAsc,OAAA,SAAA/c,GACA6b,EAAAR,EAAAoJ,KAAAzkB,CAAA,EACA,OAAAO,KAAAwnB,EAAAF,EAAAhM,EAAA1f,OAAA,EAAA0f,CAAA,CACA,EAiBAlK,EAAAlR,UAAAuc,OAAA,SAAAhd,GACA6b,EAAAR,EAAAoJ,KAAAzkB,CAAA,EAAAukB,SAAA,EACA,OAAAhkB,KAAAwnB,EAAAF,EAAAhM,EAAA1f,OAAA,EAAA0f,CAAA,CACA,EAOAlK,EAAAlR,UAAA4b,KAAA,SAAArc,GACA,OAAAO,KAAAwnB,EAAAJ,EAAA,EAAA3nB,EAAA,EAAA,CAAA,CACA,EAwBA2R,EAAAlR,UAAA8b,SAVA5K,EAAAlR,UAAA6b,QAAA,SAAAtc,GACA,OAAAO,KAAAwnB,EAAAD,EAAA,EAAA9nB,IAAA,CAAA,CACA,EA4BA2R,EAAAlR,UAAA0c,SAZAxL,EAAAlR,UAAAyc,QAAA,SAAAld,GACA6b,EAAAR,EAAAoJ,KAAAzkB,CAAA,EACA,OAAAO,KAAAwnB,EAAAD,EAAA,EAAAjM,EAAAzX,EAAA,EAAA2jB,EAAAD,EAAA,EAAAjM,EAAAxX,EAAA,CACA,EAiBAsN,EAAAlR,UAAA+b,MAAA,SAAAxc,GACA,OAAAO,KAAAwnB,EAAA3sB,EAAAohB,MAAA7X,aAAA,EAAA3E,CAAA,CACA,EAQA2R,EAAAlR,UAAAgc,OAAA,SAAAzc,GACA,OAAAO,KAAAwnB,EAAA3sB,EAAAohB,MAAAnX,cAAA,EAAArF,CAAA,CACA,EAEA,IAAAgoB,EAAA5sB,EAAAa,MAAAwE,UAAA0V,IACA,SAAA1T,EAAAC,EAAAC,GACAD,EAAAyT,IAAA1T,EAAAE,CAAA,CACA,EAEA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAvF,EAAA,EAAAA,EAAAqF,EAAAtG,OAAA,EAAAiB,EACAsF,EAAAC,EAAAvF,GAAAqF,EAAArF,EACA,EAOAuU,EAAAlR,UAAAyL,MAAA,SAAAlM,GACA,IAIA0C,EAJAoE,EAAA9G,EAAA7D,SAAA,EACA,OAAA2K,GAEA1L,EAAAgT,SAAApO,CAAA,IACA0C,EAAAiP,EAAAnL,MAAAM,EAAAlK,EAAAT,OAAA6D,CAAA,CAAA,EACApD,EAAAwB,OAAA4B,EAAA0C,EAAA,CAAA,EACA1C,EAAA0C,GAEAnC,KAAA2b,OAAApV,CAAA,EAAAihB,EAAAC,EAAAlhB,EAAA9G,CAAA,GANAO,KAAAwnB,EAAAJ,EAAA,EAAA,CAAA,CAOA,EAOAhW,EAAAlR,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAAD,EAAA1K,OAAA6D,CAAA,EACA,OAAA8G,EACAvG,KAAA2b,OAAApV,CAAA,EAAAihB,EAAAlhB,EAAAG,MAAAF,EAAA9G,CAAA,EACAO,KAAAwnB,EAAAJ,EAAA,EAAA,CAAA,CACA,EAOAhW,EAAAlR,UAAA6iB,KAAA,WAIA,OAHA/iB,KAAAmnB,OAAA,IAAAF,EAAAjnB,IAAA,EACAA,KAAAsX,KAAAtX,KAAAknB,KAAA,IAAAH,EAAAC,EAAA,EAAA,CAAA,EACAhnB,KAAAuG,IAAA,EACAvG,IACA,EAMAoR,EAAAlR,UAAAwnB,MAAA,WAUA,OATA1nB,KAAAmnB,QACAnnB,KAAAsX,KAAAtX,KAAAmnB,OAAA7P,KACAtX,KAAAknB,KAAAlnB,KAAAmnB,OAAAD,KACAlnB,KAAAuG,IAAAvG,KAAAmnB,OAAA5gB,IACAvG,KAAAmnB,OAAAnnB,KAAAmnB,OAAAjQ,OAEAlX,KAAAsX,KAAAtX,KAAAknB,KAAA,IAAAH,EAAAC,EAAA,EAAA,CAAA,EACAhnB,KAAAuG,IAAA,GAEAvG,IACA,EAMAoR,EAAAlR,UAAA8iB,OAAA,WACA,IAAA1L,EAAAtX,KAAAsX,KACA4P,EAAAlnB,KAAAknB,KACA3gB,EAAAvG,KAAAuG,IAOA,OANAvG,KAAA0nB,MAAA,EAAA/L,OAAApV,CAAA,EACAA,IACAvG,KAAAknB,KAAAhQ,KAAAI,EAAAJ,KACAlX,KAAAknB,KAAAA,EACAlnB,KAAAuG,KAAAA,GAEAvG,IACA,EAMAoR,EAAAlR,UAAAmd,OAAA,WAIA,IAHA,IAAA/F,EAAAtX,KAAAsX,KAAAJ,KACA/U,EAAAnC,KAAA+M,YAAA9G,MAAAjG,KAAAuG,GAAA,EACAnE,EAAA,EACAkV,GACAA,EAAA/b,GAAA+b,EAAApV,IAAAC,EAAAC,CAAA,EACAA,GAAAkV,EAAA/Q,IACA+Q,EAAAA,EAAAJ,KAGA,OAAA/U,CACA,EAEAiP,EAAAhB,EAAA,SAAAuX,GACAtW,EAAAsW,EACAvW,EAAAtE,OAAAA,EAAA,EACAuE,EAAAjB,EAAA,CACA,C,+BC/cAhV,EAAAR,QAAAyW,EAGA,IAAAD,EAAA9V,EAAA,EAAA,EAGAT,IAFAwW,EAAAnR,UAAApB,OAAAgO,OAAAsE,EAAAlR,SAAA,GAAA6M,YAAAsE,EAEA/V,EAAA,EAAA,GAQA,SAAA+V,IACAD,EAAAzW,KAAAqF,IAAA,CACA,CAuCA,SAAA4nB,EAAA1lB,EAAAC,EAAAC,GACAF,EAAAtG,OAAA,GACAf,EAAAyL,KAAAG,MAAAvE,EAAAC,EAAAC,CAAA,EACAD,EAAAsjB,UACAtjB,EAAAsjB,UAAAvjB,EAAAE,CAAA,EAEAD,EAAAsE,MAAAvE,EAAAE,CAAA,CACA,CA5CAiP,EAAAjB,EAAA,WAOAiB,EAAApL,MAAApL,EAAA8qB,EAEAtU,EAAAwW,iBAAAhtB,EAAAqgB,QAAArgB,EAAAqgB,OAAAhb,qBAAAwB,YAAA,QAAA7G,EAAAqgB,OAAAhb,UAAA0V,IAAAnb,KACA,SAAAyH,EAAAC,EAAAC,GACAD,EAAAyT,IAAA1T,EAAAE,CAAA,CAEA,EAEA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAA4lB,KACA5lB,EAAA4lB,KAAA3lB,EAAAC,EAAA,EAAAF,EAAAtG,MAAA,OACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAqF,EAAAtG,QACAuG,EAAAC,CAAA,IAAAF,EAAArF,CAAA,GACA,CACA,EAMAwU,EAAAnR,UAAAyL,MAAA,SAAAlM,GAGA,IAAA8G,GADA9G,EADA5E,EAAAgT,SAAApO,CAAA,EACA5E,EAAA6qB,EAAAjmB,EAAA,QAAA,EACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAA2b,OAAApV,CAAA,EACAA,GACAvG,KAAAwnB,EAAAnW,EAAAwW,iBAAAthB,EAAA9G,CAAA,EACAO,IACA,EAcAqR,EAAAnR,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAA1L,EAAAqgB,OAAA6M,WAAAtoB,CAAA,EAIA,OAHAO,KAAA2b,OAAApV,CAAA,EACAA,GACAvG,KAAAwnB,EAAAI,EAAArhB,EAAA9G,CAAA,EACAO,IACA,EAUAqR,EAAAjB,EAAA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    var defaultAlreadyEmitted = false;\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                // enum unknown values passthrough\n                if (values[keys[i]] === field.typeDefault && !defaultAlreadyEmitted) { gen\n                    (\"default:\")\n                        (\"if(typeof(d%s)===\\\"number\\\"){m%s=d%s;break}\", prop, prop, prop);\n                    if (!field.repeated) gen // fallback to default value only for\n                                             // arrays, to avoid leaving holes.\n                        (\"break\");           // for non-repeated fields, just ignore\n                    defaultAlreadyEmitted = true;\n                }\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length >= 0)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s\", prop, fieldIndex, prop, prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i: {\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\")\n            (\"}\");\n        // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.resolvedType.group\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n * @param {Object.<string,Object<string,*>>|undefined} [valuesOptions] The value options for this enum\n */\nfunction Enum(name, values, options, comment, comments, valuesOptions) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Values options, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this.valuesOptions = valuesOptions;\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"       , this.options,\n        \"valuesOptions\" , this.valuesOptions,\n        \"values\"        , this.values,\n        \"reserved\"      , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"       , keepComments ? this.comment : undefined,\n        \"comments\"      , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @param {Object.<string, *>|undefined} [options] Options, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment, options) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    if (options) {\n        if (this.valuesOptions === undefined)\n            this.valuesOptions = {};\n        this.valuesOptions[name] = options || null;\n    }\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n    if (this.valuesOptions)\n        delete this.valuesOptions[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    } else if (this.options && this.options.proto3_optional) {\n        // proto3 scalar value marked optional; should default to null\n        this.typeDefault = null;\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    util     = require(37),\n    OneOf    = require(25);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace|IOneOf}\n */\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type  || object instanceof OneOf || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(37);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set it's property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        syntax,\n        isProto3 = false;\n\n    var ptr = root;\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\n                target.push(readString());\n            else\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n        } while (skip(\",\", true));\n        var dummy = {options: undefined};\n        dummy.setOption = function(name, value) {\n          if (this.options === undefined) this.options = {};\n          this.options[name] = value;\n        };\n        ifBlock(\n            dummy,\n            function parseRange_block(token) {\n              /* istanbul ignore else */\n              if (token === \"option\") {\n                parseOption(dummy, token);  // skip\n                skip(\";\");\n              } else\n                throw illegal(token);\n            },\n            function parseRange_line() {\n              parseInlineOptions(dummy);  // skip\n            });\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-next-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        syntax = readString();\n        isProto3 = syntax === \"proto3\";\n\n        /* istanbul ignore if */\n        if (!isProto3 && syntax !== \"proto2\")\n            throw illegal(syntax, \"syntax\");\n\n        // Syntax is needed to understand the meaning of the optional field rule\n        // Otherwise the meaning is ambiguous between proto2 and proto3\n        root.setOption(\"syntax\", syntax);\n\n        skip(\";\");\n    }\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n        // Type names can consume multiple tokens, in multiple variants:\n        //    package.subpackage   field       tokens: \"package.subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package . subpackage field       tokens: \"package\" \".\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package.  subpackage field       tokens: \"package.\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package  .subpackage field       tokens: \"package\" \".subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        // Keep reading tokens until we get a type name with no period at the end,\n        // and the next token does not start with a period.\n        while (type.endsWith(\".\") || peek().startsWith(\".\")) {\n            type += next();\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\n        // all known packable types and anything that could be an enum (= is not a basic type).\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\n    }\n\n    function parseGroup(parent, rule) {\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"message\":\n                    parseType(type, token);\n                    break;\n\n                case \"enum\":\n                    parseEnum(type, token);\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {\n                options: undefined\n            };\n        dummy.setOption = function(name, value) {\n            if (this.options === undefined)\n                this.options = {};\n            this.options[name] = value;\n        };\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment, dummy.options);\n    }\n\n    function parseOption(parent, token) {\n        var isCustom = skip(\"(\", true);\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token;\n        var option = name;\n        var propName;\n\n        if (isCustom) {\n            skip(\")\");\n            name = \"(\" + name + \")\";\n            option = name;\n            token = peek();\n            if (fqTypeRefRe.test(token)) {\n                propName = token.slice(1); //remove '.' before property name\n                name += token;\n                next();\n            }\n        }\n        skip(\"=\");\n        var optionValue = parseOptionValue(parent, name);\n        setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        // { a: \"foo\" b { c: \"bar\" } }\n        if (skip(\"{\", true)) {\n            var objectResult = {};\n\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next())) {\n                    throw illegal(token, \"name\");\n                }\n                if (token === null) {\n                  throw illegal(token, \"end of input\");\n                }\n\n                var value;\n                var propName = token;\n\n                skip(\":\", true);\n\n                if (peek() === \"{\")\n                    value = parseOptionValue(parent, name + \".\" + token);\n                else if (peek() === \"[\") {\n                    // option (my_option) = {\n                    //     repeated_value: [ \"foo\", \"bar\" ]\n                    // };\n                    value = [];\n                    var lastValue;\n                    if (skip(\"[\", true)) {\n                        do {\n                            lastValue = readValue(true);\n                            value.push(lastValue);\n                        } while (skip(\",\", true));\n                        skip(\"]\");\n                        if (typeof lastValue !== \"undefined\") {\n                            setOption(parent, name + \".\" + token, lastValue);\n                        }\n                    }\n                } else {\n                    value = readValue(true);\n                    setOption(parent, name + \".\" + token, value);\n                }\n\n                var prevValue = objectResult[propName];\n\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n\n                objectResult[propName] = value;\n\n                // Semicolons and commas can be optional\n                skip(\",\", true);\n                skip(\";\", true);\n            }\n\n            return objectResult;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token))\n                return;\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (isProto3) {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"option\":\n\n                parseOption(ptr, token);\n                skip(\";\");\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         syntax       : syntax,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n\n    if (start === end) { // fix for IE 10/Win8 and others' subarray returning array of size 1\n        var nativeBuffer = util.Buffer;\n        return nativeBuffer\n            ? nativeBuffer.alloc(0)\n            : new this.buf.constructor(0);\n    }\n    return this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        if (sync)\n            throw err;\n        var cb = callback;\n        callback = null;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n        filename = getBundledFileName(filename) || filename;\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        //do not allow to extend same field twice to prevent the error\n        if (extendedType.get(sisterField.name)) {\n            return true;\n        }\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        lastCommentLine = 0,\n        comments = {};\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        var comment = {\n            type: source.charAt(start++),\n            lineEmpty: false,\n            leading: isLeading,\n        };\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                comment.lineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        comment.text = lines\n            .join(\"\\n\")\n            .trim();\n\n        comments[line] = comment;\n        lastCommentLine = line;\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        var isComment = /^\\s*\\/\\//.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                            // Trailing comment cannot not be multi-line,\n                            // so leading comment state should be reset to handle potential next comments\n                            isLeadingComment = true;\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset - 1)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                                if (!isLeadingComment) {\n                                    // Trailing comment cannot not be multi-line\n                                    break;\n                                }\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                            isLeadingComment = true;\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                        isLeadingComment = true;\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        var comment;\n        if (trailingLine === undefined) {\n            comment = comments[line - 1];\n            delete comments[line - 1];\n            if (comment && (alternateCommentMode || comment.type === \"*\" || comment.lineEmpty)) {\n                ret = comment.leading ? comment.text : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (lastCommentLine < trailingLine) {\n                peek();\n            }\n            comment = comments[trailingLine];\n            delete comments[trailingLine];\n            if (comment && !comment.lineEmpty && (alternateCommentMode || comment.type === \"/\")) {\n                ret = comment.leading ? null : comment.text;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {Array.<number[]|string>} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\" || part === \"prototype\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get: function get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value: function value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].slice(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.slice(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}