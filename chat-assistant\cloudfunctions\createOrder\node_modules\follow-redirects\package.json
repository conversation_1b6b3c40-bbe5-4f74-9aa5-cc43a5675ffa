{"name": "follow-redirects", "version": "1.15.9", "description": "HTTP and HTTPS modules that follow redirects.", "license": "MIT", "main": "index.js", "files": ["*.js"], "engines": {"node": ">=4.0"}, "scripts": {"lint": "eslint *.js test", "test": "nyc mocha"}, "repository": {"type": "git", "url": "git+ssh://**************/follow-redirects/follow-redirects.git"}, "homepage": "https://github.com/follow-redirects/follow-redirects", "bugs": {"url": "https://github.com/follow-redirects/follow-redirects/issues"}, "keywords": ["http", "https", "url", "redirect", "client", "location", "utility"], "author": "<PERSON><PERSON> <<EMAIL>> (https://ruben.verborgh.org/)", "contributors": ["<PERSON> <<EMAIL>> (http://www.syskall.com)", "<PERSON> <<EMAIL>>"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "peerDependenciesMeta": {"debug": {"optional": true}}, "devDependencies": {"concat-stream": "^2.0.0", "eslint": "^5.16.0", "express": "^4.16.4", "lolex": "^3.1.0", "mocha": "^6.0.2", "nyc": "^14.1.1"}}