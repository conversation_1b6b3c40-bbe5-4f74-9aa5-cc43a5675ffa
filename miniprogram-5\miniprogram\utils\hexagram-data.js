// 梅花易数八卦万物属类数据
// 基于《梅花易数-宋-邵雍》原著整理

// 八卦基本信息
export const TRIGRAMS = {
  1: { name: '乾', symbol: '☰', element: '金', direction: '西北', number: 1 },
  2: { name: '兑', symbol: '☱', element: '金', direction: '西', number: 2 },
  3: { name: '离', symbol: '☲', element: '火', direction: '南', number: 3 },
  4: { name: '震', symbol: '☳', element: '木', direction: '东', number: 4 },
  5: { name: '巽', symbol: '☴', element: '木', direction: '东南', number: 5 },
  6: { name: '坎', symbol: '☵', element: '水', direction: '北', number: 6 },
  7: { name: '艮', symbol: '☶', element: '土', direction: '东北', number: 7 },
  8: { name: '坤', symbol: '☷', element: '土', direction: '西南', number: 8 }
};

// 八卦万物属类（基于知识库原文）
export const TRIGRAM_ATTRIBUTES = {
  1: { // 乾卦
    nature: ['天', '父', '老人', '官贵'],
    body: ['头', '骨', '肺'],
    animals: ['马', '天鹅', '狮', '象'],
    objects: ['金宝', '珠玉', '水果', '圆物', '冠', '镜', '刚物'],
    colors: ['大赤色', '玄色', '金色'],
    directions: ['西北'],
    time: ['秋', '九十月', '戌亥年月日时'],
    weather: ['天', '冰', '雹', '霰'],
    personality: ['刚健武勇', '果决', '多动少静', '高上']
  },
  2: { // 兑卦
    nature: ['泽', '少女', '巫'],
    body: ['舌', '肺', '口齿'],
    animals: ['羊'],
    objects: ['金刃', '金器', '乐器', '带口之器', '毁折之物', '废缺之物'],
    colors: ['白色'],
    directions: ['西'],
    personality: ['喜悦', '能言', '巧舌']
  },
  3: { // 离卦
    nature: ['火', '中女', '日'],
    body: ['目', '心'],
    animals: ['雉', '龟', '蟹', '蚌', '鳖'],
    objects: ['文书', '干戈', '甲胄', '槁木', '炉', '有壳之物'],
    colors: ['红赤紫色', '火色'],
    directions: ['南'],
    weather: ['火', '电', '霓霞'],
    personality: ['文明', '聪慧', '美丽']
  },
  4: { // 震卦
    nature: ['雷', '长男'],
    body: ['足', '发'],
    animals: ['龙', '百虫', '蛇'],
    objects: ['竹', '萑苇', '乐器', '草木', '树', '木核', '柴'],
    colors: ['青碧绿色'],
    directions: ['东'],
    weather: ['雷'],
    personality: ['动', '奋发', '急躁']
  },
  5: { // 巽卦
    nature: ['风', '长女', '僧尼'],
    body: ['股', '眼'],
    animals: ['鸡', '百禽'],
    objects: ['百草', '臼', '绳', '羽毛', '帆', '扇', '枝叶', '直物', '工巧之器'],
    colors: ['青碧绿色'],
    directions: ['东南'],
    weather: ['风'],
    personality: ['柔顺', '进退', '工巧']
  },
  6: { // 坎卦
    nature: ['水', '中男', '工'],
    body: ['耳', '血', '肾'],
    animals: ['豕', '狐', '鱼'],
    objects: ['弓轮', '栋', '丛棘', '桎梏', '水族', '盐', '酒', '有核之物'],
    colors: ['黑色'],
    directions: ['北'],
    weather: ['水', '雨', '雪'],
    personality: ['险陷', '智慧', '劳苦']
  },
  7: { // 艮卦
    nature: ['山', '土', '少男', '童子'],
    body: ['手指', '鼻'],
    animals: ['狗', '鼠', '虎', '狐'],
    objects: ['径路', '门阙', '果', '瓜', '木生之物', '藤生之物'],
    colors: ['黄色'],
    directions: ['东北'],
    personality: ['静止', '诚实', '笃厚']
  },
  8: { // 坤卦
    nature: ['地', '母', '老妇', '农夫', '乡人', '众人'],
    body: ['腹', '脾', '胃', '肉'],
    animals: ['牛', '百兽', '牝马'],
    objects: ['土', '布帛', '文章', '舆辇', '方物', '瓦器', '黍稷', '书', '米', '谷'],
    colors: ['黄色', '黑色'],
    directions: ['西南'],
    weather: ['云阴', '雾气'],
    personality: ['柔顺', '懦弱', '众多', '吝啬']
  }
};

// 五行生克关系
export const FIVE_ELEMENTS = {
  generate: { // 相生
    '金': '水',
    '水': '木', 
    '木': '火',
    '火': '土',
    '土': '金'
  },
  overcome: { // 相克
    '金': '木',
    '木': '土',
    '土': '水',
    '水': '火',
    '火': '金'
  }
};

// 卦气旺衰
export const TRIGRAM_SEASONS = {
  spring: { // 春季旺
    prosperous: [4, 5], // 震、巽木旺于春
    declining: [8, 7]   // 春坤、艮衰
  },
  summer: { // 夏季旺
    prosperous: [3],    // 离火旺于夏
    declining: [1, 2]   // 夏乾、兑衰
  },
  autumn: { // 秋季旺
    prosperous: [1, 2], // 乾、兑金旺于秋
    declining: [4, 5]   // 秋震、巽衰
  },
  winter: { // 冬季旺
    prosperous: [6],    // 坎水旺于冬
    declining: [3]      // 冬离衰
  },
  fourSeasons: { // 四季土旺
    prosperous: [8, 7], // 坤、艮土旺于辰戌丑未月
    declining: [6]      // 辰戌丑未坎衰
  }
};

// 体用生克判断
export function analyzeBodyUse(bodyTrigram, useTrigram) {
  const bodyElement = TRIGRAMS[bodyTrigram].element;
  const useElement = TRIGRAMS[useTrigram].element;
  
  if (FIVE_ELEMENTS.generate[useElement] === bodyElement) {
    return { relationship: 'generate', result: '吉', description: '用生体，吉利' };
  } else if (FIVE_ELEMENTS.overcome[useElement] === bodyElement) {
    return { relationship: 'overcome', result: '凶', description: '用克体，不利' };
  } else if (FIVE_ELEMENTS.generate[bodyElement] === useElement) {
    return { relationship: 'drain', result: '耗', description: '体生用，耗泄' };
  } else if (FIVE_ELEMENTS.overcome[bodyElement] === useElement) {
    return { relationship: 'control', result: '制', description: '体克用，有制' };
  } else {
    return { relationship: 'same', result: '和', description: '同类相助' };
  }
}

// 获取卦象的基本含义
export function getTrigramMeaning(trigramNumber) {
  return TRIGRAMS[trigramNumber] || null;
}

// 获取卦象的万物属类
export function getTrigramAttributes(trigramNumber) {
  return TRIGRAM_ATTRIBUTES[trigramNumber] || null;
}

// 根据当前季节判断卦气旺衰
export function getTrigramStrength(trigramNumber, season = null) {
  if (!season) {
    const month = new Date().getMonth() + 1;
    if (month >= 3 && month <= 5) season = 'spring';
    else if (month >= 6 && month <= 8) season = 'summer';
    else if (month >= 9 && month <= 11) season = 'autumn';
    else season = 'winter';
  }
  
  const seasonData = TRIGRAM_SEASONS[season];
  if (seasonData.prosperous.includes(trigramNumber)) {
    return { strength: 'prosperous', description: '当令而旺' };
  } else if (seasonData.declining.includes(trigramNumber)) {
    return { strength: 'declining', description: '失令而衰' };
  } else {
    return { strength: 'neutral', description: '平和' };
  }
}

// 完整64卦名称对照表
export const HEXAGRAM_NAMES = {
  '11': '乾为天',
  '12': '天泽履',
  '13': '天火同人',
  '14': '天雷无妄',
  '15': '天风姤',
  '16': '天水讼',
  '17': '天山遁',
  '18': '天地否',
  '21': '泽天夬',
  '22': '兑为泽',
  '23': '泽火革',
  '24': '泽雷随',
  '25': '泽风大过',
  '26': '泽水困',
  '27': '泽山咸',
  '28': '泽地萃',
  '31': '火天大有',
  '32': '火泽睽',
  '33': '离为火',
  '34': '火雷噬嗑',
  '35': '火风鼎',
  '36': '火水未济',
  '37': '火山旅',
  '38': '火地晋',
  '41': '雷天大壮',
  '42': '雷泽归妹',
  '43': '雷火丰',
  '44': '震为雷',
  '45': '雷风恒',
  '46': '雷水解',
  '47': '雷山小过',
  '48': '雷地豫',
  '51': '风天小畜',
  '52': '风泽中孚',
  '53': '风火家人',
  '54': '风雷益',
  '55': '巽为风',
  '56': '风水涣',
  '57': '风山渐',
  '58': '风地观',
  '61': '水天需',
  '62': '水泽节',
  '63': '水火既济',
  '64': '水雷屯',
  '65': '水风井',
  '66': '坎为水',
  '67': '水山蹇',
  '68': '水地比',
  '71': '山天大畜',
  '72': '山泽损',
  '73': '山火贲',
  '74': '山雷颐',
  '75': '山风蛊',
  '76': '山水蒙',
  '77': '艮为山',
  '78': '山地剥',
  '81': '地天泰',
  '82': '地泽临',
  '83': '地火明夷',
  '84': '地雷复',
  '85': '地风升',
  '86': '地水师',
  '87': '地山谦',
  '88': '坤为地'
};

// 64卦基本含义数据库
export const HEXAGRAM_MEANINGS = {
  '11': { name: '乾为天', meaning: '刚健中正', nature: '大吉大利', advice: '自强不息，刚健有为' },
  '12': { name: '天泽履', meaning: '履险如夷', nature: '小吉', advice: '谨慎行事，礼义为先' },
  '13': { name: '天火同人', meaning: '同心协力', nature: '吉', advice: '团结合作，志同道合' },
  '14': { name: '天雷无妄', meaning: '顺天应人', nature: '大吉', advice: '顺应自然，不可妄为' },
  '15': { name: '天风姤', meaning: '不期而遇', nature: '凶', advice: '防范小人，谨慎交往' },
  '16': { name: '天水讼', meaning: '争讼不利', nature: '凶', advice: '避免争执，和解为上' },
  '17': { name: '天山遁', meaning: '退避三舍', nature: '小吉', advice: '适时退让，保全自身' },
  '18': { name: '天地否', meaning: '闭塞不通', nature: '凶', advice: '时运不济，静待转机' },
  '22': { name: '兑为泽', meaning: '喜悦和谐', nature: '吉', advice: '和悦待人，喜庆有余' },
  '33': { name: '离为火', meaning: '光明磊落', nature: '吉', advice: '文明进取，光明正大' },
  '44': { name: '震为雷', meaning: '震动奋发', nature: '吉', advice: '奋发图强，震撼人心' },
  '55': { name: '巽为风', meaning: '顺风而行', nature: '小吉', advice: '顺势而为，柔顺进取' },
  '66': { name: '坎为水', meaning: '险中求进', nature: '凶', advice: '险阻重重，需要智慧' },
  '77': { name: '艮为山', meaning: '止于至善', nature: '小吉', advice: '适可而止，稳重行事' },
  '88': { name: '坤为地', meaning: '厚德载物', nature: '大吉', advice: '包容万物，柔顺承载' },
  '81': { name: '地天泰', meaning: '天地交泰', nature: '大吉', advice: '通泰顺利，万事亨通' },
  '18': { name: '天地否', meaning: '天地不交', nature: '凶', advice: '闭塞不通，需待时机' },
  '63': { name: '水火既济', meaning: '功德圆满', nature: '吉', advice: '事业有成，但需谨慎' },
  '36': { name: '火水未济', meaning: '事未完成', nature: '凶', advice: '时机未到，继续努力' }
};

// 获取复卦名称
export function getHexagramName(upperTrigram, lowerTrigram) {
  const key = `${upperTrigram}${lowerTrigram}`;
  return HEXAGRAM_NAMES[key] || `${TRIGRAMS[upperTrigram].name}${TRIGRAMS[lowerTrigram].name}`;
}

// 获取卦象的基本含义
export function getHexagramMeaning(upperTrigram, lowerTrigram) {
  const key = `${upperTrigram}${lowerTrigram}`;
  return HEXAGRAM_MEANINGS[key] || null;
}

// 生成互卦（主卦2、3、4爻为上卦，3、4、5爻为下卦）
export function generateMutualHexagram(upperTrigram, lowerTrigram) {
  // 将八卦转换为三爻（1为阳爻，0为阴爻）
  const trigramToLines = {
    1: [1, 1, 1], // 乾 ☰
    2: [0, 1, 1], // 兑 ☱
    3: [1, 0, 1], // 离 ☲
    4: [0, 0, 1], // 震 ☳
    5: [1, 1, 0], // 巽 ☴
    6: [0, 1, 0], // 坎 ☵
    7: [1, 0, 0], // 艮 ☶
    8: [0, 0, 0]  // 坤 ☷
  };

  // 将三爻转换为八卦
  const linesToTrigram = {
    '111': 1, '011': 2, '101': 3, '001': 4,
    '110': 5, '010': 6, '100': 7, '000': 8
  };

  const upperLines = trigramToLines[upperTrigram];
  const lowerLines = trigramToLines[lowerTrigram];

  // 六爻排列：下卦为1、2、3爻，上卦为4、5、6爻
  const sixLines = [...lowerLines, ...upperLines];

  // 互卦上卦：2、3、4爻
  const mutualUpper = [sixLines[1], sixLines[2], sixLines[3]];
  // 互卦下卦：3、4、5爻
  const mutualLower = [sixLines[2], sixLines[3], sixLines[4]];

  const mutualUpperKey = mutualUpper.join('');
  const mutualLowerKey = mutualLower.join('');

  return {
    upper: linesToTrigram[mutualUpperKey],
    lower: linesToTrigram[mutualLowerKey]
  };
}

// 生成变卦（动爻变化后的卦象）
export function generateChangedHexagram(upperTrigram, lowerTrigram, changeLine) {
  // 将八卦转换为三爻
  const trigramToLines = {
    1: [1, 1, 1], // 乾 ☰
    2: [0, 1, 1], // 兑 ☱
    3: [1, 0, 1], // 离 ☲
    4: [0, 0, 1], // 震 ☳
    5: [1, 1, 0], // 巽 ☴
    6: [0, 1, 0], // 坎 ☵
    7: [1, 0, 0], // 艮 ☶
    8: [0, 0, 0]  // 坤 ☷
  };

  // 将三爻转换为八卦
  const linesToTrigram = {
    '111': 1, '011': 2, '101': 3, '001': 4,
    '110': 5, '010': 6, '100': 7, '000': 8
  };

  const upperLines = trigramToLines[upperTrigram];
  const lowerLines = trigramToLines[lowerTrigram];

  // 六爻排列：下卦为1、2、3爻，上卦为4、5、6爻
  const sixLines = [...lowerLines, ...upperLines];

  // 变动指定爻（1-6爻对应数组索引0-5）
  const changeIndex = changeLine - 1;
  sixLines[changeIndex] = sixLines[changeIndex] === 1 ? 0 : 1;

  // 重新组合为上下卦
  const newLowerLines = sixLines.slice(0, 3);
  const newUpperLines = sixLines.slice(3, 6);

  const newLowerKey = newLowerLines.join('');
  const newUpperKey = newUpperLines.join('');

  return {
    upper: linesToTrigram[newUpperKey],
    lower: linesToTrigram[newLowerKey]
  };
}
