# @cloudbase/database

[![NPM Version](https://img.shields.io/npm/v/@cloudbase/database.svg?style=flat)](https://www.npmjs.com/package/@cloudbase/database)
[![](https://img.shields.io/npm/dt/@cloudbase/database.svg)](https://www.npmjs.com/package/@cloudbase/database)

## 介绍

提供 TCB JS SDK 操作数据库的接口。

## 安装

```
yarn add @cloudbase/database
```

## 使用

使用 `@cloudbase/database` 时，需要提供发送请求的类 `reqClass`。

```js
// index.js

const database = require('@cloudbase/database/').Db;
const Request = require('./request');

class Tcb {
  ...
  database(dbConfig) {
    database.reqClass = Request;
    return new database(dbConfig);
  }
}
```

实现 `Request` 类，需要提供异步的 `send` 方法。

```js
// request.js

// 进一步处理，鉴权等...
const requestHandler = require('requestHandler')

class Request {
  constructor(config) {
    this.config = config
  }

  async send(action, data) {
    const params = Object.assign({}, data, {
      action
    })

    const slowQueryWarning = setTimeout(() => {
      console.warn(
        'Database operation is longer than 3s. Please check query performance and your network environment.'
      )
    }, 3000)

    try {
      return await requestHandler({
        timeout: this.config.timeout,
        config: this.config.config,
        params,
        method: 'post',
        headers: {
          'content-type': 'application/json'
        }
      })
    } finally {
      clearTimeout(slowQueryWarning)
    }
  }
}

module.exports = Request
```

请求数据样例

```json
{
  "url": "https://tcb-admin.tencentcloudapi.com/admin?eventId=1554896261428_92044",
  "method": "post",
  "timeout": 15000,
  "headers": { "user-agent": "tcb-admin-sdk/1.4.6", "content-type": "application/json" },
  "body": {
    "collectionName": "coll-1",
    "action": "database.addCollection",
    "envName": "base-830cab",
    "timestamp": 1554896261428,
    "eventId": "1554896261428_92044",
    "authorization": "q-sign-algorithm=sha1&q-ak=xxx&q-sign-time=1554896260;1554897160&q-key-time=1554896260;1554897160&q-header-list=content-type;user-agent&q-url-param-list=action;collectionname;envname;eventid;timestamp&q-signature=xxxxx",
    "sdk_version": "1.4.6"
  },
  "json": true
}
```

## 开发指南

### 注意

当前 database 库是通过分支来控制了两个不同的版本，主干版提供给@cloudbase/js-sdk 库使用，feature/support_db_2.0 分支提供给@cloudbase/node-sdk 库使用。

两个分支区别：
support_db_2.0 分支进行了数据库接口的升级，将原有的 json 协议转换为 mongo 支持的 bson 协议处理，目的是解决旧接口存在的问题，描述如下

- 日期对象 是走约定协议处理，即转换为 {$date: timestamp}，这种方式应摒弃(EJSON 协议可解决)
- 无法支持 null, NaN 等特殊类型处理(EJSON 协议可解决)
- serverDate 这种自定义类型的实现，是继续保留 还是摒弃
- 接口实现混杂，普通 CRUD 接口均走 flexdb，而聚合，事务又用 runcommand 方式
- 原插入文档接口返回 docId 形式混杂

目前仅针对服务端 sdk @cloudbase/node-sdk 完成了升级，而客户端 sdk @cloudbase/js-sdk 以及 小程序端 SDK 仍保留为旧接口形式。

### 开发及发布事项

1. 数据库接口的改动务必补充测试用例验证
2. 发布时，约定 feature/support_db_2.0 分支上发布正式版（for @cloudbase/node-sdk）使用，master 分支上发布 beta 版 (for @cloudbase/js-sdk)使用。两 sdk 均通过锁版本方式依赖该库，不会受自动更新依赖影响。
