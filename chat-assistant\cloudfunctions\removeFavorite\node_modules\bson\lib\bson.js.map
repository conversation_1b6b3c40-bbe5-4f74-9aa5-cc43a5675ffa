{"version": 3, "file": "bson.js", "sourceRoot": "", "sources": ["../src/bson.ts"], "names": [], "mappings": ";;;;AAAA,iCAAgC;AAChC,mCAAwC;AA+EtC,uFA/EO,eAAM,OA+EP;AAEN,qFAjFe,aAAI,OAiFf;AAhFN,+BAA8B;AA0E5B,qFA1EO,WAAI,OA0EP;AAzEN,mCAAiC;AA4E/B,sFA5EO,cAAK,OA4EP;AA3EP,2CAA0C;AAsFxC,2FAtFO,uBAAU,OAsFP;AArFZ,mCAAkC;AAgFhC,uFAhFO,eAAM,OAgFP;AA/ER,iDAA+C;AAC/C,iDAAwC;AACxC,mCAAiC;AA8E/B,sFA9EO,cAAK,OA8EP;AA7EP,+BAA8B;AA0E5B,qFA1EO,WAAI,OA0EP;AAzEN,6BAA4B;AAmE1B,oFAnEO,SAAG,OAmEP;AAlEL,qCAAmC;AA6EjC,uFA7EO,gBAAM,OA6EP;AA5ER,qCAAmC;AA2EjC,uFA3EO,gBAAM,OA2EP;AA1ER,uCAAsC;AAoEpC,yFApEO,mBAAQ,OAoEP;AAaI,yFAjFL,mBAAQ,OAiFK;AAhFtB,iCAAmD;AACnD,0DAA6F;AAC7F,sBAAsB;AACtB,sDAA+F;AAC/F,kDAA2F;AAC3F,mCAAsC;AAsEpC,2FAtEO,mBAAU,OAsEP;AArEZ,mCAAsC;AA0DpC,2FA1DO,mBAAU,OA0DP;AAzDZ,yCAAwC;AA+DtC,0FA/DO,qBAAS,OA+DP;AA5DX,yCAmCqB;AAlCnB,2HAAA,8BAA8B,OAAA;AAC9B,wHAAA,2BAA2B,OAAA;AAC3B,yHAAA,4BAA4B,OAAA;AAC5B,oHAAA,uBAAuB,OAAA;AACvB,6HAAA,gCAAgC,OAAA;AAChC,qHAAA,wBAAwB,OAAA;AACxB,yHAAA,4BAA4B,OAAA;AAC5B,0HAAA,6BAA6B,OAAA;AAC7B,uHAAA,0BAA0B,OAAA;AAC1B,4GAAA,eAAe,OAAA;AACf,6GAAA,gBAAgB,OAAA;AAChB,8GAAA,iBAAiB,OAAA;AACjB,2GAAA,cAAc,OAAA;AACd,mHAAA,sBAAsB,OAAA;AACtB,2GAAA,cAAc,OAAA;AACd,gHAAA,mBAAmB,OAAA;AACnB,iHAAA,oBAAoB,OAAA;AACpB,0GAAA,aAAa,OAAA;AACb,2GAAA,cAAc,OAAA;AACd,8GAAA,iBAAiB,OAAA;AACjB,8GAAA,iBAAiB,OAAA;AACjB,2GAAA,cAAc,OAAA;AACd,6GAAA,gBAAgB,OAAA;AAChB,6GAAA,gBAAgB,OAAA;AAChB,0GAAA,aAAa,OAAA;AACb,6GAAA,gBAAgB,OAAA;AAChB,6GAAA,gBAAgB,OAAA;AAChB,6GAAA,gBAAgB,OAAA;AAChB,gHAAA,mBAAmB,OAAA;AACnB,gHAAA,mBAAmB,OAAA;AACnB,2GAAA,cAAc,OAAA;AACd,2GAAA,cAAc,OAAA;AACd,2GAAA,cAAc,OAAA;AACd,2GAAA,cAAc,OAAA;AAMhB,iDAAwC;AAA/B,sGAAA,KAAK,OAAA;AASd,yCAAwD;AAA/C,sHAAA,yBAAyB,OAAA;AAuBlC,iCAAmD;AAA1C,kGAAA,SAAS,OAAA;AAAE,sGAAA,aAAa,OAAA;AAQjC,gBAAgB;AAChB,mBAAmB;AACnB,IAAM,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAEjC,kDAAkD;AAClD,IAAI,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAEnC;;;;;GAKG;AACH,SAAgB,qBAAqB,CAAC,IAAY;IAChD,qDAAqD;IACrD,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE;QACxB,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC7B;AACH,CAAC;AALD,sDAKC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CAAC,MAAgB,EAAE,OAA8B;IAA9B,wBAAA,EAAA,YAA8B;IACxE,qBAAqB;IACrB,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;IACrF,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;IAChF,IAAM,qBAAqB,GACzB,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,OAAO,CAAC;IAE9F,qDAAqD;IACrD,IAAI,MAAM,CAAC,MAAM,GAAG,qBAAqB,EAAE;QACzC,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;KAC9C;IAED,uBAAuB;IACvB,IAAM,kBAAkB,GAAG,IAAA,0BAAiB,EAC1C,MAAM,EACN,MAAM,EACN,SAAS,EACT,CAAC,EACD,CAAC,EACD,kBAAkB,EAClB,eAAe,EACf,EAAE,CACH,CAAC;IAEF,0BAA0B;IAC1B,IAAM,cAAc,GAAG,eAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAExD,gCAAgC;IAChC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;IAEzD,oBAAoB;IACpB,OAAO,cAAc,CAAC;AACxB,CAAC;AAnCD,8BAmCC;AAED;;;;;;;;GAQG;AACH,SAAgB,2BAA2B,CACzC,MAAgB,EAChB,WAAmB,EACnB,OAA8B;IAA9B,wBAAA,EAAA,YAA8B;IAE9B,qBAAqB;IACrB,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;IACrF,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;IAChF,IAAM,UAAU,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAEzE,uBAAuB;IACvB,IAAM,kBAAkB,GAAG,IAAA,0BAAiB,EAC1C,MAAM,EACN,MAAM,EACN,SAAS,EACT,CAAC,EACD,CAAC,EACD,kBAAkB,EAClB,eAAe,CAChB,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAC;IAE5D,mBAAmB;IACnB,OAAO,UAAU,GAAG,kBAAkB,GAAG,CAAC,CAAC;AAC7C,CAAC;AA3BD,kEA2BC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CACzB,MAA8C,EAC9C,OAAgC;IAAhC,wBAAA,EAAA,YAAgC;IAEhC,OAAO,IAAA,0BAAmB,EAAC,MAAM,YAAY,eAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAA,4BAAY,EAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AAChG,CAAC;AALD,kCAKC;AAQD;;;;;;GAMG;AACH,SAAgB,mBAAmB,CACjC,MAAgB,EAChB,OAAwC;IAAxC,wBAAA,EAAA,YAAwC;IAExC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IAExB,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC;IAEhF,OAAO,IAAA,oCAA2B,EAAC,MAAM,EAAE,kBAAkB,EAAE,eAAe,CAAC,CAAC;AAClF,CAAC;AAZD,kDAYC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,iBAAiB,CAC/B,IAA4C,EAC5C,UAAkB,EAClB,iBAAyB,EACzB,SAAqB,EACrB,aAAqB,EACrB,OAA2B;IAE3B,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,gCAAgC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EACpD,OAAO,CACR,CAAC;IACF,IAAM,UAAU,GAAG,IAAA,4BAAY,EAAC,IAAI,CAAC,CAAC;IAEtC,IAAI,KAAK,GAAG,UAAU,CAAC;IACvB,0BAA0B;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;QAC1C,4BAA4B;QAC5B,IAAM,IAAI,GACR,UAAU,CAAC,KAAK,CAAC;YACjB,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7B,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAChC,4BAA4B;QAC5B,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9B,mCAAmC;QACnC,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,IAAA,0BAAmB,EAAC,UAAU,EAAE,eAAe,CAAC,CAAC;QAChF,oCAAoC;QACpC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;KACtB;IAED,sEAAsE;IACtE,OAAO,KAAK,CAAC;AACf,CAAC;AAjCD,8CAiCC;AAED;;;;;;;GAOG;AACH,IAAM,IAAI,GAAG;IACX,MAAM,iBAAA;IACN,IAAI,aAAA;IACJ,KAAK,gBAAA;IACL,UAAU,yBAAA;IACV,MAAM,iBAAA;IACN,KAAK,gBAAA;IACL,IAAI,aAAA;IACJ,IAAI,eAAA;IACJ,GAAG,WAAA;IACH,MAAM,kBAAA;IACN,MAAM,kBAAA;IACN,QAAQ,qBAAA;IACR,QAAQ,EAAE,mBAAQ;IAClB,UAAU,qBAAA;IACV,UAAU,qBAAA;IACV,SAAS,uBAAA;IACT,KAAK,uBAAA;IACL,qBAAqB,uBAAA;IACrB,SAAS,WAAA;IACT,2BAA2B,6BAAA;IAC3B,WAAW,aAAA;IACX,mBAAmB,qBAAA;IACnB,iBAAiB,mBAAA;IACjB,SAAS,mBAAA;IACT,aAAa,uBAAA;CACd,CAAC;AACF,kBAAe,IAAI,CAAC"}