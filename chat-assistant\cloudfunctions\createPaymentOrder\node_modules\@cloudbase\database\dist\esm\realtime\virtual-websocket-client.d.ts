import { IResponseMessage } from '../typings/realtime';
import { RealtimeListener } from './listener';
import { IWSSendOptions } from './websocket-client';
import { IWatchOptions } from '../typings/index';
interface IVirtualWebSocketClientConstructorOptions extends IWatchOptions {
    envId?: string;
    collectionName: string;
    query: string;
    limit?: number;
    orderBy?: Record<string, string>;
    send: <T = any>(opts: IWSSendOptions) => Promise<T>;
    login: (envId?: string, refresh?: boolean) => Promise<any>;
    isWSConnected: () => boolean;
    onceWSConnected: () => Promise<void>;
    getWaitExpectedTimeoutLength: () => number;
    onWatchStart: (client: VirtualWebSocketClient, queryID: string) => void;
    onWatchClose: (client: VirtualWebSocketClient, queryID: string) => void;
    debug?: boolean;
}
export declare class VirtualWebSocketClient {
    watchId: string;
    private envId?;
    private collectionName;
    private query;
    private limit;
    private orderBy;
    private send;
    private login;
    private isWSConnected;
    private onceWSConnected;
    private getWaitExpectedTimeoutLength;
    private onWatchStart;
    private onWatchClose;
    private debug?;
    listener: RealtimeListener;
    private watchStatus;
    private _availableRetries;
    private _ackTimeoutId?;
    private _initWatchPromise?;
    private _rebuildWatchPromise?;
    private sessionInfo?;
    private _waitExpectedTimeoutId?;
    constructor(options: IVirtualWebSocketClientConstructorOptions);
    private _login;
    private initWatch;
    private rebuildWatch;
    private handleWatchEstablishmentError;
    private closeWatch;
    private scheduleSendACK;
    private clearACKSchedule;
    private sendACK;
    private handleCommonError;
    private useRetryTicket;
    private handleServerEvents;
    private _handleServerEvents;
    private _postHandleServerEventsValidityCheck;
    private clearWaitExpectedEvent;
    onMessage(msg: IResponseMessage): void;
    closeWithError(error: any): void;
    pause(): void;
    resume(): Promise<void>;
}
export {};
