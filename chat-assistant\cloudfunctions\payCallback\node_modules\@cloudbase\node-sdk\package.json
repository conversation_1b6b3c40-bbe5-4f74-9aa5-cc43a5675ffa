{"name": "@cloudbase/node-sdk", "version": "2.10.0", "description": "tencent cloud base server sdk for node.js", "main": "lib/index.js", "scripts": {"eslint": "eslint \"./**/*.ts\"", "eslint-fix": "eslint --fix \"./**/*.ts\"", "build": "rm -rf lib/* && npm run tsc", "tsc": "tsc -p tsconfig.json", "tsc:w": "tsc -p tsconfig.json -w", "tstest": "mocha --timeout 5000 --require espower-typescript/guess test/**/*.test.ts", "test": "jest --detect<PERSON><PERSON><PERSON>andles --verbose --coverage --runInBand", "coverage": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --coverage", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "https://github.com/TencentCloudBase/node-sdk"}, "bugs": {"url": "https://github.com/TencentCloudBase/node-sdk/issues"}, "homepage": "https://github.com/TencentCloudBase/node-sdk#readme", "keywords": ["node sdk"], "author": "lukejyhuang", "license": "MIT", "typings": "types/index.d.ts", "dependencies": {"@cloudbase/database": "1.4.1", "@cloudbase/signature-nodejs": "1.0.0-beta.0", "agentkeepalive": "^4.3.0", "axios": "^0.21.1", "jsonwebtoken": "^8.5.1", "request": "^2.87.0", "retry": "^0.13.1", "xml2js": "^0.5.0"}, "devDependencies": {"@types/jest": "^23.1.4", "@types/mocha": "^5.2.4", "@types/node": "^10.12.12", "@types/retry": "^0.12.2", "@typescript-eslint/eslint-plugin": "^2.16.0", "@typescript-eslint/parser": "^2.16.0", "babel-eslint": "^10.0.3", "coveralls": "^3.0.9", "dumper.js": "^1.3.0", "eslint": "^7.1.0", "eslint-config-alloy": "^3.5.0", "eslint-plugin-prettier": "^3.1.2", "husky": "^3.1.0", "jest": "^23.3.0", "lint-staged": "^9.2.5", "mocha": "^5.2.0", "power-assert": "^1.5.0", "prettier": "^1.19.1", "ts-jest": "^23.10.4", "ts-node": "^10.9.1", "tslib": "^1.7.1", "typescript": "3.5.3"}, "engines": {"node": ">=8.6.0"}, "husky": {"hooks": {"pre-commit": "npm run build && git add . && lint-staged"}}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}}