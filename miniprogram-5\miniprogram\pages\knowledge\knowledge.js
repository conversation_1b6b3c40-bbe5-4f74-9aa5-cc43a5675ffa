// pages/knowledge/knowledge.js - 古籍查询页面
const app = getApp();

Page({
  data: {
    searchQuery: '', // 搜索关键词
    searchResults: [], // 搜索结果
    isSearching: false, // 是否正在搜索
    selectedCategory: 'all', // 选中的分类
    categories: [
      { id: 'all', name: '全部', count: 300 },
      { id: 'yijing', name: '易经类', count: 120 },
      { id: 'bazi', name: '八字类', count: 80 },
      { id: 'ziwei', name: '紫微斗数', count: 100 }
    ],
    hotKeywords: [
      '六爻', '梅花易数', '子平真诠', '紫微斗数',
      '周易本义', '滴天髓', '穷通宝鉴', '三命通会'
    ],
    recentBooks: [
      {
        id: 1,
        title: '周易本义',
        author: '朱熹',
        category: '易经类',
        description: '宋代朱熹对《周易》的注解，是易学史上的重要著作。',
        content: '《周易本义》是朱熹对《周易》的注解...'
      },
      {
        id: 2,
        title: '子平真诠',
        author: '沈孝瞻',
        category: '八字类',
        description: '清代沈孝瞻所著，是八字命理学的经典教材。',
        content: '《子平真诠》论命以月令为纲...'
      },
      {
        id: 3,
        title: '紫微斗数全书',
        author: '陈希夷',
        category: '紫微斗数',
        description: '紫微斗数的开山之作，详述星宿排盘和推命方法。',
        content: '《紫微斗数全书》以紫微星为主...'
      }
    ]
  },

  onLoad() {
    console.log('古籍查询页面加载');
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchQuery: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    const query = this.data.searchQuery.trim();
    if (!query) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    this.performSearch(query);
  },

  // 点击热门关键词
  onHotKeywordTap(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchQuery: keyword
    });
    this.performSearch(keyword);
  },

  // 执行搜索逻辑
  async performSearch(query) {
    this.setData({
      isSearching: true,
      searchResults: []
    });

    try {
      // 调用云函数搜索知识库
      const result = await wx.cloud.callFunction({
        name: 'knowledge-search',
        data: {
          query: query,
          category: this.data.selectedCategory,
          limit: 20
        }
      });

      if (result.result.success) {
        // 处理搜索结果，格式化显示内容
        const formattedResults = result.result.data.map(item => ({
          id: item._id,
          title: item.title || '未知标题',
          author: item.author || '佚名',
          dynasty: item.dynasty || '',
          category: this.getCategoryName(item.category),
          matchedContent: this.formatMatchedContent(item.matchedSnippets, query),
          relevance: Math.round(item.relevance || 0),
          fullContent: item.content
        }));

        this.setData({
          searchResults: formattedResults,
          isSearching: false
        });

        if (formattedResults.length === 0) {
          wx.showToast({
            title: '未找到相关内容',
            icon: 'none'
          });
        }
      } else {
        throw new Error(result.result.error || '搜索失败');
      }
    } catch (error) {
      console.error('搜索失败:', error);
      this.setData({
        isSearching: false
      });
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    }
  },

  // 获取分类中文名称
  getCategoryName(category) {
    const categoryMap = {
      'yijing': '易经类',
      'bazi': '八字类',
      'ziwei': '紫微斗数',
      'meihua': '梅花易数',
      'other': '其他'
    };
    return categoryMap[category] || '其他';
  },

  // 格式化匹配内容
  formatMatchedContent(snippets, query) {
    if (!snippets || snippets.length === 0) {
      return `...包含"${query}"的相关内容...`;
    }

    // 取前2个匹配片段，每个片段限制长度
    const formattedSnippets = snippets.slice(0, 2).map(snippet => {
      let text = snippet.trim();
      if (text.length > 100) {
        text = text.substring(0, 100) + '...';
      }
      return text;
    });

    return formattedSnippets.join('\n...\n');
  },

  // 选择分类
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      selectedCategory: categoryId
    });
  },

  // 查看古籍详情
  onBookTap(e) {
    const bookId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/book-detail/book-detail?id=${bookId}`
    });
  },

  // 查看搜索结果详情
  onResultTap(e) {
    const resultId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/book-detail/book-detail?id=${resultId}&highlight=${this.data.searchQuery}`
    });
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      searchQuery: '',
      searchResults: []
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '元亨利贞 - 古籍查询',
      path: '/pages/knowledge/knowledge',
      imageUrl: '/images/share-knowledge.jpg'
    };
  }
});
