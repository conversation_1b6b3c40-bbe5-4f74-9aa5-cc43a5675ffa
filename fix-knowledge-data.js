// 修复知识库数据中的特殊字符和格式问题
const fs = require('fs');

const KNOWLEDGE_DATA_FILE = './knowledge-data.js';
const FIXED_DATA_FILE = './knowledge-data-fixed.js';

/**
 * 清理文本内容
 */
function cleanContent(content) {
  if (!content) return '';
  
  // 移除或替换特殊字符
  return content
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
    .replace(/\r\n/g, '\\n') // 替换换行符
    .replace(/\r/g, '\\n')
    .replace(/\n/g, '\\n')
    .replace(/"/g, '\\"') // 转义双引号
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/\t/g, '\\t') // 转义制表符
    .trim();
}

/**
 * 修复知识库数据
 */
function fixKnowledgeData() {
  console.log('开始修复知识库数据...');
  
  if (!fs.existsSync(KNOWLEDGE_DATA_FILE)) {
    console.error('知识库数据文件不存在！');
    return;
  }
  
  let content = fs.readFileSync(KNOWLEDGE_DATA_FILE, 'utf-8');
  
  console.log('原文件大小:', content.length, '字符');
  
  // 修复常见的JSON格式问题
  content = content
    // 修复未转义的引号
    .replace(/"content":\s*"([^"]*)"([^,}]*)"([^,}]*)",/g, (match, p1, p2, p3) => {
      const cleanedContent = cleanContent(p1 + p2 + p3);
      return `"content": "${cleanedContent}",`;
    })
    // 修复其他字段中的特殊字符
    .replace(/"title":\s*"([^"]*)",/g, (match, p1) => {
      const cleanedTitle = cleanContent(p1);
      return `"title": "${cleanedTitle}",`;
    })
    .replace(/"author":\s*"([^"]*)",/g, (match, p1) => {
      const cleanedAuthor = cleanContent(p1);
      return `"author": "${cleanedAuthor}",`;
    })
    .replace(/"dynasty":\s*"([^"]*)",/g, (match, p1) => {
      const cleanedDynasty = cleanContent(p1);
      return `"dynasty": "${cleanedDynasty}",`;
    });
  
  // 写入修复后的文件
  fs.writeFileSync(FIXED_DATA_FILE, content, 'utf-8');
  
  console.log('修复完成！');
  console.log('修复后文件大小:', content.length, '字符');
  console.log('输出文件:', FIXED_DATA_FILE);
  
  // 验证修复后的文件
  try {
    console.log('验证修复后的文件...');
    
    // 提取批次数据进行验证
    const batchRegex = /const KNOWLEDGE_BATCH_(\d+) = (\[[\s\S]*?\]);/g;
    let match;
    let validBatches = 0;
    let invalidBatches = 0;
    
    while ((match = batchRegex.exec(content)) !== null) {
      const batchNumber = parseInt(match[1]);
      const batchDataStr = match[2];
      
      try {
        const batchData = eval(batchDataStr);
        console.log(`✅ 第 ${batchNumber} 批验证成功: ${batchData.length} 个文件`);
        validBatches++;
      } catch (error) {
        console.log(`❌ 第 ${batchNumber} 批验证失败: ${error.message}`);
        invalidBatches++;
      }
    }
    
    console.log(`\n验证结果:`);
    console.log(`✅ 有效批次: ${validBatches}`);
    console.log(`❌ 无效批次: ${invalidBatches}`);
    
    if (invalidBatches === 0) {
      console.log('\n🎉 所有批次验证通过！可以安全使用修复后的文件。');
    } else {
      console.log('\n⚠️ 仍有部分批次存在问题，需要进一步处理。');
    }
    
  } catch (error) {
    console.error('验证过程中出错:', error.message);
  }
}

// 运行修复脚本
if (require.main === module) {
  fixKnowledgeData();
}

module.exports = { fixKnowledgeData };
