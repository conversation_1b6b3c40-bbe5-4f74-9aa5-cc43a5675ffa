const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  const { userId } = event
  
  try {
    const favorites = await db.collection('favorites')
      .where({ userId })
      .orderBy('createTime', 'desc')
      .get()
      
    return {
      success: true,
      data: favorites.data
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}