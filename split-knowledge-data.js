// 分割knowledge-data.js文件的脚本
const fs = require('fs');
const path = require('path');

const INPUT_FILE = './knowledge-data.js';
const OUTPUT_DIR = './knowledge-parts';
const LINES_PER_FILE = 100; // 每个文件100行

function splitFile() {
  console.log('开始分割knowledge-data.js文件...');
  
  // 创建输出目录
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR);
  }
  
  // 读取原文件
  const content = fs.readFileSync(INPUT_FILE, 'utf-8');
  const lines = content.split('\n');
  
  console.log(`原文件总行数: ${lines.length}`);
  
  // 计算需要分割的文件数
  const totalFiles = Math.ceil(lines.length / LINES_PER_FILE);
  console.log(`将分割为 ${totalFiles} 个文件`);
  
  // 分割文件
  for (let i = 0; i < totalFiles; i++) {
    const startLine = i * LINES_PER_FILE;
    const endLine = Math.min(startLine + LINES_PER_FILE, lines.length);
    const fileLines = lines.slice(startLine, endLine);
    
    const fileName = `part-${String(i + 1).padStart(3, '0')}.js`;
    const filePath = path.join(OUTPUT_DIR, fileName);
    
    let fileContent = '';
    
    // 为第一个文件添加说明注释
    if (i === 0) {
      fileContent += `// 知识库上传脚本 - 第${i + 1}部分 (共${totalFiles}部分)\n`;
      fileContent += `// 使用方法：按顺序在微信开发者工具控制台中执行每个部分\n`;
      fileContent += `// 执行完所有部分后，调用 uploadAllBatches() 开始上传\n\n`;
    } else {
      fileContent += `// 知识库上传脚本 - 第${i + 1}部分 (共${totalFiles}部分)\n\n`;
    }
    
    fileContent += fileLines.join('\n');
    
    fs.writeFileSync(filePath, fileContent, 'utf-8');
    console.log(`✅ 生成: ${fileName} (${fileLines.length} 行)`);
  }
  
  // 生成使用说明文件
  const instructionContent = `# 知识库上传脚本使用说明

## 📁 文件说明
- 原文件 knowledge-data.js 已分割为 ${totalFiles} 个部分
- 每个部分约 ${LINES_PER_FILE} 行，避免控制台崩溃

## 🚀 使用步骤

### 1. 打开微信开发者工具
- 进入您的小程序项目 miniprogram-5
- 打开控制台 (Console)

### 2. 按顺序执行脚本
依次复制粘贴以下文件内容到控制台：

${Array.from({length: totalFiles}, (_, i) => `${i + 1}. part-${String(i + 1).padStart(3, '0')}.js`).join('\n')}

### 3. 开始上传
所有部分执行完成后，运行以下命令：

\`\`\`javascript
// 检查当前状态
checkUploadStatus()

// 一键上传所有批次
uploadAllBatches()

// 或者分批上传
uploadBatch(1)  // 上传第1批
uploadBatch(2)  // 上传第2批
// ... 依此类推到第30批
\`\`\`

## 📊 数据统计
- 总文件数: 438个
- 分批数量: 30批
- 每批大小: 15个文件

## ⚠️ 注意事项
1. 必须按顺序执行所有部分
2. 确保每个部分都执行成功
3. 网络稳定时建议使用 uploadAllBatches()
4. 网络不稳定时建议使用 uploadBatch(n) 分批上传
`;

  fs.writeFileSync(path.join(OUTPUT_DIR, 'README.md'), instructionContent, 'utf-8');
  
  console.log(`\n✅ 分割完成！`);
  console.log(`📁 输出目录: ${OUTPUT_DIR}`);
  console.log(`📄 文件数量: ${totalFiles} 个`);
  console.log(`📖 使用说明: ${OUTPUT_DIR}/README.md`);
}

// 运行分割
if (require.main === module) {
  splitFile();
}

module.exports = { splitFile };
