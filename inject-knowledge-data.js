// 知识库数据注入脚本
// 将生成的知识库数据直接注入到云函数中

const fs = require('fs');
const path = require('path');

const KNOWLEDGE_DATA_FILE = './knowledge-data-clean.js';
const CLOUD_FUNCTION_FILE = './miniprogram-5/cloudfunctions/knowledge-bulk-upload/index.js';
const BATCH_SIZE = 15; // 每批15个文件

/**
 * 读取知识库数据
 */
function readKnowledgeData() {
  console.log('读取知识库数据...');
  
  if (!fs.existsSync(KNOWLEDGE_DATA_FILE)) {
    console.error('知识库数据文件不存在！');
    return null;
  }
  
  const content = fs.readFileSync(KNOWLEDGE_DATA_FILE, 'utf-8');
  
  // 提取所有批次数据
  const batches = [];
  const batchRegex = /const KNOWLEDGE_BATCH_(\d+) = (\[[\s\S]*?\]);/g;
  let match;
  
  while ((match = batchRegex.exec(content)) !== null) {
    const batchNumber = parseInt(match[1]);
    const batchDataStr = match[2];
    
    try {
      const batchData = eval(batchDataStr);
      batches[batchNumber] = batchData;
      console.log(`提取第 ${batchNumber} 批数据: ${batchData.length} 个文件`);
    } catch (error) {
      console.error(`解析第 ${batchNumber} 批数据失败:`, error.message);
    }
  }
  
  return batches;
}

/**
 * 生成批次数据代码
 */
function generateBatchDataCode(batches) {
  console.log('生成批次数据代码...');
  
  let code = `
/**
 * 获取指定批次的数据
 */
function getBatchData(batchNumber) {
  const batchData = {
`;

  // 生成每个批次的数据
  for (let i = 1; i < batches.length; i++) {
    if (batches[i] && batches[i].length > 0) {
      code += `    ${i}: ${JSON.stringify(batches[i], null, 6)},\n`;
    }
  }
  
  code += `  };
  
  return batchData[batchNumber] || [];
}`;

  return code;
}

/**
 * 注入数据到云函数
 */
function injectDataToCloudFunction(batchDataCode) {
  console.log('注入数据到云函数...');
  
  if (!fs.existsSync(CLOUD_FUNCTION_FILE)) {
    console.error('云函数文件不存在！');
    return false;
  }
  
  let content = fs.readFileSync(CLOUD_FUNCTION_FILE, 'utf-8');
  
  // 替换 getBatchData 函数
  const functionRegex = /\/\*\*[\s\S]*?\*\/\s*function getBatchData\([\s\S]*?\n}/;
  
  if (functionRegex.test(content)) {
    content = content.replace(functionRegex, batchDataCode);
  } else {
    // 如果没找到函数，在文件末尾添加
    content = content.replace(/function getBatchData\([\s\S]*?\n}/, batchDataCode);
  }
  
  // 写回文件
  fs.writeFileSync(CLOUD_FUNCTION_FILE, content, 'utf-8');
  console.log('数据注入完成！');
  
  return true;
}

/**
 * 主函数
 */
function main() {
  console.log('开始知识库数据注入...');
  
  // 读取知识库数据
  const batches = readKnowledgeData();
  if (!batches) {
    return;
  }
  
  console.log(`总共提取 ${batches.length - 1} 批数据`);
  
  // 生成批次数据代码
  const batchDataCode = generateBatchDataCode(batches);
  
  // 注入到云函数
  const success = injectDataToCloudFunction(batchDataCode);
  
  if (success) {
    console.log('\n✅ 数据注入成功！');
    console.log('\n📋 接下来的步骤：');
    console.log('1. 在微信开发者工具中上传并部署 knowledge-bulk-upload 云函数');
    console.log('2. 在小程序中调用云函数上传数据：');
    console.log('   wx.cloud.callFunction({');
    console.log('     name: "knowledge-bulk-upload",');
    console.log('     data: { action: "upload_all" }');
    console.log('   })');
  } else {
    console.log('❌ 数据注入失败！');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main };
