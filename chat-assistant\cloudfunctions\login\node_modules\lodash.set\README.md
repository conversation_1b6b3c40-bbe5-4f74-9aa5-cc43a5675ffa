# lodash.set v4.3.2

The [lodash](https://lodash.com/) method `_.set` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.set
```

In Node.js:
```js
var set = require('lodash.set');
```

See the [documentation](https://lodash.com/docs#set) or [package source](https://github.com/lodash/lodash/blob/4.3.2-npm-packages/lodash.set) for more details.
