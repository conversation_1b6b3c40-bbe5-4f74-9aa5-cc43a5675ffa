<!--pages/shopping/shopping.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">购物推荐</view>
    <view class="header-subtitle">根据您的命理推荐</view>
  </view>

  <!-- 用户登录状态 -->
  <view class="user-section" wx:if="{{!isLoggedIn}}">
    <view class="login-tip">
      <view class="tip-icon">🔐</view>
      <view class="tip-text">请先登录查看个性化推荐</view>
    </view>
  </view>

  <!-- 商品分类 -->
  <view class="categories-section">
    <view class="section-title">商品分类</view>
    <view class="categories-grid">
      <view 
        class="category-item ink-ripple" 
        wx:for="{{categories}}" 
        wx:key="id"
        data-category="{{item}}"
        bindtap="onCategoryTap"
      >
        <view class="category-icon">{{item.icon}}</view>
        <view class="category-name">{{item.name}}</view>
        <view class="category-desc">{{item.description}}</view>
      </view>
    </view>
  </view>

  <!-- 推荐商品 -->
  <view class="products-section">
    <view class="section-title">
      <view class="title-text">为您推荐</view>
      <view class="title-subtitle">基于命理分析的专属推荐</view>
    </view>
    
    <view class="products-list">
      <view 
        class="product-card ink-ripple" 
        wx:for="{{recommendedProducts}}" 
        wx:key="id"
        data-product="{{item}}"
      >
        <view class="product-image">
          <image src="{{item.image}}" mode="aspectFill" class="product-img" />
          <view class="product-badge">推荐</view>
        </view>
        
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-desc">{{item.description}}</view>
          <view class="product-suitable">{{item.suitable}}</view>
          
          <view class="product-footer">
            <view class="product-price">￥{{item.price}}</view>
            <view class="product-actions">
              <view 
                class="btn-detail" 
                data-product="{{item}}"
                bindtap="viewProductDetail"
              >详情</view>
              <view 
                class="btn-add-cart" 
                data-product="{{item}}"
                bindtap="addToCart"
              >加入购物车</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 购物车悬浮按钮 -->
  <view class="cart-float" bindtap="viewCart" wx:if="{{cartItems.length > 0}}">
    <view class="cart-icon">🛒</view>
    <view class="cart-count">{{cartItems.length}}</view>
    <view class="cart-total">￥{{cartTotal}}</view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{recommendedProducts.length === 0}}">
    <view class="empty-icon">🛍️</view>
    <view class="empty-text">暂无推荐商品</view>
    <view class="empty-desc">完成占卜后将为您推荐合适的商品</view>
  </view>
</view>
