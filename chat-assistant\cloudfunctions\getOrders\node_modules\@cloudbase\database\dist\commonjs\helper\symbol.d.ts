import InternalSymbol from '../utils/symbol';
export * from '../utils/symbol';
export declare const SYMBOL_UNSET_FIELD_NAME: InternalSymbol;
export declare const SYMBOL_UPDATE_COMMAND: InternalSymbol;
export declare const SYMBOL_QUERY_COMMAND: InternalSymbol;
export declare const SYMBOL_LOGIC_COMMAND: InternalSymbol;
export declare const SYMBOL_GEO_POINT: InternalSymbol;
export declare const SYMBOL_GEO_LINE_STRING: InternalSymbol;
export declare const SYMBOL_GEO_POLYGON: InternalSymbol;
export declare const SYMBOL_GEO_MULTI_POINT: InternalSymbol;
export declare const SYMBOL_GEO_MULTI_LINE_STRING: InternalSymbol;
export declare const SYMBOL_GEO_MULTI_POLYGON: InternalSymbol;
export declare const SYMBOL_SERVER_DATE: InternalSymbol;
export declare const SYMBOL_REGEXP: InternalSymbol;
export declare const SYMBOL_OBJECTID: InternalSymbol;
