import { LogicCommand } from './logic';
import { InternalSymbol } from '../helper/symbol';
import { Point, LineString, Polygon, MultiPoint, MultiLineString, MultiPolygon } from '../geo/index';
import { CenterSphere } from '../typings';
export declare const EQ = "eq";
export declare const NEQ = "neq";
export declare const GT = "gt";
export declare const GTE = "gte";
export declare const LT = "lt";
export declare const LTE = "lte";
export declare const IN = "in";
export declare const NIN = "nin";
export declare const ALL = "all";
export declare const ELEM_MATCH = "elemMatch";
export declare const EXISTS = "exists";
export declare const SIZE = "size";
export declare const MOD = "mod";
export declare enum QUERY_COMMANDS_LITERAL {
    EQ = "eq",
    NEQ = "neq",
    GT = "gt",
    GTE = "gte",
    LT = "lt",
    LTE = "lte",
    IN = "in",
    NIN = "nin",
    ALL = "all",
    ELEM_MATCH = "elemMatch",
    EXISTS = "exists",
    SIZE = "size",
    MOD = "mod",
    GEO_NEAR = "geoNear",
    GEO_WITHIN = "geoWithin",
    GEO_INTERSECTS = "geoIntersects"
}
export declare class QueryCommand extends LogicCommand {
    operator: QUERY_COMMANDS_LITERAL;
    constructor(operator: QUERY_COMMANDS_LITERAL, operands: any, fieldName?: string | InternalSymbol);
    toJSON(): {
        ['$ne']: any;
    } | {
        [x: string]: any;
        $ne?: undefined;
    };
    _setFieldName(fieldName: string): QueryCommand;
    eq(val: any): LogicCommand;
    neq(val: any): LogicCommand;
    gt(val: any): LogicCommand;
    gte(val: any): LogicCommand;
    lt(val: any): LogicCommand;
    lte(val: any): LogicCommand;
    in(list: any[]): LogicCommand;
    nin(list: any[]): LogicCommand;
    geoNear(val: IGeoNearOptions): LogicCommand;
    geoWithin(val: IGeoWithinOptions): LogicCommand;
    geoIntersects(val: IGeoIntersectsOptions): LogicCommand;
}
export declare function isQueryCommand(object: any): object is QueryCommand;
export declare function isKnownQueryCommand(object: any): object is QueryCommand;
export declare function isComparisonCommand(object: any): object is QueryCommand;
export default QueryCommand;
export interface IGeoNearOptions {
    geometry: Point;
    maxDistance?: number;
    minDistance?: number;
}
export interface IGeoWithinOptions {
    geometry?: Polygon | MultiPolygon;
    centerSphere?: CenterSphere;
}
export interface IGeoIntersectsOptions {
    geometry: Point | LineString | Polygon | MultiPoint | MultiLineString | MultiPolygon;
}
