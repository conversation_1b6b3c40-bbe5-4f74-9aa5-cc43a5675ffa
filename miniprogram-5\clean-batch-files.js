const fs = require('fs');
const path = require('path');

console.log('开始清理批次文件中的特殊字符...');

// 获取所有批次文件
const batchDir = './upload-batches';
const files = fs.readdirSync(batchDir).filter(file => file.startsWith('batch-') && file.endsWith('.json'));

console.log(`找到 ${files.length} 个批次文件`);

let processedCount = 0;
let errorCount = 0;

files.forEach(filename => {
  try {
    const filePath = path.join(batchDir, filename);
    console.log(`处理文件: ${filename}`);

    // 读取并解析JSON
    const jsonData = JSON.parse(fs.readFileSync(filePath, 'utf8'));

    // 只清理data数组中每个对象的content字段
    if (jsonData.data && Array.isArray(jsonData.data)) {
      jsonData.data.forEach(item => {
        if (item.content && typeof item.content === 'string') {
          // 只清理content字段中的特殊字符
          item.content = item.content
            .replace(/\r\n/g, '\n')  // 将Windows换行符替换为Unix换行符
            .replace(/\r/g, '\n')    // 将单独的回车符替换为换行符
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, ''); // 移除控制字符
        }
      });
    }

    // 重新序列化JSON
    const cleanedContent = JSON.stringify(jsonData, null, 2);

    // 验证JSON格式
    try {
      JSON.parse(cleanedContent);
      console.log(`✅ ${filename} JSON格式验证通过`);
    } catch (parseError) {
      console.error(`❌ ${filename} JSON格式验证失败:`, parseError.message);
      errorCount++;
      return;
    }

    // 写回文件
    fs.writeFileSync(filePath, cleanedContent, 'utf8');
    processedCount++;

    console.log(`✅ ${filename} 处理完成`);

  } catch (error) {
    console.error(`❌ 处理 ${filename} 时出错:`, error.message);
    errorCount++;
  }
});

console.log('\n清理完成统计:');
console.log(`- 成功处理: ${processedCount} 个文件`);
console.log(`- 处理失败: ${errorCount} 个文件`);

if (errorCount === 0) {
  console.log('\n🎉 所有文件清理成功！现在可以重新尝试导入batch-024.json了。');
} else {
  console.log('\n⚠️ 部分文件处理失败，请检查错误信息。');
}
