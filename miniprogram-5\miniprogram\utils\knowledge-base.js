// 知识库处理模块 - 云开发数据库查询
// 注意：小程序环境无法使用Node.js的fs模块，改用云开发数据库

/**
 * 知识库云开发配置
 */
const KNOWLEDGE_CONFIG = {
  // 云数据库集合名
  collection: 'knowledge_base',
  // 搜索限制
  searchLimit: 20,
  // 分类映射
  categories: {
    'yijing': '易经类',
    'bazi': '八字类',
    'ziwei': '紫微斗数',
    'meihua': '梅花易数',
    'other': '其他'
  }
};

/**
 * 云开发数据库实例
 */
let db = null;

/**
 * 初始化数据库连接
 */
function initDatabase() {
  if (!db) {
    db = wx.cloud.database();
  }
  return db;
}

/**
 * 搜索知识库内容
 * @param {string} query 搜索关键词
 * @param {string} category 分类筛选
 * @param {number} limit 返回数量限制
 * @returns {Promise<Array>} 搜索结果
 */
async function searchKnowledge(query, category = '', limit = 20) {
  try {
    initDatabase();

    // 构建查询条件
    let whereCondition = {};

    // 分类筛选
    if (category && category !== 'all') {
      whereCondition.category = category;
    }

    // 关键词搜索 - 使用正则表达式进行模糊匹配
    if (query && query.trim()) {
      const searchRegex = new RegExp(query.trim(), 'i');
      whereCondition = {
        ...whereCondition,
        $or: [
          { title: searchRegex },
          { author: searchRegex },
          { content: searchRegex },
          { keywords: searchRegex }
        ]
      };
    }

    // 执行查询
    const result = await db.collection(KNOWLEDGE_CONFIG.collection)
      .where(whereCondition)
      .limit(limit)
      .orderBy('created_at', 'desc')
      .get();

    return result.data || [];
  } catch (error) {
    console.error('搜索知识库失败:', error);
    return [];
  }
}

/**
 * 获取知识库统计信息
 * @returns {Promise<Object>} 统计信息
 */
async function getKnowledgeStats() {
  try {
    initDatabase();

    // 获取总数
    const totalResult = await db.collection(KNOWLEDGE_CONFIG.collection).count();
    const total = totalResult.total;

    // 按分类统计
    const categoryStats = {};
    for (const [key, value] of Object.entries(KNOWLEDGE_CONFIG.categories)) {
      const categoryResult = await db.collection(KNOWLEDGE_CONFIG.collection)
        .where({ category: key })
        .count();
      categoryStats[key] = {
        name: value,
        count: categoryResult.total
      };
    }

    return {
      total,
      categories: categoryStats
    };
  } catch (error) {
    console.error('获取知识库统计失败:', error);
    return {
      total: 0,
      categories: {}
    };
  }
}

/**
 * 根据问题类型获取相关知识
 * @param {string} questionType - 问题类型
 * @param {string} specificQuery - 具体查询内容
 * @returns {Promise<Array>} 相关知识内容
 */
async function getRelevantKnowledge(questionType, specificQuery = '') {
  // 问题类型到分类的映射
  const categoryMap = {
    '财运': 'bazi',
    '事业': 'bazi',
    '婚姻': 'bazi',
    '健康': 'bazi',
    '学业': 'bazi',
    '卦象': 'yijing',
    '六爻': 'yijing',
    '梅花': 'meihua',
    '紫微': 'ziwei'
  };

  const category = categoryMap[questionType] || '';

  // 构建搜索关键词
  const keywords = [specificQuery, questionType].filter(k => k).join(' ');

  return await searchKnowledge(keywords, category, 3);
}



module.exports = {
  initDatabase,
  searchKnowledge,
  getRelevantKnowledge,
  getKnowledgeStats,
  KNOWLEDGE_CONFIG
};
