// 批量上传脚本 - 在微信开发者工具控制台中运行
// 注意：这个脚本需要在开发者工具的控制台中手动执行

/**
 * 知识库文件数据 - 您需要手动填入真实的文件内容
 * 由于小程序无法直接读取本地文件系统，需要手动准备数据
 */
const KNOWLEDGE_FILES_DATA = [
  {
    filePath: '周易本义-宋-朱熹.txt',
    content: `# 周易本义
    
乾为天，坤为地，震为雷，巽为风，坎为水，离为火，艮为山，兑为泽。

乾卦：元亨利贞。
初九：潜龙勿用。
九二：见龙在田，利见大人。
九三：君子终日乾乾，夕惕若厉，无咎。
九四：或跃在渊，无咎。
九五：飞龙在天，利见大人。
上九：亢龙有悔。

坤卦：元亨，利牝马之贞。
初六：履霜，坚冰至。
六二：直方大，不习无不利。
六三：含章可贞，或从王事，无成有终。
六四：括囊，无咎无誉。
六五：黄裳，元吉。
上六：龙战于野，其血玄黄。

震卦：亨，震来虩虩，笑言哑哑，震惊百里，不丧匕鬯。
巽卦：小亨，利有攸往，利见大人。
坎卦：习坎，有孚，维心亨，行有尚。
离卦：利贞，亨，畜牝牛，吉。
艮卦：艮其背，不获其身，行其庭，不见其人，无咎。
兑卦：亨，利贞。`
  },
  {
    filePath: '梅花易数-宋-邵雍.txt',
    content: `# 梅花易数

梅花易数为宋代邵雍所创，是一种占卜方法。

起卦方法：
1. 年月日时起卦法
2. 物数起卦法
3. 声音起卦法
4. 字数起卦法

体用生克：
体卦为主，用卦为客。
体生用，泄气不利。
用生体，得助为吉。
体克用，耗力有得。
用克体，受制不利。

应期推断：
以卦气、五行、数理推断事情发生的时间。

占断要诀：
观其卦象，察其体用，
审其生克，推其吉凶，
断其应期，验其成败。`
  },
  {
    filePath: '紫微斗数全书-陈希夷.txt',
    content: `# 紫微斗数全书

紫微斗数为中国古代占星术的一种，相传为陈抟（陈希夷）所创。

十四主星：
紫微星：帝王星，主贵。
天机星：智慧星，主谋略。
太阳星：光明星，主权威。
武曲星：财星，主财富。
天同星：福星，主享受。
廉贞星：囚星，主刑罚。
天府星：库星，主保守。
太阴星：富星，主财富。
贪狼星：桃花星，主欲望。
巨门星：暗星，主是非。
天相星：印星，主辅佐。
天梁星：荫星，主庇护。
七杀星：将星，主肃杀。
破军星：耗星，主破坏。

十二宫位：
命宫：主一生命运。
兄弟宫：主兄弟姐妹。
夫妻宫：主婚姻配偶。
子女宫：主子女后代。
财帛宫：主财富收入。
疾厄宫：主健康疾病。
迁移宫：主外出变动。
奴仆宫：主朋友部属。
官禄宫：主事业官运。
田宅宫：主房产家业。
福德宫：主福分享受。
父母宫：主父母长辈。`
  }
];

/**
 * 批量上传函数
 */
async function batchUploadKnowledge() {
  console.log('开始批量上传知识库...');
  
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 0; i < KNOWLEDGE_FILES_DATA.length; i++) {
    const fileData = KNOWLEDGE_FILES_DATA[i];
    console.log(`正在上传 ${i + 1}/${KNOWLEDGE_FILES_DATA.length}: ${fileData.filePath}`);
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-upload',
        data: {
          action: 'upload_single',
          filePath: fileData.filePath,
          content: fileData.content
        }
      });
      
      if (result.result.success) {
        console.log(`✅ ${fileData.filePath} 上传成功`);
        successCount++;
      } else {
        console.error(`❌ ${fileData.filePath} 上传失败:`, result.result.error);
        failCount++;
      }
    } catch (error) {
      console.error(`❌ ${fileData.filePath} 上传异常:`, error);
      failCount++;
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n上传完成！成功: ${successCount}, 失败: ${failCount}`);
}

/**
 * 检查云端数据
 */
async function checkCloudData() {
  try {
    const db = wx.cloud.database();
    const result = await db.collection('knowledge_base').count();
    console.log(`云端知识库共有 ${result.total} 条记录`);
    
    // 获取前5条记录查看
    const sampleResult = await db.collection('knowledge_base').limit(5).get();
    console.log('示例记录:', sampleResult.data);
    
    return result.total;
  } catch (error) {
    console.error('检查云端数据失败:', error);
    return 0;
  }
}

/**
 * 清空云端数据
 */
async function clearCloudData() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-upload',
      data: {
        action: 'clear_all'
      }
    });
    
    if (result.result.success) {
      console.log(`✅ 清空完成，删除了 ${result.result.removed} 条记录`);
    } else {
      console.error('❌ 清空失败:', result.result.error);
    }
  } catch (error) {
    console.error('❌ 清空异常:', error);
  }
}

// 导出函数供控制台使用
window.batchUploadKnowledge = batchUploadKnowledge;
window.checkCloudData = checkCloudData;
window.clearCloudData = clearCloudData;

console.log(`
📚 知识库上传脚本已加载

可用命令：
1. checkCloudData() - 检查云端数据
2. batchUploadKnowledge() - 批量上传示例数据
3. clearCloudData() - 清空云端数据

注意：当前只包含3个示例文件，您需要手动添加更多文件数据到 KNOWLEDGE_FILES_DATA 数组中。
`);
