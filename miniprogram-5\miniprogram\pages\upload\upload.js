// 知识库上传管理页面
Page({
  data: {
    localFileCount: 0,
    cloudFileCount: 0,
    uploadProgress: 0,
    loading: false,
    uploading: false,
    uploadedCount: 0,
    totalCount: 0,
    currentFile: '',
    logs: [],
    sampleFiles: []
  },

  onLoad() {
    this.addLog('页面加载完成', 'info');
    this.loadSampleData();
  },

  // 添加日志
  addLog(message, type = 'info') {
    const now = new Date();
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    
    const logs = this.data.logs;
    logs.unshift({
      time,
      message,
      type
    });
    
    // 只保留最近50条日志
    if (logs.length > 50) {
      logs.splice(50);
    }
    
    this.setData({ logs });
  },

  // 加载示例数据
  loadSampleData() {
    // 这里是一些示例文件数据，实际上传时会读取真实文件
    const sampleFiles = [
      {
        title: '周易本义',
        author: '朱熹',
        category: '易经类',
        sizeKB: 156
      },
      {
        title: '梅花易数',
        author: '邵雍',
        category: '易经类',
        sizeKB: 89
      },
      {
        title: '紫微斗数全书',
        author: '陈希夷',
        category: '紫微斗数',
        sizeKB: 234
      },
      {
        title: '八字命理学',
        author: '佚名',
        category: '八字类',
        sizeKB: 178
      }
    ];
    
    this.setData({ 
      sampleFiles,
      localFileCount: 249 // 根据实际knowledge目录文件数量
    });
    this.addLog('示例数据加载完成', 'success');
  },

  // 检查云端数据
  async checkCloudData() {
    this.setData({ loading: true });
    this.addLog('正在检查云端数据...', 'info');
    
    try {
      const db = wx.cloud.database();
      const result = await db.collection('knowledge_base').count();
      
      this.setData({ 
        cloudFileCount: result.total,
        loading: false 
      });
      
      this.addLog(`云端数据检查完成，共${result.total}条记录`, 'success');
    } catch (error) {
      this.setData({ loading: false });
      this.addLog(`检查云端数据失败: ${error.message}`, 'error');
      console.error('检查云端数据失败:', error);
    }
  },

  // 开始上传知识库
  async startUpload() {
    wx.showModal({
      title: '确认上传',
      content: '即将开始上传知识库文件到云数据库，这可能需要较长时间，确认继续？',
      success: (res) => {
        if (res.confirm) {
          this.performUpload();
        }
      }
    });
  },

  // 执行上传
  async performUpload() {
    this.setData({ 
      uploading: true,
      uploadProgress: 0,
      uploadedCount: 0
    });
    
    this.addLog('开始上传知识库...', 'info');
    
    try {
      // 这里需要您手动准备文件数据
      // 由于小程序无法直接读取本地文件系统，我们需要其他方式
      this.addLog('请注意：需要手动准备文件数据', 'warning');
      this.addLog('建议使用云函数或开发者工具上传', 'warning');
      
      // 示例：上传单个文件
      await this.uploadSampleFile();
      
    } catch (error) {
      this.addLog(`上传失败: ${error.message}`, 'error');
      console.error('上传失败:', error);
    } finally {
      this.setData({ uploading: false });
    }
  },

  // 上传示例文件（演示用）
  async uploadSampleFile() {
    const sampleContent = `# 周易本义示例内容
    
乾为天，坤为地，震为雷，巽为风，坎为水，离为火，艮为山，兑为泽。

这是一个示例文件，用于测试上传功能。实际使用时，这里应该是真实的古籍内容。

八卦相重，六十四卦，变化无穷，用以占筮。`;

    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-upload',
        data: {
          action: 'upload_single',
          filePath: '周易本义-宋-朱熹.txt',
          content: sampleContent
        }
      });

      if (result.result.success) {
        this.addLog('示例文件上传成功', 'success');
        this.setData({
          uploadedCount: 1,
          totalCount: 1,
          uploadProgress: 100,
          currentFile: '周易本义-宋-朱熹.txt'
        });
        
        // 重新检查云端数据
        await this.checkCloudData();
      } else {
        throw new Error(result.result.error);
      }
    } catch (error) {
      this.addLog(`上传示例文件失败: ${error.message}`, 'error');
      throw error;
    }
  },

  // 清空云端数据
  async clearCloudData() {
    wx.showModal({
      title: '危险操作',
      content: '确认要清空所有云端知识库数据吗？此操作不可恢复！',
      confirmColor: '#e74c3c',
      success: async (res) => {
        if (res.confirm) {
          this.setData({ loading: true });
          this.addLog('正在清空云端数据...', 'warning');
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'knowledge-upload',
              data: {
                action: 'clear_all'
              }
            });

            if (result.result.success) {
              this.addLog(`清空完成，删除了${result.result.removed}条记录`, 'success');
              this.setData({ cloudFileCount: 0 });
            } else {
              throw new Error(result.result.error);
            }
          } catch (error) {
            this.addLog(`清空失败: ${error.message}`, 'error');
          } finally {
            this.setData({ loading: false });
          }
        }
      }
    });
  }
});
