// 知识库上传脚本
// 用于将knowledge目录下的所有txt文件上传到云数据库
const fs = require('fs');
const path = require('path');

// 知识库目录路径
const KNOWLEDGE_DIR = '../knowledge';

/**
 * 解析文件名获取书籍信息
 */
function parseFilename(filename) {
  const name = path.basename(filename, '.txt');
  
  let title = name;
  let author = '';
  let dynasty = '';
  let category = 'other';
  
  // 匹配模式：作者-书名 或 书名-作者
  const patterns = [
    /^(.+)-(.+)-(.+)$/, // 朝代-作者-书名
    /^(.+)-(.+)$/, // 作者-书名 或 书名-作者
    /^(\d+)\.(.+)$/, // 编号.书名
  ];
  
  for (const pattern of patterns) {
    const match = name.match(pattern);
    if (match) {
      if (match.length === 4) {
        dynasty = match[1];
        author = match[2];
        title = match[3];
      } else if (match.length === 3) {
        if (/^\d+$/.test(match[1])) {
          title = match[2];
        } else {
          author = match[1];
          title = match[2];
        }
      }
      break;
    }
  }
  
  // 根据内容判断分类
  if (title.includes('易') || title.includes('卦') || title.includes('周易')) {
    category = 'yijing';
  } else if (title.includes('紫微') || title.includes('斗数')) {
    category = 'ziwei';
  } else if (title.includes('八字') || title.includes('命理')) {
    category = 'bazi';
  } else if (title.includes('梅花易数')) {
    category = 'meihua';
  }
  
  return {
    title: title.trim(),
    author: author.trim(),
    dynasty: dynasty.trim(),
    category
  };
}

/**
 * 提取关键词
 */
function extractKeywords(content) {
  const keywords = [];
  
  const allKeywords = [
    // 易经关键词
    '乾', '坤', '震', '巽', '坎', '离', '艮', '兑',
    '阴爻', '阳爻', '动爻', '变爻', '世爻', '应爻',
    '用神', '原神', '忌神', '仇神',
    
    // 八字关键词
    '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸',
    '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥',
    '正官', '偏官', '正财', '偏财', '食神', '伤官', '比肩', '劫财', '正印', '偏印',
    
    // 紫微关键词
    '紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军',
    '命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫', '迁移宫', '奴仆宫', '官禄宫', '田宅宫', '福德宫', '父母宫'
  ];
  
  allKeywords.forEach(keyword => {
    if (content.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return [...new Set(keywords)]; // 去重
}

/**
 * 读取所有知识库文件
 */
function readKnowledgeFiles() {
  const files = [];
  const knowledgeDir = path.resolve(__dirname, KNOWLEDGE_DIR);
  
  if (!fs.existsSync(knowledgeDir)) {
    console.error('知识库目录不存在:', knowledgeDir);
    return files;
  }
  
  const fileList = fs.readdirSync(knowledgeDir);
  
  fileList.forEach(filename => {
    if (filename.endsWith('.txt')) {
      const filePath = path.join(knowledgeDir, filename);
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileInfo = parseFilename(filename);
        const keywords = extractKeywords(content);
        
        files.push({
          filePath: filename,
          ...fileInfo,
          content,
          keywords,
          file_size: content.length,
          created_at: new Date(),
          updated_at: new Date()
        });
        
        console.log(`读取文件: ${filename} (${Math.round(content.length/1024)}KB)`);
      } catch (error) {
        console.error(`读取文件失败: ${filename}`, error.message);
      }
    }
  });
  
  return files;
}

/**
 * 生成上传数据
 */
function generateUploadData() {
  console.log('开始读取知识库文件...');
  const files = readKnowledgeFiles();
  
  console.log(`\n读取完成，共${files.length}个文件`);
  
  // 按分类统计
  const categoryStats = {};
  let totalSize = 0;
  
  files.forEach(file => {
    if (!categoryStats[file.category]) {
      categoryStats[file.category] = { count: 0, size: 0 };
    }
    categoryStats[file.category].count++;
    categoryStats[file.category].size += file.file_size;
    totalSize += file.file_size;
  });
  
  console.log('\n分类统计:');
  Object.entries(categoryStats).forEach(([category, stats]) => {
    console.log(`${category}: ${stats.count}个文件, ${Math.round(stats.size/1024/1024*100)/100}MB`);
  });
  console.log(`总计: ${files.length}个文件, ${Math.round(totalSize/1024/1024*100)/100}MB`);
  
  // 保存到JSON文件供云函数使用
  const outputPath = path.join(__dirname, 'knowledge-data.json');
  fs.writeFileSync(outputPath, JSON.stringify(files, null, 2));
  console.log(`\n数据已保存到: ${outputPath}`);
  
  return files;
}

// 执行脚本
if (require.main === module) {
  generateUploadData();
}

module.exports = {
  generateUploadData,
  readKnowledgeFiles,
  parseFilename,
  extractKeywords
};
