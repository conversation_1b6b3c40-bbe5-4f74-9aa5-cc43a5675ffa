// utils/solar-time.js - 真太阳时计算工具
// 基于古籍《紫微斗数基本排盘》中的真太阳时计算方法

/**
 * 中国主要城市经度数据
 */
export const CITY_COORDINATES = {
  // 直辖市
  '北京': { longitude: 116.4074, latitude: 39.9042 },
  '上海': { longitude: 121.4737, latitude: 31.2304 },
  '天津': { longitude: 117.1901, latitude: 39.1084 },
  '重庆': { longitude: 106.5516, latitude: 29.5630 },
  
  // 省会城市
  '广州': { longitude: 113.2644, latitude: 23.1291 },
  '深圳': { longitude: 114.0579, latitude: 22.5431 },
  '杭州': { longitude: 120.1551, latitude: 30.2741 },
  '南京': { longitude: 118.7969, latitude: 32.0603 },
  '武汉': { longitude: 114.2734, latitude: 30.5844 },
  '成都': { longitude: 104.0665, latitude: 30.5723 },
  '西安': { longitude: 108.9398, latitude: 34.3416 },
  '沈阳': { longitude: 123.4315, latitude: 41.8057 },
  '长春': { longitude: 125.3245, latitude: 43.8171 },
  '哈尔滨': { longitude: 126.5358, latitude: 45.8023 },
  '济南': { longitude: 117.0009, latitude: 36.6758 },
  '郑州': { longitude: 113.6254, latitude: 34.7466 },
  '太原': { longitude: 112.5489, latitude: 37.8706 },
  '石家庄': { longitude: 114.5149, latitude: 38.0428 },
  '呼和浩特': { longitude: 111.7519, latitude: 40.8414 },
  '乌鲁木齐': { longitude: 87.6168, latitude: 43.8256 },
  '银川': { longitude: 106.2309, latitude: 38.4872 },
  '西宁': { longitude: 101.7782, latitude: 36.6171 },
  '拉萨': { longitude: 91.1322, latitude: 29.6544 },
  '昆明': { longitude: 102.8329, latitude: 24.8801 },
  '贵阳': { longitude: 106.7135, latitude: 26.5783 },
  '南宁': { longitude: 108.3669, latitude: 22.8176 },
  '海口': { longitude: 110.3312, latitude: 20.0311 },
  '福州': { longitude: 119.3063, latitude: 26.0745 },
  '南昌': { longitude: 115.8921, latitude: 28.6765 },
  '长沙': { longitude: 112.9388, latitude: 28.2282 },
  '合肥': { longitude: 117.2272, latitude: 31.8206 },
  
  // 港澳台
  '香港': { longitude: 114.1694, latitude: 22.3193 },
  '澳门': { longitude: 113.5491, latitude: 22.1987 },
  '台北': { longitude: 121.5654, latitude: 25.0330 },
  
  // 其他重要城市
  '苏州': { longitude: 120.6519, latitude: 31.3989 },
  '无锡': { longitude: 120.3019, latitude: 31.5747 },
  '宁波': { longitude: 121.5440, latitude: 29.8683 },
  '温州': { longitude: 120.6994, latitude: 27.9944 },
  '厦门': { longitude: 118.1689, latitude: 24.4797 },
  '青岛': { longitude: 120.3826, latitude: 36.0671 },
  '大连': { longitude: 121.6147, latitude: 38.9140 },
  '珠海': { longitude: 113.5767, latitude: 22.2707 }
};

/**
 * 获取城市列表
 */
export function getCityList() {
  return Object.keys(CITY_COORDINATES).map(city => ({
    name: city,
    ...CITY_COORDINATES[city]
  }));
}

/**
 * 计算均时差（视时间差）
 * 基于地球椭圆轨道和地轴倾斜造成的时间差
 * @param {Date} date - 日期
 * @returns {number} 均时差（分钟）
 */
export function calculateEquationOfTime(date) {
  const dayOfYear = getDayOfYear(date);
  
  // 使用简化的均时差公式
  // 基于《紫微斗数基本排盘》中的数据
  const B = 2 * Math.PI * (dayOfYear - 81) / 365;
  
  const E = 9.87 * Math.sin(2 * B) - 7.53 * Math.cos(B) - 1.5 * Math.sin(B);
  
  return E; // 返回分钟
}

/**
 * 获取一年中的第几天
 */
function getDayOfYear(date) {
  const start = new Date(date.getFullYear(), 0, 0);
  const diff = date - start;
  const oneDay = 1000 * 60 * 60 * 24;
  return Math.floor(diff / oneDay);
}

/**
 * 计算平太阳时
 * 根据出生地经度修正时间
 * @param {Date} birthTime - 出生时间（北京时间）
 * @param {number} longitude - 出生地经度
 * @returns {Date} 平太阳时
 */
export function calculateMeanSolarTime(birthTime, longitude) {
  // 中国标准时间基于东经120度
  const standardLongitude = 120;
  
  // 经度差转换为时间差（每度4分钟）
  const timeDifference = (longitude - standardLongitude) * 4;
  
  // 创建新的时间对象
  const meanSolarTime = new Date(birthTime);
  meanSolarTime.setMinutes(meanSolarTime.getMinutes() + timeDifference);
  
  return meanSolarTime;
}

/**
 * 计算真太阳时
 * 真太阳时 = 平太阳时 + 均时差
 * @param {Date} birthTime - 出生时间（北京时间）
 * @param {number} longitude - 出生地经度
 * @returns {Object} 包含真太阳时和计算详情的对象
 */
export function calculateTrueSolarTime(birthTime, longitude) {
  // 1. 计算平太阳时
  const meanSolarTime = calculateMeanSolarTime(birthTime, longitude);
  
  // 2. 计算均时差
  const equationOfTime = calculateEquationOfTime(birthTime);
  
  // 3. 计算真太阳时
  const trueSolarTime = new Date(meanSolarTime);
  trueSolarTime.setMinutes(trueSolarTime.getMinutes() + equationOfTime);
  
  // 4. 计算时差详情
  const standardLongitude = 120;
  const longitudeDiff = longitude - standardLongitude;
  const longitudeTimeDiff = longitudeDiff * 4;
  
  return {
    originalTime: birthTime,
    meanSolarTime: meanSolarTime,
    trueSolarTime: trueSolarTime,
    details: {
      longitude: longitude,
      longitudeDiff: longitudeDiff,
      longitudeTimeDiff: longitudeTimeDiff, // 分钟
      equationOfTime: equationOfTime, // 分钟
      totalAdjustment: longitudeTimeDiff + equationOfTime // 总调整分钟数
    }
  };
}

/**
 * 根据城市名获取坐标
 * @param {string} cityName - 城市名
 * @returns {Object|null} 城市坐标
 */
export function getCityCoordinates(cityName) {
  return CITY_COORDINATES[cityName] || null;
}

/**
 * 格式化时间差说明
 * @param {Object} solarTimeResult - calculateTrueSolarTime的返回结果
 * @returns {string} 格式化的说明文字
 */
export function formatSolarTimeExplanation(solarTimeResult) {
  const { details } = solarTimeResult;
  const { longitudeDiff, longitudeTimeDiff, equationOfTime, totalAdjustment } = details;
  
  let explanation = '真太阳时计算说明：\n';
  explanation += `出生地经度：${details.longitude.toFixed(2)}°\n`;
  explanation += `与标准经度差：${longitudeDiff.toFixed(2)}°\n`;
  explanation += `经度时差：${longitudeTimeDiff > 0 ? '+' : ''}${longitudeTimeDiff.toFixed(1)}分钟\n`;
  explanation += `均时差：${equationOfTime > 0 ? '+' : ''}${equationOfTime.toFixed(1)}分钟\n`;
  explanation += `总调整：${totalAdjustment > 0 ? '+' : ''}${totalAdjustment.toFixed(1)}分钟\n`;
  
  return explanation;
}

/**
 * 判断是否需要使用真太阳时
 * 根据时间差大小决定是否有必要使用真太阳时
 * @param {Object} solarTimeResult - calculateTrueSolarTime的返回结果
 * @returns {boolean} 是否建议使用真太阳时
 */
export function shouldUseTrueSolarTime(solarTimeResult) {
  const { totalAdjustment } = solarTimeResult.details;
  
  // 如果时间差超过30分钟，建议使用真太阳时
  return Math.abs(totalAdjustment) > 30;
}

/**
 * 获取时辰信息
 * 根据真太阳时确定准确的时辰
 * @param {Date} trueSolarTime - 真太阳时
 * @returns {Object} 时辰信息
 */
export function getTimeHour(trueSolarTime) {
  const hour = trueSolarTime.getHours();
  const minute = trueSolarTime.getMinutes();
  
  // 时辰对应表
  const timeHours = [
    { name: '子时', range: '23:00-01:00', earthlyBranch: '子' },
    { name: '丑时', range: '01:00-03:00', earthlyBranch: '丑' },
    { name: '寅时', range: '03:00-05:00', earthlyBranch: '寅' },
    { name: '卯时', range: '05:00-07:00', earthlyBranch: '卯' },
    { name: '辰时', range: '07:00-09:00', earthlyBranch: '辰' },
    { name: '巳时', range: '09:00-11:00', earthlyBranch: '巳' },
    { name: '午时', range: '11:00-13:00', earthlyBranch: '午' },
    { name: '未时', range: '13:00-15:00', earthlyBranch: '未' },
    { name: '申时', range: '15:00-17:00', earthlyBranch: '申' },
    { name: '酉时', range: '17:00-19:00', earthlyBranch: '酉' },
    { name: '戌时', range: '19:00-21:00', earthlyBranch: '戌' },
    { name: '亥时', range: '21:00-23:00', earthlyBranch: '亥' }
  ];
  
  // 计算时辰索引
  let timeIndex;
  if (hour >= 23 || hour < 1) {
    timeIndex = 0; // 子时
  } else {
    timeIndex = Math.floor((hour + 1) / 2);
  }
  
  return {
    ...timeHours[timeIndex],
    hour: hour,
    minute: minute,
    timeString: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
  };
}
