/** @public */
export interface MaxKeyExtended {
  $maxKey: 1;
}

/**
 * A class representation of the BSON MaxKey type.
 * @public
 * @category BSONType
 */
export class MaxKey {
  _bsontype!: 'MaxKey';

  constructor() {
    if (!(this instanceof MaxKey)) return new <PERSON>K<PERSON>();
  }

  /** @internal */
  toExtendedJSON(): MaxKeyExtended {
    return { $maxKey: 1 };
  }

  /** @internal */
  static fromExtendedJSON(): <PERSON><PERSON><PERSON> {
    return new <PERSON><PERSON><PERSON>();
  }

  /** @internal */
  [Symbol.for('nodejs.util.inspect.custom')](): string {
    return this.inspect();
  }

  inspect(): string {
    return 'new <PERSON>Key()';
  }
}

Object.defineProperty(MaxKey.prototype, '_bsontype', { value: '<PERSON>Key' });
