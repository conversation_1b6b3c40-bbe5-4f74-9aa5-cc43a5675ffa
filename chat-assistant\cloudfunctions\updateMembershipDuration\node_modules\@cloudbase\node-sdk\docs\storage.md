# 存储

## uploadFile

#### 1. 接口描述

接口功能：上传文件到文件管理服务

接口声明：`uploadFile(object: Object): Promise<Object>`

#### 2. 输入参数

| 字段        | 类型          | 必填 | 说明                                                                                                                                                                                                                                               |
| ----------- | ------------- | ---- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| cloudPath   | String        | 是   | 文件的绝对路径，包含文件名。例如 foo/bar.jpg、foo/bar/baz.jpg 等，不能包含除[0-9 , a-z , A-Z]、/、!、-、\_、.、、\*和中文以外的字符，使用 / 字符来实现类似传统文件系统的层级结构。[查看详情](https://cloud.tencent.com/document/product/436/13324) |
| fileContent | fs.ReadStream | 是   | buffer 或要上传的文件[可读流](https://nodejs.org/api/stream.html#stream_class_stream_readable)                                                                                                                                                     |

#### 3. 返回结果

| 字段      | 类型   | 必填 | 说明                                    |
| --------- | ------ | ---- | --------------------------------------- |
| fileID    | string | 否   | 文件唯一 ID，用来访问文件，建议存储起来 |
| requestId | string | 是   | 请求序列号，用于错误排查                |
| code      | string | 否   | 状态码，操作成功则不返回                |
| message   | string | 否   | 错误描述，操作成功则不返回              |

#### 4. 示例代码

```javascript
// 初始化
const tcb = require('@cloudbase/node-sdk')
const fs = require('fs')

const app = tcb.init({
  env: 'xxx'
})

exports.main = async (event, context) => {
  const result = await app.uploadFile({
    cloudPath: 'test-admin.jpeg',
    fileContent: fs.createReadStream(`${__dirname}/cos.jpeg`)
  })

  console.log(result.fileID) // 输出文件ID
}
```

## getTempFileURL

#### 1. 接口描述

接口功能：获取文件 CDN 下载链接

接口声明：`getTempFileURL(object: IGetTempFileURLOpts, opts: Object): Promise<Object>`

#### 2. 输入参数

| 字段   | 类型                | 必填 | 说明                                                  |
| ------ | ------------------- | ---- | ----------------------------------------------------- |
| object | IGetTempFileURLOpts | 是   | 获取下载链接请求参数                                  |
| opts   | Object              | 否   | 自定义配置，目前只支持超时时间设置，{timeout: number} |

##### IGetTempFileURLOpts

| 字段     | 类型                             | 必填 | 说明                       |
| -------- | -------------------------------- | ---- | -------------------------- |
| fileList | &lt;Array&gt;.string Or fileItem | 是   | 要下载的文件 ID 组成的数组 |

##### fileItem

| 字段   | 类型   | 必填 | 说明           |
| ------ | ------ | ---- | -------------- |
| fileID | string | 是   | 文件 ID        |
| maxAge | number | 是   | 文件链接有效期 |

#### 3. 返回结果

| 字段      | 类型                      | 必填 | 说明                         |
| --------- | ------------------------- | ---- | ---------------------------- |
| fileList  | &lt;Array&gt;.fileUrlItem | 否   | 存储下载链接的数组           |
| requestId | string                    | 是   | 请求序列号，用于错误排查     |
| code      | string                    | 否   | 状态码，操作成功则为 SUCCESS |
| message   | string                    | 否   | 错误描述                     |

##### fileUrlItem

| 字段        | 类型   | 必填 | 说明                     |
| ----------- | ------ | ---- | ------------------------ |
| code        | string | 否   | 删除结果，成功为 SUCCESS |
| fileID      | string | 是   | 文件 ID                  |
| tempFileURL | string | 是   | 文件访问链接             |

#### 4. 示例代码

```javascript
// 初始化
const tcb = require('@cloudbase/node-sdk')

const app = tcb.init({
  env: 'xxx'
})

exports.main = async (event, context) => {
  const result = await app.getTempFileURL({
    fileList: ['cloud://test-28farb/a.png']
  })

  result.fileList.forEach(item => {
    console.log(item.tempFileURL) // 打印文件访问链接
  })
}
```

## deleteFile

#### 1. 接口描述

接口功能：删除文件

接口声明：`deleteFile(object: IDeleteFileOpts, opts: Object): Promise<Object>`

#### 2. 输入参数

| 字段   | 类型            | 必填 | 说明                                                  |
| ------ | --------------- | ---- | ----------------------------------------------------- |
| object | IDeleteFileOpts | 是   | 删除文件请求参数                                      |
| opts   | Object          | 否   | 自定义配置，目前只支持超时时间设置，{timeout: number} |

##### IDeleteFileOpts

| 字段     | 类型                 | 必填 | 说明                       |
| -------- | -------------------- | ---- | -------------------------- |
| fileList | &lt;Array&gt;.string | 是   | 要删除的文件 ID 组成的数组 |

#### 3. 返回结果

| 字段      | 类型                         | 必填 | 说明                     |
| --------- | ---------------------------- | ---- | ------------------------ |
| code      | string                       | 否   | 状态码，操作成功则不返回 |
| message   | string                       | 否   | 错误描述                 |
| fileList  | &lt;Array&gt;.deleteFileItem | 否   | 删除结果组成的数组       |
| requestId | string                       | 是   | 请求序列号，用于错误排查 |

##### deleteFileItem

| 字段   | 类型   | 必填 | 说明                     |
| ------ | ------ | ---- | ------------------------ |
| code   | string | 否   | 删除结果，成功为 SUCCESS |
| fileID | string | 是   | 文件 ID                  |

#### 4. 示例代码

```javascript
// 初始化
const tcb = require('@cloudbase/node-sdk')
const app = tcb.init({
  env: 'xxx'
})

exports.main = async (event, context) => {
  const result = await app.deleteFile({
    fileList: ['HHOeahVQ0fRTDsums4GVgMCsF6CE3wb7kmIkZbX+yilTJE4NPSQQW5EYks']
  })

  result.fileList.forEach(item => {
    if (item.code === 'SUCCESS') {
      // 文件删除成功
    }
  })
}
```

## downloadFile

#### 1. 接口描述

接口功能：下载文件到本地

接口声明：`downloadFile(object: IDownloadFileOpts, opts: Object): Promise<Object>`

#### 2. 输入参数

| 字段   | 类型              | 必填 | 说明                                                  |
| ------ | ----------------- | ---- | ----------------------------------------------------- |
| object | IDownloadFileOpts | 是   | 下载文件请求参数                                      |
| opts   | Object            | 否   | 自定义配置，目前只支持超时时间设置，{timeout: number} |

##### IDownloadFileOpts

| 字段         | 类型   | 必填 | 说明                   |
| ------------ | ------ | ---- | ---------------------- |
| fileID       | string | 是   | 要下载的文件的 id      |
| tempFilePath | string | 否   | 下载的文件要存储的位置 |

#### 3. 返回结果

| 字段        | 类型   | 必填 | 说明                                                   |
| ----------- | ------ | ---- | ------------------------------------------------------ |
| code        | string | 否   | 状态码，操作成功则不返回                               |
| message     | string | 否   | 错误描述                                               |
| fileContent | buffer | 否   | 下载的文件的内容。如果传入 tempFilePath 则不返回该字段 |
| requestId   | string | 是   | 请求序列号，用于错误排查                               |

#### 4. 示例代码

```javascript
// 初始化
const tcb = require('@cloudbase/node-sdk')
const app = tcb.init({
  env: 'xxx'
})

exports.main = async (event, context) => {
  const result = await app.downloadFile({
    fileID: 'cloud://aa-99j9f/my-photo.png'
    // tempFilePath: '/tmp/test/storage/my-photo.png'
  })

  // 未传入tempFilePath 可打印fileContent, 传入则进入对应目录查看文件
  console.log(result.fileContent)
}
```
