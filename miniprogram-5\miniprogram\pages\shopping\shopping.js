// pages/shopping/shopping.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    
    // 推荐商品分类
    categories: [
      {
        id: 'talisman',
        name: '护身符',
        icon: '🛡️',
        description: '传统护身符，化解煞气'
      },
      {
        id: 'wealth',
        name: '招财符',
        icon: '💰',
        description: '招财进宝，财运亨通'
      },
      {
        id: 'love',
        name: '姻缘符',
        icon: '💕',
        description: '促进姻缘，和谐美满'
      },
      {
        id: 'health',
        name: '健康符',
        icon: '🌿',
        description: '身体健康，延年益寿'
      },
      {
        id: 'career',
        name: '事业符',
        icon: '🎯',
        description: '事业顺利，步步高升'
      },
      {
        id: 'study',
        name: '学业符',
        icon: '📚',
        description: '学业进步，智慧开启'
      }
    ],
    
    // 推荐商品
    recommendedProducts: [],
    
    // 购物车
    cartItems: [],
    cartTotal: 0
  },

  onLoad() {
    console.log('购物页面加载');
    this.initPage();
  },

  onShow() {
    this.refreshUserInfo();
    this.loadCartItems();
  },

  // 初始化页面
  initPage() {
    this.refreshUserInfo();
    this.loadRecommendedProducts();
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = app.globalData.userInfo;
    this.setData({
      userInfo: userInfo,
      isLoggedIn: !!userInfo
    });
  },

  // 加载推荐商品
  loadRecommendedProducts() {
    // 这里将来会根据用户的占卜结果推荐相应的商品
    // 目前显示示例商品
    const sampleProducts = [
      {
        id: 'p001',
        name: '五帝钱护身符',
        category: 'talisman',
        price: 88,
        image: '/images/default-goods-image.png',
        description: '清朝五帝钱制作，化解煞气，保平安',
        suitable: '适合命中缺金、犯小人者'
      },
      {
        id: 'p002',
        name: '招财貔貅',
        category: 'wealth',
        price: 168,
        image: '/images/default-goods-image.png',
        description: '开光貔貅，招财进宝，财运亨通',
        suitable: '适合求财、生意人佩戴'
      },
      {
        id: 'p003',
        name: '红绳姻缘符',
        category: 'love',
        price: 66,
        image: '/images/default-goods-image.png',
        description: '月老红绳，促进姻缘，和谐美满',
        suitable: '适合单身求缘、夫妻和睦'
      }
    ];

    this.setData({
      recommendedProducts: sampleProducts
    });
  },

  // 加载购物车商品
  loadCartItems() {
    try {
      const cartItems = wx.getStorageSync('cartItems') || [];
      const cartTotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
      
      this.setData({
        cartItems: cartItems,
        cartTotal: cartTotal
      });
    } catch (error) {
      console.error('加载购物车失败:', error);
    }
  },

  // 添加到购物车
  addToCart(e) {
    const { product } = e.currentTarget.dataset;
    
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '请先登录',
        content: '添加商品到购物车需要先登录',
        showCancel: false
      });
      return;
    }

    try {
      let cartItems = wx.getStorageSync('cartItems') || [];
      const existingItem = cartItems.find(item => item.id === product.id);
      
      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cartItems.push({
          ...product,
          quantity: 1,
          addTime: new Date().getTime()
        });
      }
      
      wx.setStorageSync('cartItems', cartItems);
      
      wx.showToast({
        title: '已添加到购物车',
        icon: 'success'
      });
      
      this.loadCartItems();
    } catch (error) {
      console.error('添加购物车失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 查看商品详情
  viewProductDetail(e) {
    const { product } = e.currentTarget.dataset;
    // 这里将来可以跳转到商品详情页
    wx.showModal({
      title: product.name,
      content: `${product.description}\n\n${product.suitable}`,
      showCancel: false
    });
  },

  // 查看购物车
  viewCart() {
    if (this.data.cartItems.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
      return;
    }
    
    // 这里将来可以跳转到购物车页面
    wx.showModal({
      title: '购物车',
      content: `共${this.data.cartItems.length}件商品，总计￥${this.data.cartTotal}`,
      confirmText: '去结算',
      success: (res) => {
        if (res.confirm) {
          this.checkout();
        }
      }
    });
  },

  // 结算
  checkout() {
    wx.showModal({
      title: '功能开发中',
      content: '支付功能正在开发中，敬请期待！',
      showCancel: false
    });
  },

  // 点击分类
  onCategoryTap(e) {
    const { category } = e.currentTarget.dataset;
    const filteredProducts = this.data.recommendedProducts.filter(
      product => product.category === category.id
    );
    
    if (filteredProducts.length === 0) {
      wx.showToast({
        title: '该分类暂无商品',
        icon: 'none'
      });
      return;
    }
    
    // 这里可以显示分类商品或跳转到分类页面
    wx.showModal({
      title: category.name,
      content: `${category.description}\n\n找到${filteredProducts.length}件相关商品`,
      showCancel: false
    });
  }
});
