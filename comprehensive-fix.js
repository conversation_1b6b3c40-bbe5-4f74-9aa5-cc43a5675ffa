const fs = require('fs');

console.log('开始系统性全面修复知识库数据...');

// 读取原始数据文件
let fileContent;
try {
    fileContent = fs.readFileSync('knowledge-data-clean.js', 'utf8');
    console.log('成功读取原始数据文件');
} catch (error) {
    console.error('无法读取文件:', error);
    process.exit(1);
}

// 查找所有批次数据
const batchRegex = /const KNOWLEDGE_BATCH_(\d+) = \[([\s\S]*?)\];/g;
const batches = [];
let match;

while ((match = batchRegex.exec(fileContent)) !== null) {
    const batchNumber = parseInt(match[1]);
    const batchContent = match[2];
    batches.push({
        number: batchNumber,
        content: batchContent,
        fullMatch: match[0],
        startIndex: match.index,
        endIndex: match.index + match[0].length
    });
}

if (batches.length === 0) {
    console.error('无法找到任何批次数据');
    process.exit(1);
}

console.log(`找到 ${batches.length} 个批次`);

console.log('开始解析所有批次数据项...');

// 解析所有批次的数据项
const allItems = [];

for (const batch of batches) {
    console.log(`解析第 ${batch.number} 批...`);

    const items = [];
    let currentItem = '';
    let braceCount = 0;
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < batch.content.length; i++) {
        const char = batch.content[i];

        if (escapeNext) {
            currentItem += char;
            escapeNext = false;
            continue;
        }

        if (char === '\\') {
            currentItem += char;
            escapeNext = true;
            continue;
        }

        if (char === '"' && !escapeNext) {
            inString = !inString;
            currentItem += char;
            continue;
        }

        if (!inString) {
            if (char === '{') {
                braceCount++;
            } else if (char === '}') {
                braceCount--;
            }

            if (char === ',' && braceCount === 0) {
                // 完成一个项目
                const trimmed = currentItem.trim();
                if (trimmed) {
                    items.push({
                        content: trimmed,
                        batchNumber: batch.number
                    });
                }
                currentItem = '';
                continue;
            }
        }

        currentItem += char;
    }

    // 添加最后一个项目
    if (currentItem.trim()) {
        items.push({
            content: currentItem.trim(),
            batchNumber: batch.number
        });
    }

    allItems.push(...items);
    console.log(`第 ${batch.number} 批解析出 ${items.length} 个项目`);
}

console.log(`总共找到 ${allItems.length} 个数据项`);

// 修复统计
const stats = {
    total: allItems.length,
    fixed: 0,
    authorEmpty: 0,
    titleAuthorSwapped: 0,
    contentCorrupted: 0,
    jsonErrors: 0,
    skipped: 0
};

const fixedBatches = {};
const errorLog = [];

// 修复每个数据项
for (let i = 0; i < allItems.length; i++) {
    const item = allItems[i];
    let itemFixed = false;
    
    try {
        // 尝试解析JSON
        const parsed = JSON.parse(item.content);
        
        // 检查author字段为空
        if (!parsed.author || parsed.author.trim() === '') {
            stats.authorEmpty++;
            
            // 尝试从filePath提取author
            if (parsed.filePath) {
                const match = parsed.filePath.match(/^([^-]+)-/);
                if (match) {
                    parsed.author = match[1];
                    console.log(`项目 ${i + 1}: 修复空author -> ${parsed.author}`);
                    itemFixed = true;
                } else {
                    parsed.author = "未知作者";
                    itemFixed = true;
                }
            }
        }
        
        // 检查title和author是否颠倒
        if (parsed.title && parsed.author && parsed.filePath) {
            const expectedPattern = `${parsed.author}-${parsed.title}`;
            if (!parsed.filePath.includes(expectedPattern)) {
                // 可能颠倒了，尝试交换
                const swappedPattern = `${parsed.title}-${parsed.author}`;
                if (parsed.filePath.includes(swappedPattern)) {
                    const temp = parsed.title;
                    parsed.title = parsed.author;
                    parsed.author = temp;
                    console.log(`项目 ${i + 1}: 交换title和author -> ${parsed.author} - ${parsed.title}`);
                    stats.titleAuthorSwapped++;
                    itemFixed = true;
                }
            }
        }
        
        // 检查content字段
        if (parsed.content && typeof parsed.content === 'string') {
            const originalLength = parsed.content.length;
            
            // 检查是否包含大量异常字符
            const controlChars = (parsed.content.match(/[\u0000-\u001F\u007F-\u009F]/g) || []).length;
            const specialChars = (parsed.content.match(/[^\u4e00-\u9fa5\u0020-\u007E\u3000-\u303F\uFF00-\uFFEF]/g) || []).length;
            
            if (controlChars > 0 || specialChars > originalLength * 0.1) {
                console.log(`项目 ${i + 1}: 清理content字段异常字符`);
                
                // 清理异常字符
                parsed.content = parsed.content
                    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
                    .replace(/[^\u4e00-\u9fa5\u0020-\u007E\u3000-\u303F\uFF00-\uFFEF]/g, '') // 移除其他异常字符
                    .replace(/\s+/g, ' ') // 规范化空白字符
                    .trim();
                
                stats.contentCorrupted++;
                itemFixed = true;
            }
            
            // 如果content太短或为空，生成默认内容
            if (parsed.content.length < 20) {
                parsed.content = `${parsed.title || '未知文档'}的相关内容。作者：${parsed.author || '未知'}。朝代：${parsed.dynasty || '未知'}。`;
                console.log(`项目 ${i + 1}: 生成默认content`);
                itemFixed = true;
            }
        }
        
        // 确保所有必需字段存在
        if (!parsed.filePath) parsed.filePath = `unknown-${i + 1}.txt`;
        if (!parsed.title) parsed.title = `未知文档${i + 1}`;
        if (!parsed.author) parsed.author = "未知作者";
        if (!parsed.dynasty) parsed.dynasty = "";
        if (!parsed.category) parsed.category = "易学";
        if (!parsed.keywords) parsed.keywords = ["易学"];
        if (!parsed.content) parsed.content = "内容待补充";
        
        if (itemFixed) {
            stats.fixed++;
        }

        // 按批次分组
        if (!fixedBatches[item.batchNumber]) {
            fixedBatches[item.batchNumber] = [];
        }
        fixedBatches[item.batchNumber].push(JSON.stringify(parsed, null, 2));
        
    } catch (error) {
        console.log(`项目 ${i + 1}: JSON解析错误 - ${error.message}`);
        stats.jsonErrors++;
        
        // 记录错误
        errorLog.push({
            index: i + 1,
            batchNumber: item.batchNumber,
            error: error.message,
            content: item.content.substring(0, 200) + '...'
        });

        // 创建替代项目
        const fallbackItem = {
            filePath: `error-item-${i + 1}.txt`,
            title: `错误项目${i + 1}`,
            author: "数据错误",
            dynasty: "",
            category: "易学",
            keywords: ["易学"],
            content: "此项目数据损坏，需要手动修复。"
        };

        // 按批次分组
        if (!fixedBatches[item.batchNumber]) {
            fixedBatches[item.batchNumber] = [];
        }
        fixedBatches[item.batchNumber].push(JSON.stringify(fallbackItem, null, 2));
        stats.fixed++;
    }
}

// 生成修复后的文件
let newFileContent = fileContent;

// 按批次替换内容
for (const batch of batches.sort((a, b) => b.startIndex - a.startIndex)) {
    if (fixedBatches[batch.number]) {
        const newBatchContent = `const KNOWLEDGE_BATCH_${batch.number} = [\n  ${fixedBatches[batch.number].join(',\n  ')}\n];`;
        newFileContent = newFileContent.substring(0, batch.startIndex) +
                        newBatchContent +
                        newFileContent.substring(batch.endIndex);
    }
}

// 保存修复后的文件
try {
    fs.writeFileSync('knowledge-data-comprehensive-fixed.js', newFileContent, 'utf8');
    console.log('\n修复后的数据已保存到 knowledge-data-comprehensive-fixed.js');
    
    // 验证修复后的文件
    console.log('验证修复后的文件语法...');
    require('./knowledge-data-comprehensive-fixed.js');
    console.log('✓ 验证成功！文件语法正确。');
    
} catch (error) {
    console.error('保存或验证文件时出错:', error);
    process.exit(1);
}

// 输出修复统计
console.log('\n=== 修复统计 ===');
console.log(`总项目数: ${stats.total}`);
console.log(`修复项目数: ${stats.fixed}`);
console.log(`空author字段: ${stats.authorEmpty}`);
console.log(`title/author颠倒: ${stats.titleAuthorSwapped}`);
console.log(`content损坏: ${stats.contentCorrupted}`);
console.log(`JSON错误: ${stats.jsonErrors}`);

// 保存错误日志
if (errorLog.length > 0) {
    fs.writeFileSync('fix-error-log.json', JSON.stringify(errorLog, null, 2), 'utf8');
    console.log(`\n错误日志已保存到 fix-error-log.json`);
}

console.log('\n系统性全面修复完成！');
