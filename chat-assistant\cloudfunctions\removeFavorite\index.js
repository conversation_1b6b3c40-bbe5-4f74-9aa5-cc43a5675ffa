const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  const { favoriteId, userId } = event
  const db = cloud.database()
  
  try {
    const result = await db.collection('favorites')
      .where({
        _id: favoriteId,
        userId: userId
      })
      .remove()
    
    return {
      success: true,
      ...result
    }
  } catch (error) {
    return {
      success: false,
      error
    }
  }
}
