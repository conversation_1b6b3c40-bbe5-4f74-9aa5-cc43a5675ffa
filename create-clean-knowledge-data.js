// 重新生成干净的知识库数据
const fs = require('fs');
const path = require('path');

const KNOWLEDGE_DIR = './knowledge';
const OUTPUT_FILE = './knowledge-data-clean.js';
const BATCH_SIZE = 15; // 每批15个文件

/**
 * 解析文件名获取书籍信息
 */
function parseFilename(filename) {
  const name = path.basename(filename, '.txt');
  
  let title = name;
  let author = '';
  let dynasty = '';
  let category = 'other';
  
  // 匹配模式
  const patterns = [
    /^(.+)-(.+)-(.+)$/, // 书名-朝代-作者
    /^(.+)-(.+)$/, // 书名-作者 或 作者-书名
    /^(\d+)\.(.+)$/, // 编号.书名
    /^(.+)$/  // 仅书名
  ];
  
  // 尝试匹配不同模式
  for (const pattern of patterns) {
    const match = name.match(pattern);
    if (match) {
      if (match.length === 4) {
        title = match[1];
        dynasty = match[2];
        author = match[3];
      } else if (match.length === 3) {
        if (/^\d+$/.test(match[1])) {
          title = match[2];
        } else {
          if (match[2].includes('宋') || match[2].includes('唐') || 
              match[2].includes('明') || match[2].includes('清') ||
              match[2].includes('元') || match[2].includes('汉')) {
            title = match[1];
            dynasty = match[2];
          } else if (match[1].includes('宋') || match[1].includes('唐') || 
                     match[1].includes('明') || match[1].includes('清') ||
                     match[1].includes('元') || match[1].includes('汉')) {
            dynasty = match[1];
            title = match[2];
          } else {
            title = match[1];
            author = match[2];
          }
        }
      }
      break;
    }
  }
  
  // 根据书名判断分类
  if (title.includes('周易') || title.includes('易经') || title.includes('易传')) {
    category = 'zhouyi';
  } else if (title.includes('梅花易数')) {
    category = 'meihua';
  } else if (title.includes('紫微斗数') || title.includes('紫薇')) {
    category = 'ziwei';
  } else if (title.includes('八字') || title.includes('子平')) {
    category = 'bazi';
  } else if (title.includes('六壬') || title.includes('奇门')) {
    category = 'liuren';
  }
  
  return {
    title: title.trim(),
    author: author.trim(),
    dynasty: dynasty.trim(),
    category
  };
}

/**
 * 提取关键词
 */
function extractKeywords(content) {
  const keywords = [];
  
  // 易经相关关键词
  const yijingKeywords = ['乾', '坤', '震', '巽', '坎', '离', '艮', '兑', '太极', '阴阳', '八卦', '六十四卦'];
  // 紫微斗数关键词
  const ziweiKeywords = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'];
  // 梅花易数关键词
  const meihuaKeywords = ['梅花易数', '先天', '后天', '体卦', '用卦', '变卦', '互卦'];
  
  const allKeywords = [...yijingKeywords, ...ziweiKeywords, ...meihuaKeywords];
  
  allKeywords.forEach(keyword => {
    if (content.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return keywords.slice(0, 10); // 最多返回10个关键词
}

/**
 * 清理和转义文本
 */
function cleanText(text) {
  if (!text) return '';
  
  return text
    .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
    .replace(/\\/g, '\\\\') // 转义反斜杠
    .replace(/"/g, '\\"') // 转义双引号
    .replace(/\r\n/g, '\\n') // 转义换行符
    .replace(/\r/g, '\\n')
    .replace(/\n/g, '\\n')
    .replace(/\t/g, '\\t') // 转义制表符
    .trim();
}

/**
 * 生成知识库数据
 */
function generateKnowledgeData() {
  console.log('开始生成干净的知识库数据...');
  
  if (!fs.existsSync(KNOWLEDGE_DIR)) {
    console.error('knowledge目录不存在！');
    return;
  }
  
  const files = fs.readdirSync(KNOWLEDGE_DIR)
    .filter(file => file.endsWith('.txt'))
    .sort();
  
  console.log(`找到 ${files.length} 个txt文件`);
  
  let allData = [];
  let processedCount = 0;
  
  files.forEach((file, index) => {
    try {
      const filePath = path.join(KNOWLEDGE_DIR, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      if (content.trim().length === 0) {
        console.log(`跳过空文件: ${file}`);
        return;
      }
      
      const fileInfo = parseFilename(file);
      const keywords = extractKeywords(content);
      
      // 清理所有文本字段
      const fileData = {
        filePath: cleanText(file),
        title: cleanText(fileInfo.title),
        author: cleanText(fileInfo.author),
        dynasty: cleanText(fileInfo.dynasty),
        category: fileInfo.category,
        keywords: keywords,
        content: cleanText(content),
        file_size: content.length,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      allData.push(fileData);
      processedCount++;
      
      if (processedCount % 50 === 0) {
        console.log(`已处理 ${processedCount}/${files.length} 个文件...`);
      }
      
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error.message);
    }
  });
  
  console.log(`总共处理了 ${processedCount} 个文件`);
  
  // 分批处理数据
  const batches = [];
  for (let i = 0; i < allData.length; i += BATCH_SIZE) {
    const batch = allData.slice(i, i + BATCH_SIZE);
    batches.push(batch);
  }
  
  console.log(`分为 ${batches.length} 批，每批最多 ${BATCH_SIZE} 个文件`);
  
  // 生成JavaScript文件
  let jsContent = `// 知识库数据 - 干净版本
// 生成时间: ${new Date().toISOString()}
// 总文件数: ${allData.length}
// 总批次数: ${batches.length}

`;

  // 生成每个批次的数据
  batches.forEach((batch, index) => {
    const batchNumber = index + 1;
    jsContent += `// 第 ${batchNumber} 批数据 (${batch.length} 个文件)\n`;
    jsContent += `const KNOWLEDGE_BATCH_${batchNumber} = ${JSON.stringify(batch, null, 2)};\n\n`;
  });
  
  // 生成批量上传函数
  jsContent += `// 批量上传函数
const ALL_BATCHES = [
${batches.map((_, index) => `  KNOWLEDGE_BATCH_${index + 1}`).join(',\n')}
];

// 上传指定批次
async function uploadBatch(batchNumber) {
  if (batchNumber < 1 || batchNumber > ${batches.length}) {
    console.error('批次号无效:', batchNumber);
    return { success: false, message: '批次号无效' };
  }
  
  const batch = ALL_BATCHES[batchNumber - 1];
  console.log(\`开始上传第 \${batchNumber} 批，共 \${batch.length} 个文件...\`);
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-upload',
      data: {
        action: 'batch_upload',
        files: batch
      }
    });
    
    if (result.result.success) {
      console.log(\`✅ 第 \${batchNumber} 批上传成功\`);
      return { success: true, batchNumber, uploadedCount: batch.length };
    } else {
      console.error(\`❌ 第 \${batchNumber} 批上传失败:\`, result.result.message);
      return { success: false, batchNumber, message: result.result.message };
    }
  } catch (error) {
    console.error(\`❌ 第 \${batchNumber} 批上传异常:\`, error);
    return { success: false, batchNumber, message: error.message };
  }
}

// 上传所有批次
async function uploadAllBatches() {
  console.log(\`开始上传所有 \${ALL_BATCHES.length} 批数据...\`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 1; i <= ALL_BATCHES.length; i++) {
    console.log(\`\\n--- 上传第 \${i}/\${ALL_BATCHES.length} 批 ---\`);
    
    const result = await uploadBatch(i);
    
    if (result.success) {
      successCount++;
    } else {
      failCount++;
    }
    
    // 每批之间暂停1秒
    if (i < ALL_BATCHES.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(\`\\n📊 上传完成统计:\`);
  console.log(\`✅ 成功: \${successCount} 批\`);
  console.log(\`❌ 失败: \${failCount} 批\`);
  console.log(\`📁 总文件数: \${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)}\`);
}

// 检查上传状态
async function checkUploadStatus() {
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: {
        action: 'count'
      }
    });
    
    if (result.result.success) {
      console.log(\`📊 当前云端知识库统计:\`);
      console.log(\`📁 总记录数: \${result.result.total}\`);
      console.log(\`🎯 预期记录数: \${ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)}\`);
      
      if (result.result.total === ALL_BATCHES.reduce((sum, batch) => sum + batch.length, 0)) {
        console.log(\`✅ 数据完整，上传成功！\`);
      } else {
        console.log(\`⚠️ 数据不完整，可能需要重新上传\`);
      }
    } else {
      console.error('检查状态失败:', result.result.message);
    }
  } catch (error) {
    console.error('检查状态异常:', error);
  }
}

console.log('知识库数据加载完成！');
console.log('可用命令:');
console.log('- uploadBatch(n): 上传第n批数据');
console.log('- uploadAllBatches(): 上传所有批次');
console.log('- checkUploadStatus(): 检查上传状态');
`;

  // 写入文件
  fs.writeFileSync(OUTPUT_FILE, jsContent, 'utf-8');
  
  console.log(`\n✅ 干净的知识库数据生成完成！`);
  console.log(`📁 输出文件: ${OUTPUT_FILE}`);
  console.log(`📄 文件大小: ${Math.round(jsContent.length / 1024)} KB`);
  console.log(`📊 数据统计:`);
  console.log(`  - 总文件数: ${allData.length}`);
  console.log(`  - 总批次数: ${batches.length}`);
  console.log(`  - 每批大小: ${BATCH_SIZE} 个文件`);
}

// 运行脚本
if (require.main === module) {
  generateKnowledgeData();
}

module.exports = { generateKnowledgeData };
