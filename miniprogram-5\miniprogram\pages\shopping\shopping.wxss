/* pages/shopping/shopping.wxss */

/* 通用变量定义 */
:root {
  --ancient-paper: #f8f6f0;
  --paper-white: #ffffff;
  --ink-black: #1a1a1a;
  --ink-gray: #666666;
  --ancient-gold: #d4af37;
}

/* 通用动画效果 */
.ink-ripple {
  transition: all 0.3s ease;
}

.ink-ripple:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.container {
  min-height: 100vh;
  background: var(--ancient-paper);
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  background: linear-gradient(135deg, var(--ink-black) 0%, #2a2a2a 100%);
  color: white;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  font-family: 'STSong', '华文宋体', serif;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  font-family: 'STKaiti', '楷体', serif;
}

/* 用户登录提示 */
.user-section {
  margin: 30rpx;
}

.login-tip {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  border: 2rpx solid var(--ancient-gold);
}

.tip-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.tip-text {
  color: var(--ink-gray);
  font-size: 28rpx;
  font-family: 'STKaiti', '楷体', serif;
}

/* 商品分类 */
.categories-section {
  margin: 30rpx;
}

.section-title {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 20rpx;
  font-family: 'STSong', '华文宋体', serif;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.category-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  border: 1rpx solid rgba(212, 175, 55, 0.2);
}

.category-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.category-name {
  font-size: 26rpx;
  color: var(--ink-black);
  font-weight: 500;
  margin-bottom: 8rpx;
  font-family: 'STSong', '华文宋体', serif;
}

.category-desc {
  font-size: 20rpx;
  color: var(--ink-gray);
  line-height: 1.4;
  font-family: 'STKaiti', '楷体', serif;
}

/* 推荐商品 */
.products-section {
  margin: 30rpx;
}

.title-text {
  font-size: 32rpx;
  color: var(--ink-black);
  font-weight: 600;
  font-family: 'STSong', '华文宋体', serif;
}

.title-subtitle {
  font-size: 24rpx;
  color: var(--ink-gray);
  margin-top: 4rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.products-list {
  margin-top: 20rpx;
}

.product-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(26, 26, 26, 0.06);
  border: 1rpx solid rgba(212, 175, 55, 0.2);
}

.product-image {
  position: relative;
  height: 200rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-img {
  width: 100%;
  height: 100%;
}

.product-badge {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  background: var(--ancient-gold);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  color: var(--ink-black);
  font-weight: 600;
  margin-bottom: 8rpx;
  font-family: 'STSong', '华文宋体', serif;
}

.product-desc {
  font-size: 24rpx;
  color: var(--ink-gray);
  line-height: 1.4;
  margin-bottom: 8rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.product-suitable {
  font-size: 22rpx;
  color: var(--ancient-gold);
  background: rgba(212, 175, 55, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
  margin-bottom: 16rpx;
  font-family: 'STKaiti', '楷体', serif;
}

.product-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  font-size: 32rpx;
  color: var(--ancient-gold);
  font-weight: 600;
  font-family: 'STSong', '华文宋体', serif;
}

.product-actions {
  display: flex;
  gap: 12rpx;
}

.btn-detail {
  padding: 12rpx 20rpx;
  border: 1rpx solid var(--ink-gray);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: var(--ink-gray);
  font-family: 'STKaiti', '楷体', serif;
}

.btn-add-cart {
  padding: 12rpx 20rpx;
  background: var(--ancient-gold);
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-family: 'STKaiti', '楷体', serif;
}

/* 购物车悬浮按钮 */
.cart-float {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  background: var(--ancient-gold);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
  z-index: 100;
}

.cart-icon {
  font-size: 32rpx;
  color: white;
}

.cart-count {
  background: white;
  color: var(--ancient-gold);
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  min-width: 32rpx;
  text-align: center;
}

.cart-total {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
  font-family: 'STSong', '华文宋体', serif;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--ink-gray);
  margin-bottom: 12rpx;
  font-family: 'STSong', '华文宋体', serif;
}

.empty-desc {
  font-size: 24rpx;
  color: var(--ink-gray);
  opacity: 0.7;
  font-family: 'STKaiti', '楷体', serif;
}
