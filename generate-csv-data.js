// 生成CSV格式的知识库数据，用于云开发控制台导入
const fs = require('fs');
const path = require('path');

const KNOWLEDGE_DIR = './knowledge';
const OUTPUT_DIR = './csv-data';
const FILES_PER_CSV = 50; // 每个CSV文件包含50个知识库文件

/**
 * 解析文件名获取书籍信息
 */
function parseFilename(filename) {
  const name = path.basename(filename, '.txt');
  
  let title = name;
  let author = '';
  let dynasty = '';
  let category = 'other';
  
  // 匹配模式：书名-朝代-作者
  const patterns = [
    /^(.+)-(.+)-(.+)$/, // 书名-朝代-作者
    /^(.+)-(.+)$/, // 书名-作者 或 作者-书名
    /^(\d+)\.(.+)$/, // 编号.书名
    /^(.+)$/  // 仅书名
  ];
  
  // 尝试匹配不同模式
  for (const pattern of patterns) {
    const match = name.match(pattern);
    if (match) {
      if (match.length === 4) {
        // 书名-朝代-作者
        title = match[1];
        dynasty = match[2];
        author = match[3];
      } else if (match.length === 3) {
        if (/^\d+$/.test(match[1])) {
          // 编号.书名
          title = match[2];
        } else {
          // 判断是 书名-作者 还是 作者-书名
          if (match[2].includes('宋') || match[2].includes('唐') || 
              match[2].includes('明') || match[2].includes('清') ||
              match[2].includes('元') || match[2].includes('汉')) {
            title = match[1];
            dynasty = match[2];
          } else if (match[1].includes('宋') || match[1].includes('唐') || 
                     match[1].includes('明') || match[1].includes('清') ||
                     match[1].includes('元') || match[1].includes('汉')) {
            dynasty = match[1];
            title = match[2];
          } else {
            // 默认第一个是书名，第二个是作者
            title = match[1];
            author = match[2];
          }
        }
      }
      break;
    }
  }
  
  // 根据书名判断分类
  if (title.includes('周易') || title.includes('易经') || title.includes('易传')) {
    category = 'zhouyi';
  } else if (title.includes('梅花易数')) {
    category = 'meihua';
  } else if (title.includes('紫微斗数') || title.includes('紫薇')) {
    category = 'ziwei';
  } else if (title.includes('八字') || title.includes('子平')) {
    category = 'bazi';
  } else if (title.includes('六壬') || title.includes('奇门')) {
    category = 'liuren';
  }
  
  return {
    title: title.trim(),
    author: author.trim(),
    dynasty: dynasty.trim(),
    category
  };
}

/**
 * 提取关键词
 */
function extractKeywords(content) {
  const keywords = [];
  
  // 易经相关关键词
  const yijingKeywords = ['乾', '坤', '震', '巽', '坎', '离', '艮', '兑', '太极', '阴阳', '八卦', '六十四卦'];
  // 紫微斗数关键词
  const ziweiKeywords = ['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'];
  // 梅花易数关键词
  const meihuaKeywords = ['梅花易数', '先天', '后天', '体卦', '用卦', '变卦', '互卦'];
  
  const allKeywords = [...yijingKeywords, ...ziweiKeywords, ...meihuaKeywords];
  
  allKeywords.forEach(keyword => {
    if (content.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return keywords.slice(0, 10); // 最多返回10个关键词
}

/**
 * 转义CSV字段
 */
function escapeCSV(text) {
  if (!text) return '';
  
  // 替换换行符为空格
  text = text.replace(/[\r\n]+/g, ' ');
  
  // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
  if (text.includes(',') || text.includes('"') || text.includes('\n')) {
    text = '"' + text.replace(/"/g, '""') + '"';
  }
  
  return text;
}

/**
 * 生成CSV数据
 */
function generateCSVData() {
  console.log('开始生成CSV数据...');
  
  if (!fs.existsSync(KNOWLEDGE_DIR)) {
    console.error('knowledge目录不存在！');
    return;
  }
  
  // 创建输出目录
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR);
  }
  
  const files = fs.readdirSync(KNOWLEDGE_DIR)
    .filter(file => file.endsWith('.txt'))
    .sort();
  
  console.log(`找到 ${files.length} 个txt文件`);
  
  let allData = [];
  let processedCount = 0;
  
  files.forEach((file, index) => {
    try {
      const filePath = path.join(KNOWLEDGE_DIR, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      
      if (content.trim().length === 0) {
        console.log(`跳过空文件: ${file}`);
        return;
      }
      
      const fileInfo = parseFilename(file);
      const keywords = extractKeywords(content);
      
      // 限制内容长度，避免CSV过大
      const truncatedContent = content.length > 10000 
        ? content.substring(0, 10000) + '...[内容已截断]'
        : content;
      
      const fileData = {
        filePath: file,
        title: fileInfo.title,
        author: fileInfo.author,
        dynasty: fileInfo.dynasty,
        category: fileInfo.category,
        keywords: keywords.join(';'),
        content: truncatedContent,
        file_size: content.length,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      allData.push(fileData);
      processedCount++;
      
      if (processedCount % 10 === 0) {
        console.log(`已处理 ${processedCount}/${files.length} 个文件...`);
      }
      
    } catch (error) {
      console.error(`处理文件 ${file} 时出错:`, error.message);
    }
  });
  
  console.log(`总共处理了 ${processedCount} 个文件`);
  
  // 分割成多个CSV文件
  const totalFiles = Math.ceil(allData.length / FILES_PER_CSV);
  
  for (let i = 0; i < totalFiles; i++) {
    const startIndex = i * FILES_PER_CSV;
    const endIndex = Math.min(startIndex + FILES_PER_CSV, allData.length);
    const batchData = allData.slice(startIndex, endIndex);
    
    // 生成CSV内容
    const csvHeader = 'filePath,title,author,dynasty,category,keywords,content,file_size,created_at,updated_at\n';
    
    const csvRows = batchData.map(item => {
      return [
        escapeCSV(item.filePath),
        escapeCSV(item.title),
        escapeCSV(item.author),
        escapeCSV(item.dynasty),
        escapeCSV(item.category),
        escapeCSV(item.keywords),
        escapeCSV(item.content),
        item.file_size,
        escapeCSV(item.created_at),
        escapeCSV(item.updated_at)
      ].join(',');
    });
    
    const csvContent = csvHeader + csvRows.join('\n');
    
    // 写入文件
    const fileName = `knowledge_batch_${String(i + 1).padStart(2, '0')}.csv`;
    const filePath = path.join(OUTPUT_DIR, fileName);
    
    fs.writeFileSync(filePath, csvContent, 'utf-8');
    console.log(`✅ 生成: ${fileName} (${batchData.length} 条记录)`);
  }
  
  // 生成使用说明
  const instructionContent = `# CSV数据导入说明

## 📁 文件说明
- 总共生成 ${totalFiles} 个CSV文件
- 每个文件包含最多 ${FILES_PER_CSV} 条记录
- 总记录数: ${allData.length}

## 🚀 导入步骤

### 1. 打开微信云开发控制台
- 访问 https://console.cloud.tencent.com/tcb
- 选择您的环境: cloud1-0g3xctv612d8f755

### 2. 进入数据库管理
- 点击左侧菜单 "数据库"
- 创建集合 "knowledge_base"（如果不存在）

### 3. 导入CSV文件
- 点击 "导入" 按钮
- 选择 "CSV格式"
- 依次上传每个CSV文件：
${Array.from({length: totalFiles}, (_, i) => `  - knowledge_batch_${String(i + 1).padStart(2, '0')}.csv`).join('\n')}

### 4. 字段映射
确保字段映射正确：
- filePath -> filePath (字符串)
- title -> title (字符串)
- author -> author (字符串)
- dynasty -> dynasty (字符串)
- category -> category (字符串)
- keywords -> keywords (字符串)
- content -> content (字符串)
- file_size -> file_size (数字)
- created_at -> created_at (日期)
- updated_at -> updated_at (日期)

## ⚠️ 注意事项
1. 每次只能导入一个CSV文件
2. 导入前请确保数据库集合已创建
3. 如果导入失败，请检查CSV格式是否正确
4. 建议先导入一个小文件测试

## 📊 数据统计
- 总文件数: ${allData.length}
- CSV文件数: ${totalFiles}
- 每个CSV最大记录数: ${FILES_PER_CSV}
`;

  fs.writeFileSync(path.join(OUTPUT_DIR, 'README.md'), instructionContent, 'utf-8');
  
  console.log(`\n✅ CSV数据生成完成！`);
  console.log(`📁 输出目录: ${OUTPUT_DIR}`);
  console.log(`📄 CSV文件数: ${totalFiles}`);
  console.log(`📖 使用说明: ${OUTPUT_DIR}/README.md`);
}

// 运行脚本
if (require.main === module) {
  generateCSVData();
}

module.exports = { generateCSVData };
